import { useState, useEffect } from 'react';
import { ArrowLeft, Cpu, HardDrive, Zap, FileType, Save, RotateCcw } from 'lucide-react';
import * as Slider from '@radix-ui/react-slider';
import * as Tabs from '@radix-ui/react-tabs';
import * as Checkbox from '@radix-ui/react-checkbox';
import { CheckIcon } from 'lucide-react';
import type { PerformanceSettings, FileTypeSettings } from './types';

interface SettingsScreenProps {
  performanceSettings: PerformanceSettings;
  fileTypeSettings: FileTypeSettings;
  onPerformanceChange: (settings: PerformanceSettings) => void;
  onFileTypeChange: (settings: FileTypeSettings) => void;
  onBack: () => void;
}

interface ResourceUsage {
  cpu: number;
  memory: number;
  gpu: number;
}

export default function SettingsScreen({
  performanceSettings,
  fileTypeSettings,
  onPerformanceChange,
  onFileTypeChange,
  onBack,
}: SettingsScreenProps) {
  const [localPerformanceSettings, setLocalPerformanceSettings] = useState(performanceSettings);
  const [localFileTypeSettings, setLocalFileTypeSettings] = useState(fileTypeSettings);
  const [resourceUsage, setResourceUsage] = useState<ResourceUsage>({ cpu: 0, memory: 0, gpu: 0 });
  const [hasChanges, setHasChanges] = useState(false);

  // Simulate real-time resource monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      setResourceUsage({
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        gpu: Math.random() * 100,
      });
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Track changes
  useEffect(() => {
    const perfChanged = JSON.stringify(localPerformanceSettings) !== JSON.stringify(performanceSettings);
    const fileChanged = JSON.stringify(localFileTypeSettings) !== JSON.stringify(fileTypeSettings);
    setHasChanges(perfChanged || fileChanged);
  }, [localPerformanceSettings, localFileTypeSettings, performanceSettings, fileTypeSettings]);

  const handlePerformanceChange = (key: keyof PerformanceSettings, value: number) => {
    setLocalPerformanceSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleFileTypeToggle = (category: string, enabled: boolean) => {
    setLocalFileTypeSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        enabled,
      },
    }));
  };

  const handleExtensionChange = (category: string, extensions: string) => {
    const extensionList = extensions.split(',').map(ext => ext.trim().toLowerCase()).filter(Boolean);
    setLocalFileTypeSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        extensions: extensionList,
      },
    }));
  };

  const handleSave = () => {
    onPerformanceChange(localPerformanceSettings);
    onFileTypeChange(localFileTypeSettings);
    setHasChanges(false);
  };

  const handleReset = () => {
    setLocalPerformanceSettings(performanceSettings);
    setLocalFileTypeSettings(fileTypeSettings);
    setHasChanges(false);
  };

  const formatMemory = (mb: number): string => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  const getUsageColor = (usage: number): string => {
    if (usage >= 80) return '#ef4444';
    if (usage >= 60) return '#f59e0b';
    return '#10b981';
  };

  return (
    <div className="settings-screen">
      <div className="screen-header">
        <button className="back-button" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
          Back
        </button>
        <h1 className="screen-title">Settings</h1>
        {hasChanges && (
          <div className="settings-actions">
            <button className="reset-button" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
              Reset
            </button>
            <button className="save-button" onClick={handleSave}>
              <Save className="w-4 h-4" />
              Save Changes
            </button>
          </div>
        )}
      </div>

      <div className="settings-content">
        <Tabs.Root defaultValue="performance" className="settings-tabs">
          <Tabs.List className="tabs-list">
            <Tabs.Trigger value="performance" className="tab-trigger">
              <Cpu className="w-4 h-4" />
              Performance
            </Tabs.Trigger>
            <Tabs.Trigger value="filetypes" className="tab-trigger">
              <FileType className="w-4 h-4" />
              File Types
            </Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="performance" className="tab-content">
            <div className="settings-section">
              <h2 className="section-title">Performance Limits</h2>
              <p className="section-description">
                Configure resource usage limits to optimize scanning performance while maintaining system responsiveness.
              </p>

              <div className="performance-controls">
                {/* CPU Limit */}
                <div className="control-group">
                  <div className="control-header">
                    <div className="control-label">
                      <Cpu className="w-5 h-5 text-blue-600" />
                      <span>CPU Usage Limit</span>
                    </div>
                    <span className="control-value">{localPerformanceSettings.cpu_limit}%</span>
                  </div>
                  <Slider.Root
                    className="slider-root"
                    value={[localPerformanceSettings.cpu_limit]}
                    onValueChange={([value]) => handlePerformanceChange('cpu_limit', value)}
                    max={100}
                    min={10}
                    step={5}
                  >
                    <Slider.Track className="slider-track">
                      <Slider.Range className="slider-range" />
                    </Slider.Track>
                    <Slider.Thumb className="slider-thumb" />
                  </Slider.Root>
                </div>

                {/* Memory Limit */}
                <div className="control-group">
                  <div className="control-header">
                    <div className="control-label">
                      <HardDrive className="w-5 h-5 text-green-600" />
                      <span>Memory Usage Limit</span>
                    </div>
                    <span className="control-value">{formatMemory(localPerformanceSettings.memory_limit)}</span>
                  </div>
                  <Slider.Root
                    className="slider-root"
                    value={[localPerformanceSettings.memory_limit]}
                    onValueChange={([value]) => handlePerformanceChange('memory_limit', value)}
                    max={8192}
                    min={512}
                    step={256}
                  >
                    <Slider.Track className="slider-track">
                      <Slider.Range className="slider-range" />
                    </Slider.Track>
                    <Slider.Thumb className="slider-thumb" />
                  </Slider.Root>
                </div>

                {/* GPU Limit */}
                <div className="control-group">
                  <div className="control-header">
                    <div className="control-label">
                      <Zap className="w-5 h-5 text-purple-600" />
                      <span>GPU Usage Limit</span>
                    </div>
                    <span className="control-value">{localPerformanceSettings.gpu_limit}%</span>
                  </div>
                  <Slider.Root
                    className="slider-root"
                    value={[localPerformanceSettings.gpu_limit]}
                    onValueChange={([value]) => handlePerformanceChange('gpu_limit', value)}
                    max={100}
                    min={0}
                    step={10}
                  >
                    <Slider.Track className="slider-track">
                      <Slider.Range className="slider-range" />
                    </Slider.Track>
                    <Slider.Thumb className="slider-thumb" />
                  </Slider.Root>
                </div>
              </div>
            </div>

            {/* Real-time Resource Usage */}
            <div className="settings-section">
              <h2 className="section-title">Current Resource Usage</h2>
              <div className="resource-monitors">
                <div className="resource-monitor">
                  <div className="monitor-header">
                    <Cpu className="w-4 h-4" />
                    <span>CPU</span>
                  </div>
                  <div className="monitor-bar">
                    <div 
                      className="monitor-fill"
                      style={{ 
                        width: `${resourceUsage.cpu}%`,
                        backgroundColor: getUsageColor(resourceUsage.cpu)
                      }}
                    />
                  </div>
                  <span className="monitor-value">{resourceUsage.cpu.toFixed(1)}%</span>
                </div>

                <div className="resource-monitor">
                  <div className="monitor-header">
                    <HardDrive className="w-4 h-4" />
                    <span>Memory</span>
                  </div>
                  <div className="monitor-bar">
                    <div 
                      className="monitor-fill"
                      style={{ 
                        width: `${resourceUsage.memory}%`,
                        backgroundColor: getUsageColor(resourceUsage.memory)
                      }}
                    />
                  </div>
                  <span className="monitor-value">{resourceUsage.memory.toFixed(1)}%</span>
                </div>

                <div className="resource-monitor">
                  <div className="monitor-header">
                    <Zap className="w-4 h-4" />
                    <span>GPU</span>
                  </div>
                  <div className="monitor-bar">
                    <div 
                      className="monitor-fill"
                      style={{ 
                        width: `${resourceUsage.gpu}%`,
                        backgroundColor: getUsageColor(resourceUsage.gpu)
                      }}
                    />
                  </div>
                  <span className="monitor-value">{resourceUsage.gpu.toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </Tabs.Content>

          <Tabs.Content value="filetypes" className="tab-content">
            <div className="settings-section">
              <h2 className="section-title">File Type Configuration</h2>
              <p className="section-description">
                Configure which file types to include in scans and customize file extension patterns.
              </p>

              <div className="filetype-controls">
                {Object.entries(localFileTypeSettings).map(([category, config]) => (
                  <div key={category} className="filetype-group">
                    <div className="filetype-header">
                      <Checkbox.Root
                        className="checkbox-root"
                        checked={config.enabled}
                        onCheckedChange={(checked) => handleFileTypeToggle(category, checked as boolean)}
                      >
                        <Checkbox.Indicator className="checkbox-indicator">
                          <CheckIcon className="w-4 h-4" />
                        </Checkbox.Indicator>
                      </Checkbox.Root>
                      <h3 className="filetype-title">{category.charAt(0).toUpperCase() + category.slice(1)}</h3>
                    </div>
                    
                    <div className="filetype-content">
                      <label className="extension-label">
                        File Extensions (comma-separated):
                      </label>
                      <input
                        type="text"
                        className="extension-input"
                        value={config.extensions.join(', ')}
                        onChange={(e) => handleExtensionChange(category, e.target.value)}
                        disabled={!config.enabled}
                        placeholder="e.g., pdf, doc, docx, txt"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Tabs.Content>
        </Tabs.Root>
      </div>
    </div>
  );
}
