/// Comprehensive tests for cryptocurrency wallet file detection
/// 
/// This test suite validates the wallet detection capabilities including:
/// - File signature detection for major wallet formats
/// - Content analysis for encrypted wallets
/// - BIP39 mnemonic seed phrase detection
/// - Hardware wallet recovery information detection
/// - Performance validation and false positive reduction

use tauri_appprivacy_ai_lib::security::cryptocurrency::{
    WalletDetector, WalletType, WalletDetectionMethod
};

#[cfg(test)]
mod wallet_detection_tests {
    use super::*;

    #[test]
    fn test_wallet_detector_creation() {
        let detector = WalletDetector::new();
        
        assert!(detector.get_signature_count() > 0, "No wallet signatures loaded");
        assert!(detector.get_supported_wallet_types().len() > 0, "No wallet types supported");
        
        println!("✅ WalletDetector created with {} signatures", detector.get_signature_count());
        println!("   Supported wallet types: {:?}", detector.get_supported_wallet_types());
    }

    #[test]
    fn test_bitcoin_core_wallet_detection() {
        let detector = WalletDetector::new();
        
        // Test Bitcoin Core wallet.dat file detection by path
        let result = detector.detect_by_path("C:\\Users\\<USER>\\AppData\\Roaming\\Bitcoin\\wallet.dat");
        assert!(result.is_some(), "Should detect Bitcoin Core wallet.dat by path");
        
        let detection = result.unwrap();
        assert_eq!(detection.wallet_type, WalletType::BitcoinCore);
        assert_eq!(detection.detection_method, WalletDetectionMethod::FileExtension);
        assert!(detection.confidence > 0.5, "Should have reasonable confidence");
        
        println!("✅ Bitcoin Core wallet.dat detected (confidence: {:.2})", detection.confidence);
    }

    #[test]
    fn test_electrum_wallet_content_detection() {
        let detector = WalletDetector::new();
        
        // Simulated Electrum wallet file content
        let electrum_content = r#"
        {
            "encrypted": true,
            "seed_version": 17,
            "use_encryption": true,
            "keystore": {
                "type": "bip32",
                "xpub": "xpub..."
            },
            "addresses": {
                "receiving": [],
                "change": []
            }
        }
        "#;
        
        let result = detector.detect_by_content(
            "/home/<USER>/.electrum/wallets/default_wallet", 
            electrum_content.as_bytes()
        ).expect("Content analysis should not fail");
        
        assert!(result.is_some(), "Should detect Electrum wallet by content");
        
        let detection = result.unwrap();
        assert_eq!(detection.wallet_type, WalletType::Electrum);
        assert!(detection.confidence > 0.8, "Should have high confidence for content match");
        assert!(detection.is_encrypted, "Should detect encryption");
        
        println!("✅ Electrum wallet detected by content (confidence: {:.2})", detection.confidence);
    }

    #[test]
    fn test_ethereum_keystore_detection() {
        let detector = WalletDetector::new();
        
        // Simulated Ethereum keystore file content (Web3 Secret Storage)
        let keystore_content = r#"
        {
            "address": "d8da6bf26964af9d7eed9e03e53415d37aa96045",
            "crypto": {
                "cipher": "aes-128-ctr",
                "ciphertext": "...",
                "cipherparams": {
                    "iv": "..."
                },
                "kdf": "scrypt",
                "kdfparams": {
                    "dklen": 32,
                    "n": 262144,
                    "p": 8,
                    "r": 1,
                    "salt": "..."
                },
                "mac": "..."
            },
            "id": "...",
            "version": 3
        }
        "#;
        
        let result = detector.detect_by_content(
            "/home/<USER>/keystore/UTC--2023-01-01T00-00-00.000Z--d8da6bf26964af9d7eed9e03e53415d37aa96045",
            keystore_content.as_bytes()
        ).expect("Content analysis should not fail");
        
        assert!(result.is_some(), "Should detect Ethereum keystore by content");
        
        let detection = result.unwrap();
        assert_eq!(detection.wallet_type, WalletType::EthereumKeystore);
        assert!(detection.confidence > 0.8, "Should have high confidence for keystore");
        assert!(detection.is_encrypted, "Keystore should be detected as encrypted");
        
        println!("✅ Ethereum keystore detected (confidence: {:.2})", detection.confidence);
    }

    #[test]
    fn test_bip39_mnemonic_detection() {
        let detector = WalletDetector::new();
        
        // Test various BIP39 mnemonic seed phrases
        let test_cases = vec![
            // 12-word seed
            "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about",
            // 24-word seed (partial, using common BIP39 words)
            "abandon ability able about above absent absorb abstract absurd abuse access accident account accuse achieve acid acoustic acquire across act action actor actress actual",
            // Mixed case and extra text
            "My wallet seed phrase: abandon ability able about above absent absorb abstract absurd abuse access accident - keep this safe!",
        ];
        
        for (i, test_text) in test_cases.iter().enumerate() {
            let results = detector.detect_mnemonic_seeds(test_text);
            
            assert!(!results.is_empty(), "Should detect mnemonic in test case {}: {}", i + 1, test_text);
            
            for result in &results {
                assert_eq!(result.wallet_type, WalletType::BIP39Mnemonic);
                assert_eq!(result.detection_method, WalletDetectionMethod::RecoveryPhrase);
                assert!(result.confidence > 0.7, "Should have high confidence for valid mnemonic");
                
                println!("✅ BIP39 mnemonic detected (confidence: {:.2}, words: {})", 
                         result.confidence, 
                         result.additional_info.get("word_count").unwrap_or(&"unknown".to_string()));
            }
        }
    }

    #[test]
    fn test_hardware_wallet_backup_detection() {
        let detector = WalletDetector::new();
        
        // Test hardware wallet backup file detection
        let backup_paths = vec![
            "/home/<USER>/Documents/trezor_backup.backup",
            "C:\\Users\\<USER>\\Desktop\\ledger_recovery.bak",
            "/Users/<USER>/coldcard_backup.hww",
        ];
        
        for path in backup_paths {
            let result = detector.detect_by_path(path);
            assert!(result.is_some(), "Should detect hardware wallet backup: {}", path);
            
            let detection = result.unwrap();
            assert_eq!(detection.wallet_type, WalletType::HardwareBackup);
            assert_eq!(detection.detection_method, WalletDetectionMethod::FileExtension);
            
            println!("✅ Hardware wallet backup detected: {} (confidence: {:.2})", path, detection.confidence);
        }
    }

    #[test]
    fn test_monero_wallet_detection() {
        let detector = WalletDetector::new();
        
        // Simulated Monero wallet file content with magic bytes
        let monero_content = vec![
            0x01, 0x16, 0xF1, 0xA0, // Monero magic bytes
            0x00, 0x00, 0x00, 0x01, // Version
            // Additional wallet data...
            b'm', b'o', b'n', b'e', b'r', b'o', // "monero" string
        ];
        
        let result = detector.detect_by_content(
            "/home/<USER>/.bitmonero/wallet",
            &monero_content
        ).expect("Content analysis should not fail");
        
        assert!(result.is_some(), "Should detect Monero wallet by magic bytes");
        
        let detection = result.unwrap();
        assert_eq!(detection.wallet_type, WalletType::MoneroWallet);
        assert_eq!(detection.detection_method, WalletDetectionMethod::MagicBytes);
        assert!(detection.confidence > 0.5, "Should have reasonable confidence for magic bytes");
        
        println!("✅ Monero wallet detected by magic bytes (confidence: {:.2})", detection.confidence);
    }

    #[test]
    fn test_false_positive_reduction() {
        let detector = WalletDetector::new();
        
        // Test files that should NOT be detected as wallets
        let false_positive_cases = vec![
            ("regular_file.dat", "This is just a regular data file with no wallet content"),
            ("document.json", r#"{"title": "My Document", "content": "Regular JSON file"}"#),
            ("random.wallet", "This file has wallet extension but no wallet content"),
            ("text.txt", "Just some random text with no cryptocurrency content"),
        ];
        
        for (filename, content) in false_positive_cases {
            // Test path-based detection
            let path_result = detector.detect_by_path(filename);
            
            // Test content-based detection
            let content_result = detector.detect_by_content(filename, content.as_bytes())
                .expect("Content analysis should not fail");
            
            // Path detection might trigger on extension, but should have low confidence
            if let Some(detection) = path_result {
                assert!(detection.confidence < 0.8, 
                       "False positive with high confidence: {} (confidence: {:.2})", 
                       filename, detection.confidence);
            }
            
            // Content detection should not trigger for non-wallet content
            if let Some(detection) = content_result {
                assert!(detection.confidence < 0.6, 
                       "Content false positive: {} (confidence: {:.2})", 
                       filename, detection.confidence);
            }
            
            println!("✅ False positive test passed: {}", filename);
        }
    }

    #[test]
    fn test_performance_validation() {
        let detector = WalletDetector::new();
        
        // Create large content for performance testing
        let large_content = "random data ".repeat(1000);
        let large_content_bytes = large_content.as_bytes();
        
        // Test path detection performance
        let start = std::time::Instant::now();
        for i in 0..100 {
            let _ = detector.detect_by_path(&format!("test_file_{}.dat", i));
        }
        let path_time = start.elapsed();
        
        // Test content detection performance
        let start = std::time::Instant::now();
        for i in 0..10 {
            let _ = detector.detect_by_content(&format!("test_file_{}.dat", i), large_content_bytes)
                .expect("Content analysis should not fail");
        }
        let content_time = start.elapsed();
        
        // Test mnemonic detection performance
        let mnemonic_text = "abandon ability able about above absent absorb abstract absurd abuse access accident ".repeat(10);
        let start = std::time::Instant::now();
        for _ in 0..10 {
            let _ = detector.detect_mnemonic_seeds(&mnemonic_text);
        }
        let mnemonic_time = start.elapsed();
        
        // Validate performance targets
        assert!(path_time.as_millis() < 100, "Path detection took {}ms, should be <100ms", path_time.as_millis());
        assert!(content_time.as_millis() < 500, "Content detection took {}ms, should be <500ms", content_time.as_millis());
        assert!(mnemonic_time.as_millis() < 200, "Mnemonic detection took {}ms, should be <200ms", mnemonic_time.as_millis());
        
        println!("✅ Performance validation passed:");
        println!("   - Path detection: {}ms (100 files)", path_time.as_millis());
        println!("   - Content detection: {}ms (10 files)", content_time.as_millis());
        println!("   - Mnemonic detection: {}ms (10 texts)", mnemonic_time.as_millis());
    }

    #[test]
    fn test_comprehensive_wallet_coverage() {
        let detector = WalletDetector::new();
        
        // Test that all major wallet types are supported
        let supported_types = detector.get_supported_wallet_types();
        
        // Check for essential wallet types
        assert!(supported_types.contains(&WalletType::BitcoinCore), "Should support Bitcoin Core");
        assert!(supported_types.contains(&WalletType::Electrum), "Should support Electrum");
        assert!(supported_types.contains(&WalletType::EthereumKeystore), "Should support Ethereum");
        assert!(supported_types.contains(&WalletType::MoneroWallet), "Should support Monero");
        assert!(supported_types.contains(&WalletType::BIP39Mnemonic), "Should support BIP39");
        assert!(supported_types.contains(&WalletType::HardwareBackup), "Should support hardware wallets");
        
        println!("✅ Comprehensive wallet coverage validated:");
        for wallet_type in &supported_types {
            println!("   - {:?}", wallet_type);
        }
        
        assert!(supported_types.len() >= 6, "Should support at least 6 wallet types");
    }

    #[test]
    fn test_encryption_detection() {
        let detector = WalletDetector::new();
        
        // Test encrypted content detection
        let encrypted_content = r#"
        {
            "encrypted": true,
            "cipher": "aes-256-cbc",
            "ciphertext": "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K14=",
            "salt": "...",
            "iv": "...",
            "kdf": "pbkdf2"
        }
        "#;
        
        let result = detector.detect_by_content("test.wallet", encrypted_content.as_bytes())
            .expect("Content analysis should not fail");
        
        if let Some(detection) = result {
            assert!(detection.is_encrypted, "Should detect content as encrypted");
            println!("✅ Encryption detection working: {}", detection.is_encrypted);
        }
    }
}
