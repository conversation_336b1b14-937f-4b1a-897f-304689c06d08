/// Comprehensive tests for Custom Pattern Management System
/// 
/// This test suite validates the custom pattern management capabilities including:
/// - Custom pattern creation, validation, and management
/// - Pattern template system and usage
/// - Integration with existing cryptocurrency detection
/// - Performance monitoring and safety validation
/// - Import/export functionality
/// - Real-time pattern testing and validation

use tauri_appprivacy_ai_lib::security::{
    CustomPatternManager, PatternTemplateManager, IntegratedDetector,
    CustomPattern, CustomPatternType, CustomSeverity, PatternTestCase,
    TemplateDifficulty, PatternValidationConfig, PatternPerformanceStats
};

#[cfg(test)]
mod custom_pattern_tests {
    use super::*;

    #[test]
    fn test_custom_pattern_creation_and_validation() {
        let mut manager = CustomPatternManager::new();
        
        // Create a test custom pattern
        let test_pattern = CustomPattern {
            id: String::new(), // Will be generated
            name: "Test Bitcoin Testnet".to_string(),
            regex_pattern: r"\b[mn2][a-km-zA-HJ-NP-Z1-9]{25,34}\b".to_string(),
            pattern_type: CustomPatternType::Address,
            severity: CustomSeverity::Medium,
            description: "Detects Bitcoin testnet addresses".to_string(),
            test_cases: vec![
                PatternTestCase {
                    text: "mipcBbFg9gMiCh81Kj8tqqdgoZub1ZJRfn".to_string(),
                    should_match: true,
                    description: "Valid testnet address".to_string(),
                },
                PatternTestCase {
                    text: "**********************************".to_string(),
                    should_match: false,
                    description: "Mainnet address should not match".to_string(),
                },
            ],
            is_enabled: true,
            created_at: String::new(),
            updated_at: String::new(),
            performance_stats: PatternPerformanceStats {
                total_executions: 0,
                total_time_ms: 0,
                average_time_ms: 0.0,
                max_time_ms: 0,
                matches_found: 0,
                last_execution: None,
            },
            validation_config: PatternValidationConfig {
                requires_validation: true,
                confidence_threshold: 0.8,
                max_execution_time_ms: 50,
                enable_performance_monitoring: true,
            },
        };
        
        // Add pattern to manager
        let pattern_id = manager.add_pattern(test_pattern).expect("Failed to add pattern");
        assert!(!pattern_id.is_empty(), "Pattern ID should be generated");
        
        // Verify pattern was added
        let retrieved_pattern = manager.get_pattern(&pattern_id).expect("Pattern should exist");
        assert_eq!(retrieved_pattern.name, "Test Bitcoin Testnet");
        assert_eq!(retrieved_pattern.pattern_type, CustomPatternType::Address);
        assert_eq!(retrieved_pattern.severity, CustomSeverity::Medium);
        assert!(retrieved_pattern.is_enabled);
        
        println!("✅ Custom pattern created and validated: {}", pattern_id);
    }

    #[test]
    fn test_pattern_testing_and_performance() {
        let mut manager = CustomPatternManager::new();
        
        // Create a simple test pattern
        let test_pattern = CustomPattern {
            id: String::new(),
            name: "Simple Test Pattern".to_string(),
            regex_pattern: r"\btest\d+\b".to_string(),
            pattern_type: CustomPatternType::Custom,
            severity: CustomSeverity::Low,
            description: "Simple test pattern for validation".to_string(),
            test_cases: vec![
                PatternTestCase {
                    text: "test123".to_string(),
                    should_match: true,
                    description: "Should match test with numbers".to_string(),
                },
                PatternTestCase {
                    text: "testing".to_string(),
                    should_match: false,
                    description: "Should not match without numbers".to_string(),
                },
            ],
            is_enabled: true,
            created_at: String::new(),
            updated_at: String::new(),
            performance_stats: PatternPerformanceStats {
                total_executions: 0,
                total_time_ms: 0,
                average_time_ms: 0.0,
                max_time_ms: 0,
                matches_found: 0,
                last_execution: None,
            },
            validation_config: PatternValidationConfig {
                requires_validation: false,
                confidence_threshold: 0.7,
                max_execution_time_ms: 25,
                enable_performance_monitoring: true,
            },
        };
        
        let pattern_id = manager.add_pattern(test_pattern).expect("Failed to add pattern");
        
        // Test pattern against various inputs
        let test_cases = vec![
            ("test123", true),
            ("test456", true),
            ("testing", false),
            ("test", false),
            ("123test", false),
        ];
        
        for (test_text, expected) in test_cases {
            let result = manager.test_pattern(&pattern_id, test_text).expect("Pattern test should not fail");
            assert_eq!(result, expected, "Pattern test failed for: {}", test_text);
        }
        
        // Run all test cases
        let test_results = manager.run_pattern_tests(&pattern_id).expect("Test execution should not fail");
        assert_eq!(test_results.len(), 2, "Should have 2 test results");
        
        for result in &test_results {
            assert!(result.passed, "Test case should pass: {}", result.test_case.description);
            assert!(result.execution_time_ms < 50, "Execution should be fast");
        }
        
        // Check performance stats were updated
        let pattern = manager.get_pattern(&pattern_id).expect("Pattern should exist");
        assert!(pattern.performance_stats.total_executions > 0, "Performance stats should be updated");
        
        println!("✅ Pattern testing and performance validation passed");
    }

    #[test]
    fn test_pattern_templates() {
        let template_manager = PatternTemplateManager::new();
        
        // Test template retrieval
        let all_templates = template_manager.get_all_templates();
        assert!(!all_templates.is_empty(), "Should have built-in templates");
        
        // Test category filtering
        let categories = template_manager.get_categories();
        assert!(!categories.is_empty(), "Should have template categories");
        
        for category in &categories {
            let category_templates = template_manager.get_templates_by_category(category);
            assert!(!category_templates.is_empty(), "Category should have templates: {}", category);
        }
        
        // Test difficulty filtering
        let beginner_templates = template_manager.get_templates_by_difficulty(TemplateDifficulty::Beginner);
        assert!(!beginner_templates.is_empty(), "Should have beginner templates");
        
        // Test template search
        let bitcoin_templates = template_manager.search_templates("bitcoin");
        assert!(!bitcoin_templates.is_empty(), "Should find bitcoin-related templates");
        
        // Test pattern creation from template
        if let Some(template) = all_templates.first() {
            let custom_pattern = template_manager.create_pattern_from_template(&template.id, Some("My Custom Pattern".to_string()));
            assert!(custom_pattern.is_some(), "Should create pattern from template");
            
            let pattern = custom_pattern.unwrap();
            assert_eq!(pattern.name, "My Custom Pattern");
            assert_eq!(pattern.regex_pattern, template.regex_pattern);
            assert!(!pattern.test_cases.is_empty(), "Should have test cases from template");
        }
        
        println!("✅ Pattern templates validation passed");
        println!("   - Total templates: {}", all_templates.len());
        println!("   - Categories: {:?}", categories);
    }

    #[test]
    fn test_pattern_import_export() {
        let mut manager = CustomPatternManager::new();
        
        // Create multiple test patterns
        let patterns = vec![
            CustomPattern {
                id: String::new(),
                name: "Export Test 1".to_string(),
                regex_pattern: r"\bexport1\b".to_string(),
                pattern_type: CustomPatternType::Custom,
                severity: CustomSeverity::Low,
                description: "First export test pattern".to_string(),
                test_cases: vec![],
                is_enabled: true,
                created_at: String::new(),
                updated_at: String::new(),
                performance_stats: PatternPerformanceStats {
                    total_executions: 0,
                    total_time_ms: 0,
                    average_time_ms: 0.0,
                    max_time_ms: 0,
                    matches_found: 0,
                    last_execution: None,
                },
                validation_config: PatternValidationConfig {
                    requires_validation: false,
                    confidence_threshold: 0.7,
                    max_execution_time_ms: 25,
                    enable_performance_monitoring: true,
                },
            },
            CustomPattern {
                id: String::new(),
                name: "Export Test 2".to_string(),
                regex_pattern: r"\bexport2\b".to_string(),
                pattern_type: CustomPatternType::Custom,
                severity: CustomSeverity::Medium,
                description: "Second export test pattern".to_string(),
                test_cases: vec![],
                is_enabled: true,
                created_at: String::new(),
                updated_at: String::new(),
                performance_stats: PatternPerformanceStats {
                    total_executions: 0,
                    total_time_ms: 0,
                    average_time_ms: 0.0,
                    max_time_ms: 0,
                    matches_found: 0,
                    last_execution: None,
                },
                validation_config: PatternValidationConfig {
                    requires_validation: false,
                    confidence_threshold: 0.8,
                    max_execution_time_ms: 30,
                    enable_performance_monitoring: true,
                },
            },
        ];
        
        let mut pattern_ids = Vec::new();
        for pattern in patterns {
            let id = manager.add_pattern(pattern).expect("Failed to add pattern");
            pattern_ids.push(id);
        }
        
        // Export patterns
        let exported_json = manager.export_patterns(None).expect("Export should succeed");
        assert!(!exported_json.is_empty(), "Exported JSON should not be empty");
        assert!(exported_json.contains("Export Test 1"), "Should contain first pattern");
        assert!(exported_json.contains("Export Test 2"), "Should contain second pattern");
        
        // Create new manager and import
        let mut new_manager = CustomPatternManager::new();
        let imported_ids = new_manager.import_patterns(&exported_json, false).expect("Import should succeed");
        assert_eq!(imported_ids.len(), 2, "Should import 2 patterns");
        
        // Verify imported patterns
        let imported_patterns = new_manager.get_all_patterns();
        assert_eq!(imported_patterns.len(), 2, "Should have 2 imported patterns");
        
        let names: Vec<&String> = imported_patterns.iter().map(|p| &p.name).collect();
        assert!(names.contains(&&"Export Test 1".to_string()), "Should have first pattern");
        assert!(names.contains(&&"Export Test 2".to_string()), "Should have second pattern");
        
        println!("✅ Pattern import/export validation passed");
    }

    #[test]
    fn test_pattern_categories() {
        let mut manager = CustomPatternManager::new();
        
        // Create a category
        let category_id = manager.create_category(
            "Test Category".to_string(),
            "Category for testing purposes".to_string(),
        );
        assert!(!category_id.is_empty(), "Category ID should be generated");
        
        // Create a test pattern
        let test_pattern = CustomPattern {
            id: String::new(),
            name: "Category Test Pattern".to_string(),
            regex_pattern: r"\bcategory\b".to_string(),
            pattern_type: CustomPatternType::Custom,
            severity: CustomSeverity::Low,
            description: "Pattern for category testing".to_string(),
            test_cases: vec![],
            is_enabled: true,
            created_at: String::new(),
            updated_at: String::new(),
            performance_stats: PatternPerformanceStats {
                total_executions: 0,
                total_time_ms: 0,
                average_time_ms: 0.0,
                max_time_ms: 0,
                matches_found: 0,
                last_execution: None,
            },
            validation_config: PatternValidationConfig {
                requires_validation: false,
                confidence_threshold: 0.7,
                max_execution_time_ms: 25,
                enable_performance_monitoring: true,
            },
        };
        
        let pattern_id = manager.add_pattern(test_pattern).expect("Failed to add pattern");
        
        // Add pattern to category
        manager.add_pattern_to_category(&pattern_id, &category_id).expect("Failed to add pattern to category");
        
        // Verify category contents
        let patterns_in_category = manager.get_patterns_in_category(&category_id);
        assert_eq!(patterns_in_category.len(), 1, "Category should have 1 pattern");
        assert_eq!(patterns_in_category[0].name, "Category Test Pattern");
        
        // Verify all categories
        let all_categories = manager.get_all_categories();
        assert_eq!(all_categories.len(), 1, "Should have 1 category");
        assert_eq!(all_categories[0].name, "Test Category");
        
        println!("✅ Pattern categories validation passed");
    }

    #[test]
    fn test_regex_safety_validation() {
        let mut manager = CustomPatternManager::new();
        
        // Test dangerous regex patterns that should be rejected
        let dangerous_patterns = vec![
            (r"(?=.*test)", "Positive lookahead"),
            (r"(?!.*test)", "Negative lookahead"),
            (r"(?<=test)", "Positive lookbehind"),
            (r"(?<!test)", "Negative lookbehind"),
            (r"(?>test)", "Atomic group"),
            (r"test++", "Possessive quantifier"),
        ];
        
        for (dangerous_regex, description) in dangerous_patterns {
            let test_pattern = CustomPattern {
                id: String::new(),
                name: format!("Dangerous Pattern: {}", description),
                regex_pattern: dangerous_regex.to_string(),
                pattern_type: CustomPatternType::Custom,
                severity: CustomSeverity::Low,
                description: format!("Testing dangerous pattern: {}", description),
                test_cases: vec![],
                is_enabled: true,
                created_at: String::new(),
                updated_at: String::new(),
                performance_stats: PatternPerformanceStats {
                    total_executions: 0,
                    total_time_ms: 0,
                    average_time_ms: 0.0,
                    max_time_ms: 0,
                    matches_found: 0,
                    last_execution: None,
                },
                validation_config: PatternValidationConfig {
                    requires_validation: false,
                    confidence_threshold: 0.7,
                    max_execution_time_ms: 25,
                    enable_performance_monitoring: true,
                },
            };
            
            let result = manager.add_pattern(test_pattern);
            assert!(result.is_err(), "Dangerous pattern should be rejected: {}", description);
            
            println!("✅ Rejected dangerous pattern: {}", description);
        }
        
        // Test safe patterns that should be accepted
        let safe_patterns = vec![
            (r"\btest\b", "Word boundary"),
            (r"test\d+", "Digit quantifier"),
            (r"[a-zA-Z]+", "Character class"),
            (r"test|example", "Alternation"),
        ];
        
        for (safe_regex, description) in safe_patterns {
            let test_pattern = CustomPattern {
                id: String::new(),
                name: format!("Safe Pattern: {}", description),
                regex_pattern: safe_regex.to_string(),
                pattern_type: CustomPatternType::Custom,
                severity: CustomSeverity::Low,
                description: format!("Testing safe pattern: {}", description),
                test_cases: vec![],
                is_enabled: true,
                created_at: String::new(),
                updated_at: String::new(),
                performance_stats: PatternPerformanceStats {
                    total_executions: 0,
                    total_time_ms: 0,
                    average_time_ms: 0.0,
                    max_time_ms: 0,
                    matches_found: 0,
                    last_execution: None,
                },
                validation_config: PatternValidationConfig {
                    requires_validation: false,
                    confidence_threshold: 0.7,
                    max_execution_time_ms: 25,
                    enable_performance_monitoring: true,
                },
            };
            
            let result = manager.add_pattern(test_pattern);
            assert!(result.is_ok(), "Safe pattern should be accepted: {}", description);
            
            println!("✅ Accepted safe pattern: {}", description);
        }
        
        println!("✅ Regex safety validation passed");
    }

    #[test]
    fn test_integrated_detection() {
        let mut integrated_detector = IntegratedDetector::new().expect("Failed to create integrated detector");
        
        // Add a custom pattern to the integrated detector
        let custom_pattern = CustomPattern {
            id: String::new(),
            name: "Integration Test Pattern".to_string(),
            regex_pattern: r"\bintegration_test_\d+\b".to_string(),
            pattern_type: CustomPatternType::Custom,
            severity: CustomSeverity::Medium,
            description: "Pattern for integration testing".to_string(),
            test_cases: vec![
                PatternTestCase {
                    text: "integration_test_123".to_string(),
                    should_match: true,
                    description: "Should match integration test pattern".to_string(),
                },
            ],
            is_enabled: true,
            created_at: String::new(),
            updated_at: String::new(),
            performance_stats: PatternPerformanceStats {
                total_executions: 0,
                total_time_ms: 0,
                average_time_ms: 0.0,
                max_time_ms: 0,
                matches_found: 0,
                last_execution: None,
            },
            validation_config: PatternValidationConfig {
                requires_validation: false,
                confidence_threshold: 0.8,
                max_execution_time_ms: 50,
                enable_performance_monitoring: true,
            },
        };
        
        let _pattern_id = integrated_detector.get_custom_pattern_manager()
            .add_pattern(custom_pattern)
            .expect("Failed to add custom pattern");
        
        // Test integrated detection with both built-in and custom patterns
        let test_text = "
            Bitcoin address: **********************************
            Custom pattern: integration_test_456
            Ethereum address: ******************************************
        ";
        
        let result = integrated_detector.detect(test_text).expect("Detection should not fail");
        
        // Verify results
        assert!(result.total_findings > 0, "Should detect patterns");
        assert!(result.processing_time_ms < 1000, "Should complete within performance target");
        
        // Check that both built-in and custom patterns were executed
        assert!(result.performance_summary.builtin_patterns_executed > 0, "Should execute built-in patterns");
        // Custom patterns might be 0 if none match or are enabled
        println!("Custom patterns executed: {}", result.performance_summary.custom_patterns_executed);
        
        // Verify combined findings include both types
        let has_builtin = result.combined_findings.iter().any(|f| matches!(f.source, tauri_appprivacy_ai_lib::security::integrated_detector::FindingSource::BuiltinPattern { .. }));
        let _has_custom = result.combined_findings.iter().any(|f| matches!(f.source, tauri_appprivacy_ai_lib::security::integrated_detector::FindingSource::CustomPattern { .. }));
        
        assert!(has_builtin, "Should have built-in pattern findings");
        // Note: Custom pattern might not match if the text doesn't contain the pattern
        
        println!("✅ Integrated detection validation passed");
        println!("   - Total findings: {}", result.total_findings);
        println!("   - Processing time: {}ms", result.processing_time_ms);
        println!("   - Built-in patterns: {}", result.performance_summary.builtin_patterns_executed);
        println!("   - Custom patterns: {}", result.performance_summary.custom_patterns_executed);
    }

    #[test]
    fn test_performance_monitoring() {
        let mut manager = CustomPatternManager::new();
        
        // Set a low performance limit for testing
        manager.set_performance_limit(10); // 10ms limit
        
        // Create a pattern that might be slow (complex regex)
        let complex_pattern = CustomPattern {
            id: String::new(),
            name: "Complex Pattern".to_string(),
            regex_pattern: r"\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b".to_string(), // Email pattern
            pattern_type: CustomPatternType::Custom,
            severity: CustomSeverity::Low,
            description: "Complex email pattern for performance testing".to_string(),
            test_cases: vec![
                PatternTestCase {
                    text: "<EMAIL>".to_string(),
                    should_match: true,
                    description: "Valid email".to_string(),
                },
            ],
            is_enabled: true,
            created_at: String::new(),
            updated_at: String::new(),
            performance_stats: PatternPerformanceStats {
                total_executions: 0,
                total_time_ms: 0,
                average_time_ms: 0.0,
                max_time_ms: 0,
                matches_found: 0,
                last_execution: None,
            },
            validation_config: PatternValidationConfig {
                requires_validation: false,
                confidence_threshold: 0.7,
                max_execution_time_ms: 5, // Very low limit
                enable_performance_monitoring: true,
            },
        };
        
        let pattern_id = manager.add_pattern(complex_pattern).expect("Failed to add pattern");
        
        // Test pattern multiple times to build performance stats
        let test_text = "Contact <NAME_EMAIL> or <EMAIL> for more information.";
        
        for _ in 0..5 {
            let _ = manager.test_pattern(&pattern_id, test_text);
        }
        
        // Check performance statistics
        let performance_summary = manager.get_performance_summary();
        assert!(performance_summary.contains_key(&pattern_id), "Should have performance stats for pattern");
        
        let stats = &performance_summary[&pattern_id];
        assert!(stats.total_executions >= 5, "Should have executed multiple times");
        assert!(stats.average_time_ms >= 0.0, "Should have average time");
        
        println!("✅ Performance monitoring validation passed");
        println!("   - Total executions: {}", stats.total_executions);
        println!("   - Average time: {:.2}ms", stats.average_time_ms);
        println!("   - Max time: {}ms", stats.max_time_ms);
    }
}
