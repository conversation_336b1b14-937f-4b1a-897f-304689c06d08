# 🔧 **Phase 5 Weeks 10-12 Technical Specifications**

**Specification Date**: July 27, 2025  
**Implementation Period**: August 3-24, 2025  
**Scope**: API Extensions, Audit Logging, Configuration Management  
**Status**: ✅ **Ready for Implementation**

## 🎯 **Implementation Overview**

This document provides detailed technical specifications for implementing the approved Phase 5 enterprise features using recommended open source libraries and best practices identified in the code quality audit.

### **Implementation Schedule**
- **Week 10 (Aug 3-10)**: API Extensions with `axum` framework
- **Week 11 (Aug 10-17)**: Audit Logging with `tracing` ecosystem  
- **Week 12 (Aug 17-24)**: Configuration Management with local-first approach

---

## 🌐 **Week 10: API Extensions Implementation**

### **Technical Architecture**

#### **Core Dependencies**
```toml
[dependencies]
# API Framework
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout"] }

# Authentication & Security
jsonwebtoken = "9.0"
tower-governor = "0.2"
uuid = { version = "1.0", features = ["v4"] }
ring = "0.17"

# HTTP Client for webhooks
reqwest = { version = "0.11", features = ["json"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Documentation
utoipa = { version = "4.0", features = ["axum_extras"] }
utoipa-swagger-ui = { version = "4.0", features = ["axum"] }
```

#### **API Endpoint Specifications**

##### **1. Scan Operations**
```rust
// POST /api/v1/scan/file
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ScanFileRequest {
    /// Path to the file to scan
    #[schema(example = "/path/to/document.pdf")]
    pub file_path: String,
    
    /// Scan profile to use
    #[schema(example = "comprehensive")]
    pub profile: String,
    
    /// Optional webhook URL for completion notification
    #[schema(example = "https://example.com/webhook")]
    pub webhook_url: Option<String>,
    
    /// Scan timeout in seconds (default: 300)
    #[schema(example = 300)]
    pub timeout_seconds: Option<u32>,
}

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ScanFileResponse {
    /// Unique scan identifier
    pub scan_id: String,
    
    /// Current scan status
    pub status: ScanStatus,
    
    /// Estimated completion time
    pub estimated_completion: Option<DateTime<Utc>>,
    
    /// API endpoint to check status
    pub status_url: String,
}

#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub enum ScanStatus {
    Queued,
    InProgress { progress_percent: u8 },
    Completed { findings_count: u32 },
    Failed { error_message: String },
}
```

##### **2. Status and Results**
```rust
// GET /api/v1/scan/{scan_id}/status
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ScanStatusResponse {
    pub scan_id: String,
    pub status: ScanStatus,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub results: Option<ScanResults>,
}

// GET /api/v1/scan/{scan_id}/results
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct ScanResults {
    pub scan_id: String,
    pub file_path: String,
    pub scan_profile: String,
    pub processing_time_ms: u64,
    pub findings: Vec<Finding>,
    pub risk_score: f32,
    pub compliance_status: ComplianceStatus,
}
```

#### **Authentication Implementation**

##### **API Key System**
```rust
// src/api/auth.rs
use ring::{digest, rand};
use base64::{Engine as _, engine::general_purpose};

#[derive(Debug, Clone)]
pub struct ApiKey {
    pub id: String,
    pub key_hash: String,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub rate_limit: u32,
}

pub struct ApiKeyManager {
    keys: Arc<RwLock<HashMap<String, ApiKey>>>,
}

impl ApiKeyManager {
    pub fn generate_key(&self) -> Result<(String, ApiKey)> {
        let rng = rand::SystemRandom::new();
        let mut key_bytes = [0u8; 32];
        rng.fill(&mut key_bytes)?;
        
        let key_string = general_purpose::STANDARD.encode(key_bytes);
        let key_hash = digest::digest(&digest::SHA256, key_string.as_bytes());
        
        let api_key = ApiKey {
            id: Uuid::new_v4().to_string(),
            key_hash: general_purpose::STANDARD.encode(key_hash.as_ref()),
            created_at: Utc::now(),
            last_used: None,
            rate_limit: 100, // requests per minute
        };
        
        Ok((key_string, api_key))
    }
    
    pub async fn validate_key(&self, key: &str) -> Result<Option<ApiKey>> {
        let key_hash = digest::digest(&digest::SHA256, key.as_bytes());
        let hash_string = general_purpose::STANDARD.encode(key_hash.as_ref());
        
        let keys = self.keys.read().await;
        Ok(keys.values().find(|k| k.key_hash == hash_string).cloned())
    }
}
```

#### **Rate Limiting Configuration**
```rust
// src/api/middleware.rs
use tower_governor::{GovernorConfigBuilder, GovernorLayer};

pub fn create_rate_limiter() -> GovernorLayer<PeerIpKeyExtractor, NoOpMiddleware> {
    let governor_conf = Box::new(
        GovernorConfigBuilder::default()
            .per_second(2)           // 2 requests per second
            .burst_size(10)          // Allow bursts up to 10 requests
            .use_headers()           // Include rate limit headers in response
            .finish()
            .unwrap(),
    );
    
    GovernorLayer {
        config: governor_conf,
    }
}
```

#### **Webhook Implementation**
```rust
// src/api/webhooks.rs
use reqwest::Client;

#[derive(Debug, Serialize)]
pub struct WebhookPayload {
    pub scan_id: String,
    pub status: ScanStatus,
    pub timestamp: DateTime<Utc>,
    pub results_url: Option<String>,
}

pub struct WebhookManager {
    client: Client,
}

impl WebhookManager {
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");
            
        Self { client }
    }
    
    pub async fn send_notification(
        &self,
        webhook_url: &str,
        payload: WebhookPayload,
    ) -> Result<()> {
        let response = self
            .client
            .post(webhook_url)
            .json(&payload)
            .header("Content-Type", "application/json")
            .header("User-Agent", "PrivacyAI-Webhook/1.0")
            .send()
            .await?;
            
        if response.status().is_success() {
            tracing::info!("Webhook sent successfully to {}", webhook_url);
        } else {
            tracing::warn!("Webhook failed with status: {}", response.status());
        }
        
        Ok(())
    }
}
```

### **Performance Requirements**
- **Response Time**: <100ms for scan initiation
- **Throughput**: 1000+ requests/hour with rate limiting
- **Memory Usage**: <10MB additional for API server
- **Concurrent Connections**: Support 50+ simultaneous connections

---

## 📋 **Week 11: Audit Logging Implementation**

### **Technical Architecture**

#### **Core Dependencies**
```toml
[dependencies]
# Structured Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter"] }
tracing-appender = "0.2"

# Log Processing
csv = "1.3"
chrono = { version = "0.4", features = ["serde"] }

# File Operations
tempfile = "3.8"
```

#### **Audit Log Structure**
```rust
// src/logging/audit.rs
#[derive(Debug, Serialize, Deserialize)]
pub struct AuditLogEntry {
    /// ISO 8601 timestamp
    pub timestamp: DateTime<Utc>,
    
    /// Event type identifier
    pub event_type: AuditEventType,
    
    /// User or system identifier
    pub actor: String,
    
    /// Unique session or request identifier
    pub session_id: String,
    
    /// Resource being accessed or modified
    pub resource: String,
    
    /// Action performed
    pub action: String,
    
    /// Result of the action
    pub result: AuditResult,
    
    /// Additional context data
    pub metadata: serde_json::Value,
    
    /// Processing time in milliseconds
    pub duration_ms: Option<u64>,
    
    /// Client IP address (if applicable)
    pub client_ip: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum AuditEventType {
    ScanStarted,
    ScanCompleted,
    ScanFailed,
    ConfigurationChanged,
    ApiKeyGenerated,
    ApiKeyUsed,
    DataExported,
    SystemStarted,
    SystemShutdown,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum AuditResult {
    Success,
    Failure { error_code: String, error_message: String },
    Partial { warning_message: String },
}
```

#### **Logging Service Implementation**
```rust
// src/logging/service.rs
use tracing_appender::rolling::{RollingFileAppender, Rotation};

pub struct AuditLogger {
    _guard: tracing_appender::non_blocking::WorkerGuard,
}

impl AuditLogger {
    pub fn new(log_directory: &str) -> Result<Self> {
        // Create rolling file appender (daily rotation)
        let file_appender = RollingFileAppender::new(
            Rotation::daily(),
            log_directory,
            "audit.log"
        );
        
        let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);
        
        // Configure JSON formatting
        let subscriber = tracing_subscriber::fmt()
            .with_writer(non_blocking)
            .json()
            .with_current_span(false)
            .with_span_list(false)
            .finish();
            
        tracing::subscriber::set_global_default(subscriber)?;
        
        Ok(Self { _guard: guard })
    }
    
    pub fn log_event(&self, entry: AuditLogEntry) {
        tracing::info!(
            timestamp = %entry.timestamp,
            event_type = ?entry.event_type,
            actor = %entry.actor,
            session_id = %entry.session_id,
            resource = %entry.resource,
            action = %entry.action,
            result = ?entry.result,
            duration_ms = entry.duration_ms,
            client_ip = entry.client_ip,
            metadata = %entry.metadata,
            "audit_event"
        );
    }
}
```

#### **Log Export Functionality**
```rust
// src/logging/export.rs
use csv::Writer;

pub struct LogExporter;

impl LogExporter {
    pub async fn export_to_csv(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        output_path: &str,
    ) -> Result<()> {
        let mut writer = Writer::from_path(output_path)?;
        
        // Write CSV headers
        writer.write_record(&[
            "timestamp",
            "event_type", 
            "actor",
            "session_id",
            "resource",
            "action",
            "result",
            "duration_ms",
            "client_ip",
        ])?;
        
        // Read and filter log entries
        let log_entries = self.read_log_entries(start_date, end_date).await?;
        
        for entry in log_entries {
            writer.write_record(&[
                entry.timestamp.to_rfc3339(),
                format!("{:?}", entry.event_type),
                entry.actor,
                entry.session_id,
                entry.resource,
                entry.action,
                format!("{:?}", entry.result),
                entry.duration_ms.map(|d| d.to_string()).unwrap_or_default(),
                entry.client_ip.unwrap_or_default(),
            ])?;
        }
        
        writer.flush()?;
        Ok(())
    }
    
    async fn read_log_entries(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<Vec<AuditLogEntry>> {
        // Implementation to read and parse log files
        // Filter by date range and return structured entries
        todo!("Implement log file parsing")
    }
}
```

### **Compliance Features**
- **GDPR Compliance**: No personal data in logs, configurable retention
- **HIPAA Compliance**: Secure log storage, access controls
- **PCI-DSS Compliance**: Detailed transaction logging, integrity protection
- **SOX Compliance**: Immutable audit trails, regular exports

### **Performance Requirements**
- **Logging Overhead**: <5ms per audit event
- **File Rotation**: Daily rotation with 90-day retention
- **Export Performance**: <30 seconds for 10,000 log entries
- **Storage Efficiency**: Compressed JSON format, <1MB per 1000 entries

---

## ⚙️ **Week 12: Configuration Management Implementation**

### **Technical Architecture**

#### **Core Dependencies**
```toml
[dependencies]
# JSON Processing and Validation
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
jsonschema = "0.17"

# File Operations
camino = "1.1"
tempfile = "3.8"

# Error Handling
thiserror = "1.0"
```

#### **Configuration Schema**
```rust
// src/config/schema.rs
#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct PrivacyAIConfig {
    /// Configuration format version
    #[serde(default = "default_version")]
    pub version: String,
    
    /// Human-readable configuration name
    pub name: String,
    
    /// Optional description
    pub description: Option<String>,
    
    /// Scan profile configuration
    pub scan_profile: ScanProfileConfig,
    
    /// Detection type settings
    pub detection_types: DetectionTypesConfig,
    
    /// Processing method settings
    pub processing_methods: ProcessingMethodsConfig,
    
    /// Performance and resource settings
    pub performance_settings: PerformanceConfig,
    
    /// Export and reporting settings
    pub export_settings: Option<ExportConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct ScanProfileConfig {
    /// Base profile (quick_text, comprehensive, etc.)
    pub base_profile: String,
    
    /// Custom overrides
    pub custom_overrides: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct DetectionTypesConfig {
    pub privacy_detection: bool,
    pub cryptocurrency_detection: bool,
    pub government_id_detection: bool,
    pub file_integrity_detection: bool,
    pub financial_data_detection: bool,
    pub custom_patterns: Vec<CustomPattern>,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct ProcessingMethodsConfig {
    pub ocr_processing: bool,
    pub ai_visual_detection: bool,
    pub metadata_extraction: bool,
    pub binary_analysis: bool,
    pub archive_extraction: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, JsonSchema)]
pub struct PerformanceConfig {
    /// Performance mode: fast, balanced, comprehensive
    pub performance_mode: String,
    
    /// Maximum file size to process (MB)
    pub max_file_size_mb: u64,
    
    /// Processing timeout (seconds)
    pub timeout_seconds: u32,
    
    /// Memory limit (MB)
    pub memory_limit_mb: u64,
    
    /// Concurrent processing threads
    pub max_threads: u32,
}
```

#### **Configuration Manager Implementation**
```rust
// src/config/manager.rs
use jsonschema::{JSONSchema, ValidationError};

pub struct ConfigurationManager {
    schema: JSONSchema,
    current_config: Option<PrivacyAIConfig>,
}

impl ConfigurationManager {
    pub fn new() -> Result<Self> {
        let schema_json = include_str!("../schemas/config_schema.json");
        let schema_value: serde_json::Value = serde_json::from_str(schema_json)?;
        let schema = JSONSchema::compile(&schema_value)?;
        
        Ok(Self {
            schema,
            current_config: None,
        })
    }
    
    pub fn export_config(&self, output_path: &str) -> Result<()> {
        let config = self.current_config.as_ref()
            .ok_or_else(|| ConfigError::NoConfigLoaded)?;
            
        let json_string = serde_json::to_string_pretty(config)?;
        std::fs::write(output_path, json_string)?;
        
        tracing::info!("Configuration exported to {}", output_path);
        Ok(())
    }
    
    pub fn import_config(&mut self, input_path: &str) -> Result<PrivacyAIConfig> {
        let json_string = std::fs::read_to_string(input_path)?;
        let config_value: serde_json::Value = serde_json::from_str(&json_string)?;
        
        // Validate against schema
        if let Err(errors) = self.schema.validate(&config_value) {
            let error_messages: Vec<String> = errors
                .map(|e| format!("{}: {}", e.instance_path, e))
                .collect();
            return Err(ConfigError::ValidationFailed(error_messages));
        }
        
        let config: PrivacyAIConfig = serde_json::from_value(config_value)?;
        self.current_config = Some(config.clone());
        
        tracing::info!("Configuration imported from {}", input_path);
        Ok(config)
    }
    
    pub fn validate_config_string(&self, json_string: &str) -> Result<Vec<String>> {
        let config_value: serde_json::Value = serde_json::from_str(json_string)?;
        
        match self.schema.validate(&config_value) {
            Ok(_) => Ok(vec![]),
            Err(errors) => {
                let error_messages: Vec<String> = errors
                    .map(|e| format!("{}: {}", e.instance_path, e))
                    .collect();
                Ok(error_messages)
            }
        }
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("No configuration loaded")]
    NoConfigLoaded,
    
    #[error("Configuration validation failed: {0:?}")]
    ValidationFailed(Vec<String>),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),
    
    #[error("Schema error: {0}")]
    Schema(#[from] jsonschema::ValidationError<'static>),
}
```

#### **Configuration Sharing Features**
```rust
// src/config/sharing.rs
use base64::{Engine as _, engine::general_purpose};

pub struct ConfigurationSharing;

impl ConfigurationSharing {
    /// Generate a shareable URL for configuration
    pub fn generate_share_url(&self, config: &PrivacyAIConfig) -> Result<String> {
        let json_string = serde_json::to_string(config)?;
        let encoded = general_purpose::STANDARD.encode(json_string.as_bytes());
        
        Ok(format!("privacyai://config/{}", encoded))
    }
    
    /// Parse configuration from shareable URL
    pub fn parse_share_url(&self, url: &str) -> Result<PrivacyAIConfig> {
        let prefix = "privacyai://config/";
        if !url.starts_with(prefix) {
            return Err(ConfigError::InvalidShareUrl);
        }
        
        let encoded = &url[prefix.len()..];
        let decoded = general_purpose::STANDARD.decode(encoded)?;
        let json_string = String::from_utf8(decoded)?;
        let config: PrivacyAIConfig = serde_json::from_str(&json_string)?;
        
        Ok(config)
    }
    
    /// Export configuration to QR code (for mobile sharing)
    pub fn generate_qr_code(&self, config: &PrivacyAIConfig) -> Result<Vec<u8>> {
        let share_url = self.generate_share_url(config)?;
        // QR code generation implementation
        todo!("Implement QR code generation")
    }
}
```

### **UI Integration Points**
- **Export Button**: Save current configuration to JSON file
- **Import Button**: Load configuration from JSON file or URL
- **Share Button**: Generate shareable URL or QR code
- **Validation Display**: Real-time validation feedback
- **Configuration Preview**: Show configuration summary before import

### **Performance Requirements**
- **Export Time**: <1 second for configuration export
- **Import Time**: <2 seconds for configuration import and validation
- **Validation Time**: <100ms for real-time validation
- **File Size**: <10KB for typical configuration files

---

## 🧪 **Testing Strategy**

### **Test Coverage Requirements**
- **Unit Tests**: >90% coverage for all new modules
- **Integration Tests**: Complete workflow testing for each feature
- **Performance Tests**: Benchmark all performance-critical operations
- **Security Tests**: Authentication, authorization, input validation

### **Test Implementation**
```toml
[dev-dependencies]
tokio-test = "0.4"
wiremock = "0.5"
tempfile = "3.8"
criterion = "0.5"
proptest = "1.0"
```

### **Quality Gates**
- **Pre-commit**: Linting, formatting, unit tests
- **CI/CD**: Full test suite, security scanning, performance benchmarks
- **Release**: Manual security review, integration testing

**These technical specifications provide a comprehensive implementation guide for delivering high-quality, secure, and performant enterprise features while maintaining PrivacyAI's core strengths.**
