import React, { useState, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { 
  Upload, 
  FileText, 
  Image, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Loader2,
  Shield,
  Eye,
  Folder
} from 'lucide-react';

// Type definitions for file processing
interface DroppedFile {
  path: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

interface FileRiskAssessment {
  file_path: string;
  risk_level: string;
  confidence: number;
  preview_findings: string[];
  processing_time_ms: number;
  file_size: number;
}

interface DragDropZoneProps {
  onFilesDropped?: (files: DroppedFile[]) => void;
  onRiskAssessment?: (assessments: FileRiskAssessment[]) => void;
  onError?: (error: string) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  disabled?: boolean;
  showInstantPreview?: boolean;
}

const DragDropZone: React.FC<DragDropZoneProps> = ({
  onFilesDropped,
  onRiskAssessment,
  onError,
  maxFiles = 10,
  acceptedTypes = ['.pdf', '.jpg', '.jpeg', '.png', '.txt', '.doc', '.docx'],
  disabled = false,
  showInstantPreview = true
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [droppedFiles, setDroppedFiles] = useState<DroppedFile[]>([]);
  const [riskAssessments, setRiskAssessments] = useState<FileRiskAssessment[]>([]);
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragging(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Only set dragging to false if we're leaving the drop zone entirely
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
      setIsDragging(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const validateFile = (file: File): boolean => {
    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (acceptedTypes.length > 0 && !acceptedTypes.includes(fileExtension)) {
      return false;
    }
    
    // Check file size (100MB limit)
    const maxSizeBytes = 100 * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return false;
    }
    
    return true;
  };

  const processFiles = async (files: File[]) => {
    setIsProcessing(true);
    setError('');
    
    try {
      // Validate files
      const validFiles = files.filter(validateFile);
      if (validFiles.length !== files.length) {
        const invalidCount = files.length - validFiles.length;
        setError(`${invalidCount} file(s) were skipped due to invalid type or size`);
      }
      
      // Limit number of files
      const filesToProcess = validFiles.slice(0, maxFiles);
      if (validFiles.length > maxFiles) {
        setError(`Only processing first ${maxFiles} files`);
      }
      
      // Convert to DroppedFile format
      const droppedFileList: DroppedFile[] = filesToProcess.map(file => ({
        path: (file as any).path || file.name, // Tauri provides file.path
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
      
      setDroppedFiles(droppedFileList);
      
      // Perform instant risk assessment if enabled
      if (showInstantPreview && droppedFileList.length > 0) {
        const assessments = await performRiskAssessment(droppedFileList);
        setRiskAssessments(assessments);
        
        if (onRiskAssessment) {
          onRiskAssessment(assessments);
        }
      }
      
      if (onFilesDropped) {
        onFilesDropped(droppedFileList);
      }
      
    } catch (err) {
      const errorMessage = `Failed to process files: ${err}`;
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const performRiskAssessment = async (files: DroppedFile[]): Promise<FileRiskAssessment[]> => {
    const assessments: FileRiskAssessment[] = [];
    
    for (const file of files) {
      try {
        const assessment = await invoke<FileRiskAssessment>(
          'quick_privacy_assessment',
          { filePath: file.path }
        );
        assessments.push(assessment);
      } catch (err) {
        // If assessment fails, create a default assessment
        assessments.push({
          file_path: file.path,
          risk_level: 'unknown',
          confidence: 0.0,
          preview_findings: [],
          processing_time_ms: 0,
          file_size: file.size
        });
      }
    }
    
    return assessments;
  };

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    
    if (disabled) return;
    
    const files = Array.from(e.dataTransfer.files);
    await processFiles(files);
  }, [disabled, maxFiles, acceptedTypes, showInstantPreview]);

  const handleFileSelect = useCallback(async () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  const handleFileInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      await processFiles(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, []);

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'critical':
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'medium': return <Eye className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff'].includes(extension || '')) {
      return <Image className="w-5 h-5 text-blue-500" />;
    }
    return <FileText className="w-5 h-5 text-gray-500" />;
  };

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
          ${isDragging 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={!disabled ? handleFileSelect : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />
        
        {isProcessing ? (
          <div className="flex flex-col items-center space-y-3">
            <Loader2 className="w-12 h-12 text-blue-500 animate-spin" />
            <p className="text-lg font-medium text-gray-700">Processing files...</p>
            <p className="text-sm text-gray-500">Performing instant privacy assessment</p>
          </div>
        ) : isDragging ? (
          <div className="flex flex-col items-center space-y-3">
            <Upload className="w-12 h-12 text-blue-500" />
            <p className="text-lg font-medium text-blue-700">Drop files to analyze</p>
            <p className="text-sm text-blue-600">Instant privacy risk assessment</p>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-3">
            <Folder className="w-12 h-12 text-gray-400" />
            <p className="text-lg font-medium text-gray-700">
              Drag files here or click to browse
            </p>
            <p className="text-sm text-gray-500">
              Supports: {acceptedTypes.join(', ')} • Max {maxFiles} files • 100MB limit
            </p>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <XCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800 font-medium">Processing Error</span>
          </div>
          <p className="text-red-700 mt-2">{error}</p>
        </div>
      )}

      {/* File List with Risk Assessment */}
      {droppedFiles.length > 0 && (
        <div className="bg-white border rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">
            Dropped Files ({droppedFiles.length})
          </h3>
          <div className="space-y-3">
            {droppedFiles.map((file, index) => {
              const assessment = riskAssessments.find(a => a.file_path === file.path);
              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(file.name)}
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  
                  {assessment && showInstantPreview && (
                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRiskColor(assessment.risk_level)}`}>
                      {getRiskIcon(assessment.risk_level)}
                      <span className="ml-1">
                        {assessment.risk_level.toUpperCase()}
                      </span>
                      <span className="ml-2 text-xs opacity-75">
                        ({(assessment.confidence * 100).toFixed(0)}%)
                      </span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default DragDropZone;
