/**
 * Document Type Detector Component
 * 
 * Provides comprehensive document type detection using template matching,
 * ML classification, and ensemble methods. Integrates with the unified
 * document detection system.
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import './DocumentTypeDetector.css';

// Type definitions
interface DetectionConfig {
    enable_template_matching: boolean;
    enable_ml_classification: boolean;
    ensemble_method: string;
    min_confidence: number;
}

interface DocumentDetectionRequest {
    image_data: string;
    format: string;
    extracted_text?: string;
    config?: DetectionConfig;
}

interface DocumentDetectionResponse {
    success: boolean;
    document_type: string;
    confidence: number;
    template_confidence: number;
    ml_confidence: number;
    processing_time_ms: number;
    layout_analysis: LayoutAnalysisResult;
    ensemble_details: EnsembleDetailsResult;
    error?: string;
}

interface LayoutAnalysisResult {
    text_blocks: number;
    tables: number;
    form_fields: number;
    has_header: boolean;
    has_footer: boolean;
    text_density: number;
    complexity_score: number;
}

interface EnsembleDetailsResult {
    template_weight: number;
    ml_weight: number;
    consensus_score: number;
    methods_agree: boolean;
}

const DocumentTypeDetector: React.FC = () => {
    const [isInitialized, setIsInitialized] = useState<boolean>(false);
    const [isProcessing, setIsProcessing] = useState<boolean>(false);
    const [detectionResult, setDetectionResult] = useState<DocumentDetectionResponse | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [dragActive, setDragActive] = useState<boolean>(false);
    const [processingProgress, setProcessingProgress] = useState<number>(0);
    const [supportedTypes, setSupportedTypes] = useState<string[]>([]);
    const [, setPerformanceStats] = useState<Record<string, number>>({});
    const [config, setConfig] = useState<DetectionConfig>({
        enable_template_matching: true,
        enable_ml_classification: true,
        ensemble_method: 'adaptive',
        min_confidence: 0.7
    });
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [selectedPaths, setSelectedPaths] = useState<string[]>([]);
    const [currentFilePath, setCurrentFilePath] = useState<string | null>(null);

    // Initialize detector on component mount
    useEffect(() => {
        initializeDetector();
        loadSupportedTypes();
    }, []);

    // Initialize the document detector
    const initializeDetector = async () => {
        try {
            console.log('🔧 Initializing document detector...');
            const result = await invoke<string>('initialize_document_detector');
            console.log('✅ Detector initialized:', result);
            setIsInitialized(true);
            setError(null);
        } catch (err) {
            console.error('❌ Failed to initialize detector:', err);
            setError(`Initialization failed: ${err}`);
            setIsInitialized(false);
        }
    };

    // Load supported document types
    const loadSupportedTypes = async () => {
        try {
            const types = await invoke<string[]>('get_supported_document_types');
            setSupportedTypes(types);
        } catch (err) {
            console.warn('Failed to load supported types:', err);
        }
    };

    // Handle file selection
    const handleFileSelect = useCallback(async (files: FileList) => {
        if (!files || files.length === 0) return;
        if (!isInitialized) {
            setError('Detector not initialized. Please wait...');
            return;
        }

        const file = files[0];
        
        try {
            setError(null);
            setIsProcessing(true);
            setProcessingProgress(10);
            setCurrentFilePath(file.name); // Track the current file

            // Validate file
            if (!file.type.startsWith('image/')) {
                throw new Error('Please select an image file');
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                throw new Error('File too large. Maximum size is 10MB');
            }

            // Convert file to base64
            const base64Data = await fileToBase64(file);
            const imageFormat = getImageFormat(file);
            
            setProcessingProgress(30);

            // Prepare detection request
            const request: DocumentDetectionRequest = {
                image_data: base64Data,
                format: imageFormat,
                extracted_text: undefined, // Could be integrated with OCR
                config: config
            };

            setProcessingProgress(50);

            // Perform document type detection
            const result = await invoke<DocumentDetectionResponse>('detect_document_type', { request });

            setProcessingProgress(90);

            if (result.success) {
                setDetectionResult(result);
                
                // Update performance stats
                const stats = await invoke<Record<string, number>>('get_detector_performance_stats');
                setPerformanceStats(stats);
            } else {
                setError(result.error || 'Document type detection failed');
            }

            setProcessingProgress(100);
            
        } catch (err) {
            console.error('Document detection error:', err);
            setError((err as Error).message || 'Detection failed');
        } finally {
            setIsProcessing(false);
            setTimeout(() => setProcessingProgress(0), 1000);
        }
    }, [isInitialized, config]);

    // Handle drag and drop
    const handleDrag = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    }, []);

    const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
        
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files);
        }
    }, [handleFileSelect]);

    // Handle file input change
    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            handleFileSelect(e.target.files);
        }
    }, [handleFileSelect]);

    // Update configuration
    const updateConfig = async (newConfig: DetectionConfig) => {
        try {
            await invoke<string>('update_detector_config', { config: newConfig });
            setConfig(newConfig);
            console.log('✅ Configuration updated');
        } catch (err) {
            console.error('❌ Failed to update config:', err);
            setError(`Configuration update failed: ${err}`);
        }
    };

    // Handle file selection via dialog
    const handleSelectFiles = async () => {
        try {
            const filePaths = await invoke<string[]>('select_files');
            if (filePaths && filePaths.length > 0) {
                setSelectedPaths(filePaths);
                setError(null);
                // Process first image file found
                const imageFile = filePaths.find(path =>
                    /\.(jpg|jpeg|png|gif|bmp|tiff)$/i.test(path)
                );
                if (imageFile) {
                    setError(`File selected: ${imageFile.split(/[/\\]/).pop()}`);
                } else {
                    setError('No image files found in selection. Please select image files for document detection.');
                }
            }
        } catch (err) {
            setError(`Failed to select files: ${err}`);
        }
    };

    // Handle folder selection via dialog
    const handleSelectFolder = async () => {
        try {
            const folderPath = await invoke<string | null>('select_directory');
            if (folderPath) {
                setSelectedPaths([folderPath]);
                setError(null);
                setDetectionResult(null);
                setError(`Folder selected: ${folderPath.split(/[/\\]/).pop()}. Drag and drop image files to process them.`);
            }
        } catch (err) {
            setError(`Failed to select folder: ${err}`);
        }
    };

    // Utility functions
    const fileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                if (typeof reader.result === 'string') {
                    const base64 = reader.result.split(',')[1];
                    resolve(base64);
                } else {
                    reject(new Error('Failed to read file as base64'));
                }
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    };

    const getImageFormat = (file: File): string => {
        const typeMap: Record<string, string> = {
            'image/jpeg': 'jpeg',
            'image/jpg': 'jpeg',
            'image/png': 'png',
            'image/webp': 'webp',
            'image/bmp': 'bmp',
            'image/tiff': 'tiff'
        };
        return typeMap[file.type] || 'png';
    };

    const getDocumentTypeIcon = (docType: string): string => {
        const icons: Record<string, string> = {
            'GovernmentId': '🆔',
            'Financial': '💰',
            'Medical': '🏥',
            'Legal': '⚖️',
            'Employment': '💼',
            'Educational': '🎓',
            'Insurance': '🛡️',
            'Business': '📊',
            'Personal': '📝',
            'Unknown': '❓'
        };
        return icons[docType] || '📄';
    };

    const getConfidenceColor = (confidence: number): string => {
        if (confidence >= 0.8) return '#4CAF50'; // Green
        if (confidence >= 0.6) return '#FF9800'; // Orange
        return '#F44336'; // Red
    };

    return (
        <div className="document-type-detector">
            <div className="detector-header">
                <h2>📋 Document Type Detection</h2>
                <p>Upload a document to automatically detect its type using AI</p>
                <div className="status-indicator">
                    <span className={`status ${isInitialized ? 'ready' : 'initializing'}`}>
                        {isInitialized ? '✅ Ready' : '🔄 Initializing...'}
                    </span>
                </div>
            </div>

            {/* Configuration Panel */}
            <div className="config-panel">
                <h3>🔧 Detection Settings</h3>
                <div className="config-options">
                    <label>
                        <input
                            type="checkbox"
                            checked={config.enable_template_matching}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateConfig({...config, enable_template_matching: e.target.checked})}
                        />
                        Template Matching
                    </label>
                    <label>
                        <input
                            type="checkbox"
                            checked={config.enable_ml_classification}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateConfig({...config, enable_ml_classification: e.target.checked})}
                        />
                        ML Classification
                    </label>
                    <label>
                        Ensemble Method:
                        <select
                            value={config.ensemble_method}
                            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => updateConfig({...config, ensemble_method: e.target.value})}
                        >
                            <option value="adaptive">Adaptive</option>
                            <option value="weighted_average">Weighted Average</option>
                            <option value="majority_voting">Majority Voting</option>
                            <option value="confidence_based">Confidence Based</option>
                        </select>
                    </label>
                    <label>
                        Min Confidence:
                        <input
                            type="range"
                            min="0.1"
                            max="1.0"
                            step="0.1"
                            value={config.min_confidence}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateConfig({...config, min_confidence: parseFloat(e.target.value)})}
                        />
                        <span>{config.min_confidence.toFixed(1)}</span>
                    </label>
                </div>
            </div>

            {/* File Selection Buttons */}
            <div className="file-selection-buttons" style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
                    <button
                        onClick={handleSelectFiles}
                        style={{
                            flex: 1,
                            padding: '12px 16px',
                            backgroundColor: isInitialized ? '#007bff' : '#6c757d',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: isInitialized ? 'pointer' : 'not-allowed',
                            fontWeight: '500',
                            fontSize: '14px',
                            transition: 'background-color 0.2s'
                        }}
                        disabled={!isInitialized}
                        onMouseOver={(e) => {
                            if (isInitialized) e.currentTarget.style.backgroundColor = '#0056b3';
                        }}
                        onMouseOut={(e) => {
                            if (isInitialized) e.currentTarget.style.backgroundColor = '#007bff';
                        }}
                    >
                        📄 Select Image Files
                    </button>
                    <button
                        onClick={handleSelectFolder}
                        style={{
                            flex: 1,
                            padding: '12px 16px',
                            backgroundColor: isInitialized ? '#28a745' : '#6c757d',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: isInitialized ? 'pointer' : 'not-allowed',
                            fontWeight: '500',
                            fontSize: '14px',
                            transition: 'background-color 0.2s'
                        }}
                        disabled={!isInitialized}
                        onMouseOver={(e) => {
                            if (isInitialized) e.currentTarget.style.backgroundColor = '#1e7e34';
                        }}
                        onMouseOut={(e) => {
                            if (isInitialized) e.currentTarget.style.backgroundColor = '#28a745';
                        }}
                    >
                        📁 Select Folder
                    </button>
                </div>
                {selectedPaths.length > 0 && (
                    <div style={{ fontSize: '0.9rem', color: '#666', marginBottom: '0.5rem' }}>
                        Selected: {selectedPaths.map(path => path.split(/[/\\]/).pop()).join(', ')}
                    </div>
                )}
            </div>

            {/* File Upload Area */}
            <div 
                className={`upload-area ${dragActive ? 'drag-active' : ''} ${isProcessing ? 'processing' : ''} ${!isInitialized ? 'disabled' : ''}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => isInitialized && fileInputRef.current?.click()}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleInputChange}
                    style={{ display: 'none' }}
                    disabled={!isInitialized}
                />
                
                {isProcessing ? (
                    <div className="processing-indicator">
                        <div className="spinner"></div>
                        <p>Analyzing document...</p>
                        {processingProgress > 0 && (
                            <div className="progress-bar">
                                <div 
                                    className="progress-fill" 
                                    style={{ width: `${processingProgress}%` }}
                                ></div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="upload-prompt">
                        <div className="upload-icon">📄</div>
                        <p>{isInitialized ? 'Drop a document here or click to select' : 'Initializing detector...'}</p>
                        <small>Supports: JPG, PNG, WebP, BMP, TIFF (max 10MB)</small>
                    </div>
                )}
            </div>

            {/* Error Display */}
            {error && (
                <div className="error-message">
                    <span className="error-icon">❌</span>
                    <span>{error}</span>
                    <button onClick={() => setError(null)}>✕</button>
                </div>
            )}

            {/* Detection Results */}
            {detectionResult && (
                <div className="detection-results">
                    <h3>📊 Detection Results</h3>

                    {/* File Path Display */}
                    {currentFilePath && (
                        <div className="file-path-display" style={{
                            background: '#f8f9fa',
                            padding: '12px',
                            borderRadius: '8px',
                            marginBottom: '20px',
                            border: '1px solid #e9ecef'
                        }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <span style={{ fontSize: '1.2em' }}>📄</span>
                                <div>
                                    <div style={{ fontWeight: '600', color: '#2c3e50' }}>
                                        Scanned File: {currentFilePath}
                                    </div>
                                    <div style={{ fontSize: '0.9em', color: '#6c757d' }}>
                                        Analysis completed successfully
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="main-result">
                        <div className="document-type">
                            <span className="type-icon">{getDocumentTypeIcon(detectionResult.document_type)}</span>
                            <span className="type-name">{detectionResult.document_type}</span>
                            <span 
                                className="confidence-badge"
                                style={{ backgroundColor: getConfidenceColor(detectionResult.confidence) }}
                            >
                                {(detectionResult.confidence * 100).toFixed(1)}%
                            </span>
                        </div>
                    </div>

                    <div className="detailed-results">
                        <div className="result-section">
                            <h4>🔍 Method Breakdown</h4>
                            <div className="method-results">
                                <div className="method">
                                    <span>Template Matching:</span>
                                    <span>{(detectionResult.template_confidence * 100).toFixed(1)}%</span>
                                </div>
                                <div className="method">
                                    <span>ML Classification:</span>
                                    <span>{(detectionResult.ml_confidence * 100).toFixed(1)}%</span>
                                </div>
                                <div className="method">
                                    <span>Consensus Score:</span>
                                    <span>{(detectionResult.ensemble_details.consensus_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        </div>

                        <div className="result-section">
                            <h4>📐 Layout Analysis</h4>
                            <div className="layout-stats">
                                <div className="stat">
                                    <span>Text Blocks:</span>
                                    <span>{detectionResult.layout_analysis.text_blocks}</span>
                                </div>
                                <div className="stat">
                                    <span>Tables:</span>
                                    <span>{detectionResult.layout_analysis.tables}</span>
                                </div>
                                <div className="stat">
                                    <span>Form Fields:</span>
                                    <span>{detectionResult.layout_analysis.form_fields}</span>
                                </div>
                                <div className="stat">
                                    <span>Text Density:</span>
                                    <span>{(detectionResult.layout_analysis.text_density * 100).toFixed(1)}%</span>
                                </div>
                                <div className="stat">
                                    <span>Complexity:</span>
                                    <span>{(detectionResult.layout_analysis.complexity_score * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        </div>

                        <div className="result-section">
                            <h4>⚡ Performance</h4>
                            <div className="performance-stats">
                                <div className="stat">
                                    <span>Processing Time:</span>
                                    <span>{detectionResult.processing_time_ms}ms</span>
                                </div>
                                <div className="stat">
                                    <span>Template Weight:</span>
                                    <span>{(detectionResult.ensemble_details.template_weight * 100).toFixed(0)}%</span>
                                </div>
                                <div className="stat">
                                    <span>ML Weight:</span>
                                    <span>{(detectionResult.ensemble_details.ml_weight * 100).toFixed(0)}%</span>
                                </div>
                                <div className="stat">
                                    <span>Methods Agree:</span>
                                    <span>{detectionResult.ensemble_details.methods_agree ? '✅' : '❌'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Supported Types */}
            {supportedTypes.length > 0 && (
                <div className="supported-types">
                    <h3>📋 Supported Document Types</h3>
                    <div className="types-grid">
                        {supportedTypes.map((type, index) => (
                            <div key={index} className="type-item">
                                <span className="type-icon">{getDocumentTypeIcon(type)}</span>
                                <span className="type-name">{type}</span>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Action Buttons */}
            <div className="action-buttons">
                <button 
                    onClick={initializeDetector}
                    disabled={isProcessing}
                    className="init-button"
                >
                    🔄 Reinitialize Detector
                </button>
                
                <button 
                    onClick={() => {
                        setDetectionResult(null);
                        setError(null);
                        setPerformanceStats({});
                    }}
                    className="clear-button"
                >
                    🗑️ Clear Results
                </button>
            </div>
        </div>
    );
};

export default DocumentTypeDetector;
