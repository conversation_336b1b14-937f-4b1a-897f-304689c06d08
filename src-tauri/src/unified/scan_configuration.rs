/// Granular Scan Configuration System
/// 
/// This module provides fine-grained control over scan operations, allowing users
/// to selectively enable/disable specific detection types and processing methods
/// to optimize performance for their specific use cases.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;

/// Granular scan configuration with selective detection types
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct GranularScanConfig {
    /// Detection type configuration
    pub detection_types: DetectionTypeConfig,
    
    /// File processing method configuration
    pub processing_methods: ProcessingMethodConfig,
    
    /// Performance optimization settings
    pub performance_settings: PerformanceConfig,
    
    /// Output and filtering configuration
    pub output_config: OutputConfig,
    
    /// Configuration metadata
    pub metadata: ConfigMetadata,
}

/// Selective detection type configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct DetectionTypeConfig {
    /// Privacy-sensitive data detection
    pub privacy_detection: PrivacyDetectionConfig,
    
    /// Cryptocurrency detection
    pub cryptocurrency_detection: CryptocurrencyDetectionConfig,
    
    /// Government ID detection
    pub government_id_detection: GovernmentIdDetectionConfig,
    
    /// File integrity scanning
    pub file_integrity_detection: FileIntegrityDetectionConfig,
}

/// Privacy-sensitive data detection configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PrivacyDetectionConfig {
    /// Enable privacy detection module
    pub enabled: bool,
    
    /// Social Security Numbers
    pub ssn_detection: bool,
    
    /// Credit card numbers
    pub credit_card_detection: bool,
    
    /// Phone numbers
    pub phone_number_detection: bool,
    
    /// Email addresses
    pub email_detection: bool,
    
    /// Physical addresses
    pub address_detection: bool,
    
    /// Bank account numbers
    pub bank_account_detection: bool,
    
    /// Tax identification numbers
    pub tax_id_detection: bool,
    
    /// Medical record numbers
    pub medical_record_detection: bool,
}

/// Cryptocurrency detection configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CryptocurrencyDetectionConfig {
    /// Enable cryptocurrency detection module
    pub enabled: bool,
    
    /// Bitcoin addresses and private keys
    pub bitcoin_detection: bool,
    
    /// Ethereum addresses and private keys
    pub ethereum_detection: bool,
    
    /// Cardano (ADA) addresses
    pub cardano_detection: bool,
    
    /// World Mobile Token (WMT)
    pub wmt_detection: bool,
    
    /// AdaHandle detection
    pub ada_handle_detection: bool,
    
    /// Unstoppable Domains
    pub unstoppable_domains_detection: bool,
    
    /// Exchange API credentials
    pub exchange_credentials_detection: bool,
    
    /// Seed phrases and mnemonics
    pub seed_phrase_detection: bool,
}

/// Government ID detection configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct GovernmentIdDetectionConfig {
    /// Enable government ID detection module
    pub enabled: bool,
    
    /// US Social Security Numbers
    pub us_ssn_detection: bool,
    
    /// US driver's licenses
    pub us_drivers_license_detection: bool,
    
    /// US passport numbers
    pub us_passport_detection: bool,
    
    /// International passport numbers
    pub international_passport_detection: bool,
    
    /// European national IDs
    pub european_national_id_detection: bool,
    
    /// Canadian Social Insurance Numbers
    pub canadian_sin_detection: bool,
    
    /// UK National Insurance Numbers
    pub uk_national_insurance_detection: bool,
    
    /// Australian Tax File Numbers
    pub australian_tfn_detection: bool,
}

/// File integrity detection configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FileIntegrityDetectionConfig {
    /// Enable file integrity detection module
    pub enabled: bool,
    
    /// File corruption detection
    pub corruption_detection: bool,
    
    /// Duplicate file detection
    pub duplicate_detection: bool,
    
    /// File metadata analysis
    pub metadata_analysis: bool,
    
    /// File hash verification
    pub hash_verification: bool,
}

/// File processing method configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ProcessingMethodConfig {
    /// OCR text extraction configuration
    pub ocr_processing: OcrProcessingConfig,
    
    /// AI visual detection configuration
    pub ai_visual_detection: AiVisualDetectionConfig,
    
    /// Metadata extraction configuration
    pub metadata_extraction: MetadataExtractionConfig,
    
    /// Binary file analysis configuration
    pub binary_analysis: BinaryAnalysisConfig,
}

/// OCR processing configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct OcrProcessingConfig {
    /// Enable OCR processing
    pub enabled: bool,
    
    /// OCR for images
    pub image_ocr: bool,
    
    /// OCR for PDFs
    pub pdf_ocr: bool,
    
    /// OCR language selection
    pub languages: Vec<String>,
    
    /// OCR accuracy vs speed trade-off (1-10, higher = more accurate but slower)
    pub accuracy_level: u8,
    
    /// Maximum image size for OCR (MB)
    pub max_image_size_mb: u64,
}

/// AI visual detection configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AiVisualDetectionConfig {
    /// Enable AI visual detection
    pub enabled: bool,
    
    /// Privacy content classification
    pub privacy_classification: bool,
    
    /// Face detection
    pub face_detection: bool,
    
    /// Text region detection
    pub text_detection: bool,
    
    /// Document type classification
    pub document_classification: bool,
    
    /// Confidence threshold (0.0-1.0)
    pub confidence_threshold: f32,
}

/// Metadata extraction configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct MetadataExtractionConfig {
    /// Enable metadata extraction
    pub enabled: bool,
    
    /// EXIF data extraction from images
    pub exif_extraction: bool,
    
    /// GPS coordinate extraction
    pub gps_extraction: bool,
    
    /// Document author and creation info
    pub document_metadata: bool,
    
    /// File system metadata
    pub filesystem_metadata: bool,
    
    /// Hidden metadata analysis
    pub hidden_metadata: bool,
}

/// Binary file analysis configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct BinaryAnalysisConfig {
    /// Enable binary file analysis
    pub enabled: bool,
    
    /// Executable file analysis
    pub executable_analysis: bool,
    
    /// Archive extraction and analysis
    pub archive_analysis: bool,
    
    /// Database file analysis
    pub database_analysis: bool,
    
    /// String extraction from binaries
    pub string_extraction: bool,
    
    /// Maximum binary file size (MB)
    pub max_binary_size_mb: u64,
}

/// Performance optimization settings
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PerformanceConfig {
    /// Performance mode selection
    pub mode: PerformanceMode,
    
    /// Maximum processing time per file (milliseconds)
    pub max_processing_time_ms: u64,
    
    /// Maximum memory usage (MB)
    pub max_memory_usage_mb: u64,
    
    /// Enable parallel processing
    pub parallel_processing: bool,
    
    /// Number of worker threads (0 = auto-detect)
    pub worker_threads: u32,
    
    /// Enable intelligent caching
    pub enable_caching: bool,
    
    /// Cache size limit (MB)
    pub cache_size_mb: u64,
}

/// Performance mode selection
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PerformanceMode {
    /// Maximum speed, minimal accuracy
    Fast,
    
    /// Balanced speed and accuracy
    Balanced,
    
    /// Maximum accuracy, slower processing
    Comprehensive,
    
    /// Custom performance settings
    Custom,
}

/// Output configuration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct OutputConfig {
    /// Output format selection
    pub format: OutputFormat,
    
    /// Minimum severity level to include
    pub min_severity: SeverityLevel,
    
    /// Maximum number of results per file
    pub max_results_per_file: Option<u32>,
    
    /// Include confidence scores
    pub include_confidence: bool,
    
    /// Include processing time metrics
    pub include_timing: bool,
    
    /// Include file metadata in results
    pub include_metadata: bool,
}

/// Output format options
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OutputFormat {
    /// All findings from enabled modules
    Full,
    
    /// Only privacy-related findings
    PrivacyOnly,
    
    /// Only security-related findings
    SecurityOnly,
    
    /// Only high-risk findings
    HighRiskOnly,
    
    /// Only file integrity findings
    IntegrityOnly,
    
    /// Summary statistics only
    SummaryOnly,
    
    /// Custom filtering rules
    Custom(CustomOutputFilter),
}

/// Custom output filtering
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct CustomOutputFilter {
    /// Include specific detection types
    pub include_types: Vec<String>,
    
    /// Exclude specific detection types
    pub exclude_types: Vec<String>,
    
    /// Minimum confidence threshold
    pub min_confidence: f32,
    
    /// Custom severity filtering
    pub severity_filter: Vec<SeverityLevel>,
}

/// Severity levels for findings
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum SeverityLevel {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

/// Configuration metadata
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ConfigMetadata {
    /// Configuration name
    pub name: String,
    
    /// Configuration description
    pub description: String,
    
    /// Configuration version
    pub version: String,
    
    /// Creation timestamp
    pub created_at: String,
    
    /// Last modified timestamp
    pub modified_at: String,
    
    /// Configuration tags
    pub tags: Vec<String>,
    
    /// Estimated performance impact
    pub performance_estimate: PerformanceEstimate,
}

/// Performance impact estimation
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct PerformanceEstimate {
    /// Estimated processing time per file (milliseconds)
    pub estimated_time_ms: u64,
    
    /// Estimated memory usage (MB)
    pub estimated_memory_mb: u64,
    
    /// Estimated throughput (files per minute)
    pub estimated_throughput: u32,
    
    /// Performance impact score (1-10, higher = more resource intensive)
    pub impact_score: u8,
}

impl Default for GranularScanConfig {
    fn default() -> Self {
        Self {
            detection_types: DetectionTypeConfig::default(),
            processing_methods: ProcessingMethodConfig::default(),
            performance_settings: PerformanceConfig::default(),
            output_config: OutputConfig::default(),
            metadata: ConfigMetadata::default(),
        }
    }
}

impl Default for DetectionTypeConfig {
    fn default() -> Self {
        Self {
            privacy_detection: PrivacyDetectionConfig::default(),
            cryptocurrency_detection: CryptocurrencyDetectionConfig::default(),
            government_id_detection: GovernmentIdDetectionConfig::default(),
            file_integrity_detection: FileIntegrityDetectionConfig::default(),
        }
    }
}

impl Default for PrivacyDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            ssn_detection: true,
            credit_card_detection: true,
            phone_number_detection: true,
            email_detection: true,
            address_detection: true,
            bank_account_detection: true,
            tax_id_detection: true,
            medical_record_detection: true,
        }
    }
}

impl Default for CryptocurrencyDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            bitcoin_detection: true,
            ethereum_detection: true,
            cardano_detection: true,
            wmt_detection: true,
            ada_handle_detection: true,
            unstoppable_domains_detection: true,
            exchange_credentials_detection: true,
            seed_phrase_detection: true,
        }
    }
}

impl Default for GovernmentIdDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            us_ssn_detection: true,
            us_drivers_license_detection: true,
            us_passport_detection: true,
            international_passport_detection: true,
            european_national_id_detection: false,
            canadian_sin_detection: false,
            uk_national_insurance_detection: false,
            australian_tfn_detection: false,
        }
    }
}

impl Default for FileIntegrityDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            corruption_detection: true,
            duplicate_detection: true,
            metadata_analysis: true,
            hash_verification: true,
        }
    }
}

impl Default for ProcessingMethodConfig {
    fn default() -> Self {
        Self {
            ocr_processing: OcrProcessingConfig::default(),
            ai_visual_detection: AiVisualDetectionConfig::default(),
            metadata_extraction: MetadataExtractionConfig::default(),
            binary_analysis: BinaryAnalysisConfig::default(),
        }
    }
}

impl Default for OcrProcessingConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            image_ocr: true,
            pdf_ocr: true,
            languages: vec!["eng".to_string()],
            accuracy_level: 7,
            max_image_size_mb: 50,
        }
    }
}

impl Default for AiVisualDetectionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            privacy_classification: true,
            face_detection: true,
            text_detection: true,
            document_classification: true,
            confidence_threshold: 0.7,
        }
    }
}

impl Default for MetadataExtractionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            exif_extraction: true,
            gps_extraction: true,
            document_metadata: true,
            filesystem_metadata: true,
            hidden_metadata: true,
        }
    }
}

impl Default for BinaryAnalysisConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            executable_analysis: true,
            archive_analysis: true,
            database_analysis: true,
            string_extraction: true,
            max_binary_size_mb: 500,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            mode: PerformanceMode::Balanced,
            max_processing_time_ms: 5000,
            max_memory_usage_mb: 1024,
            parallel_processing: true,
            worker_threads: 0, // Auto-detect
            enable_caching: true,
            cache_size_mb: 100,
        }
    }
}

impl Default for OutputConfig {
    fn default() -> Self {
        Self {
            format: OutputFormat::Full,
            min_severity: SeverityLevel::Low,
            max_results_per_file: None,
            include_confidence: true,
            include_timing: true,
            include_metadata: false,
        }
    }
}

impl Default for ConfigMetadata {
    fn default() -> Self {
        Self {
            name: "Default Configuration".to_string(),
            description: "Comprehensive scanning with all detection types enabled".to_string(),
            version: "1.0".to_string(),
            created_at: chrono::Utc::now().to_rfc3339(),
            modified_at: chrono::Utc::now().to_rfc3339(),
            tags: vec!["default".to_string(), "comprehensive".to_string()],
            performance_estimate: PerformanceEstimate::default(),
        }
    }
}

impl Default for PerformanceEstimate {
    fn default() -> Self {
        Self {
            estimated_time_ms: 800,
            estimated_memory_mb: 100,
            estimated_throughput: 75,
            impact_score: 8,
        }
    }
}
