use tauri_appprivacy_ai_lib::commands::{
    greet,
    get_system_info,
    initialize_privacy_engine,
    initialize_ocr_engine,
    scan_file_for_privacy,
    quick_privacy_assessment,
    scan_directory_for_duplicates
};
use tempfile::{TempDir, NamedTempFile};
use std::fs;
use std::io::Write;

#[tokio::test]
async fn test_greet_command() {
    let result = greet("Test User");
    assert!(result.contains("Test User"));
    assert!(result.contains("PrivacyAI"));
}

#[tokio::test]
async fn test_greet_command_empty_name() {
    let result = greet("");
    assert!(result.contains("PrivacyAI"));
}

#[tokio::test]
async fn test_greet_command_special_characters() {
    let result = greet("Test User 123!@#");
    assert!(result.contains("Test User 123!@#"));
}

#[tokio::test]
async fn test_system_info_command() {
    let result = get_system_info().await.unwrap();

    assert!(result.ocr_available);
    assert!(!result.supported_image_formats.is_empty());
    assert!(!result.supported_document_formats.is_empty());
    assert!(result.max_file_size_mb > 0);

    // Verify specific formats are supported
    assert!(result.supported_image_formats.contains(&"jpg".to_string()));
    assert!(result.supported_image_formats.contains(&"png".to_string()));
    assert!(result.supported_document_formats.contains(&"pdf".to_string()));
    assert!(result.supported_document_formats.contains(&"txt".to_string()));
}

#[tokio::test]
async fn test_initialize_privacy_engine() {
    let result = initialize_privacy_engine().await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_initialize_ocr_engine() {
    let result = initialize_ocr_engine().await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_scan_file_for_privacy_text_file() {
    // Create a temporary text file with sensitive data
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Contact Information:").unwrap();
    writeln!(temp_file, "Email: <EMAIL>").unwrap();
    writeln!(temp_file, "Phone: (*************").unwrap();
    writeln!(temp_file, "SSN: ***********").unwrap();

    let file_path = temp_file.path().to_string_lossy().to_string();
    let result = scan_file_for_privacy(file_path).await;

    assert!(result.is_ok());
    let scan_result = result.unwrap();

    assert!(!scan_result.file_path.is_empty());
    assert!(scan_result.findings.len() >= 2); // Should find email and phone at minimum
    assert!(scan_result.risk_score > 0.0);
    assert!(scan_result.processing_time_ms > 0);
    assert!(scan_result.file_size > 0);
}

#[tokio::test]
async fn test_scan_file_for_privacy_empty_file() {
    let temp_file = NamedTempFile::new().unwrap();
    let file_path = temp_file.path().to_string_lossy().to_string();

    let result = scan_file_for_privacy(file_path).await;
    assert!(result.is_ok());

    let scan_result = result.unwrap();
    assert_eq!(scan_result.findings.len(), 0);
    assert_eq!(scan_result.risk_score, 0.0);
}

#[tokio::test]
async fn test_scan_file_for_privacy_nonexistent_file() {
    let result = scan_file_for_privacy("nonexistent_file.txt".to_string()).await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_scan_file_for_privacy_unsupported_format() {
    let temp_dir = TempDir::new().unwrap();
    let unsupported_file = temp_dir.path().join("test.xyz");
    fs::write(&unsupported_file, "test content").unwrap();

    let file_path = unsupported_file.to_string_lossy().to_string();
    let result = scan_file_for_privacy(file_path).await;

    assert!(result.is_err());
}

#[tokio::test]
async fn test_quick_privacy_assessment() {
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Email: <EMAIL>").unwrap();

    let file_path = temp_file.path().to_string_lossy().to_string();
    let result = quick_privacy_assessment(file_path).await;

    assert!(result.is_ok());
    let assessment = result.unwrap();

    assert!(assessment.risk_level > 0);
    assert!(!assessment.risk_description.is_empty());
    assert!(assessment.confidence > 0.0);
    assert!(assessment.processing_time_ms > 0);
}

#[tokio::test]
async fn test_quick_privacy_assessment_clean_file() {
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "This is just normal text with no sensitive data.").unwrap();

    let file_path = temp_file.path().to_string_lossy().to_string();
    let result = quick_privacy_assessment(file_path).await;

    assert!(result.is_ok());
    let assessment = result.unwrap();

    assert_eq!(assessment.risk_level, 0);
    assert!(assessment.risk_description.contains("No privacy risks"));
}

#[tokio::test]
async fn test_scan_directory_for_duplicates() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create some duplicate files
    let content = "duplicate content for testing";
    fs::write(temp_path.join("file1.txt"), content).unwrap();
    fs::write(temp_path.join("file2.txt"), content).unwrap();
    fs::write(temp_path.join("unique.txt"), "unique content").unwrap();

    let dir_path = temp_path.to_string_lossy().to_string();
    let result = scan_directory_for_duplicates(dir_path).await;

    assert!(result.is_ok());
    let duplicate_result = result.unwrap();

    assert_eq!(duplicate_result.duplicate_groups.len(), 1);
    assert_eq!(duplicate_result.total_duplicates, 2);
    assert_eq!(duplicate_result.total_files_scanned, 3);
    assert!(duplicate_result.processing_time_ms > 0);
}

#[tokio::test]
async fn test_scan_directory_for_duplicates_empty_directory() {
    let temp_dir = TempDir::new().unwrap();
    let dir_path = temp_dir.path().to_string_lossy().to_string();

    let result = scan_directory_for_duplicates(dir_path).await;
    assert!(result.is_ok());

    let duplicate_result = result.unwrap();
    assert_eq!(duplicate_result.duplicate_groups.len(), 0);
    assert_eq!(duplicate_result.total_duplicates, 0);
    assert_eq!(duplicate_result.total_files_scanned, 0);
}

#[tokio::test]
async fn test_scan_directory_for_duplicates_nonexistent() {
    let result = scan_directory_for_duplicates("nonexistent_directory".to_string()).await;
    assert!(result.is_err());
}
