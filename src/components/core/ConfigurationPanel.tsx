import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import './ConfigurationPanel.css';

// Types for configuration (matching backend structure)
interface GranularScanConfig {
    detection_types: DetectionTypeConfig;
    processing_methods: ProcessingMethodConfig;
    performance_settings: PerformanceConfig;
    output_config: OutputConfig;
    metadata: ConfigMetadata;
    // Additional top-level properties for UI convenience
    detection_sensitivity: number;
    performance_mode: 'fast' | 'balanced' | 'thorough';
    enable_ai_enhancement: boolean;
    confidence_threshold: number;
}

interface DetectionTypeConfig {
    privacy_detection: PrivacyDetectionConfig;
    cryptocurrency_detection: CryptocurrencyDetectionConfig;
    government_id_detection: GovernmentIdDetectionConfig;
    file_integrity_detection: FileIntegrityDetectionConfig;
}

interface PrivacyDetectionConfig {
    enabled: boolean;
    ssn_detection: boolean;
    credit_card_detection: boolean;
    email_detection: boolean;
    phone_detection: boolean;
    custom_patterns: boolean;
    confidence_threshold: number;
}

interface CryptocurrencyDetectionConfig {
    enabled: boolean;
    bitcoin_detection: boolean;
    ethereum_detection: boolean;
    cardano_detection: boolean;
    wmt_detection: boolean;
    ada_handle_detection: boolean;
    unstoppable_domains_detection: boolean;
    exchange_credentials_detection: boolean;
    seed_phrase_detection: boolean;
}

interface GovernmentIdDetectionConfig {
    enabled: boolean;
    passport_detection: boolean;
    drivers_license_detection: boolean;
    national_id_detection: boolean;
    tax_id_detection: boolean;
}

interface FileIntegrityDetectionConfig {
    enabled: boolean;
    corruption_detection: boolean;
    duplicate_detection: boolean;
    metadata_analysis: boolean;
    hash_verification: boolean;
}

interface ProcessingMethodConfig {
    ocr_processing: OcrProcessingConfig;
    ai_visual_detection: AiVisualDetectionConfig;
    metadata_extraction: MetadataExtractionConfig;
    binary_analysis: BinaryAnalysisConfig;
}

interface OcrProcessingConfig {
    enabled: boolean;
    image_ocr: boolean;
    pdf_ocr: boolean;
    languages: string[];
    accuracy_level: number;
    max_image_size_mb: number;
}

interface AiVisualDetectionConfig {
    enabled: boolean;
    privacy_classification: boolean;
    face_detection: boolean;
    text_detection: boolean;
    document_classification: boolean;
    confidence_threshold: number;
}

interface MetadataExtractionConfig {
    enabled: boolean;
    exif_data: boolean;
    document_properties: boolean;
    file_timestamps: boolean;
    author_information: boolean;
}

interface BinaryAnalysisConfig {
    enabled: boolean;
    embedded_files: boolean;
    steganography_detection: boolean;
    malware_scanning: boolean;
    entropy_analysis: boolean;
}

interface PerformanceConfig {
    max_concurrent_files: number;
    memory_limit_mb: number;
    timeout_seconds: number;
    cache_enabled: boolean;
    performance_mode: 'fast' | 'balanced' | 'thorough';
}

interface OutputConfig {
    format: 'json' | 'csv' | 'xml';
    include_metadata: boolean;
    include_context: boolean;
    severity_filter: 'all' | 'low' | 'medium' | 'high' | 'critical';
    max_results: number;
}

interface ConfigMetadata {
    name: string;
    description: string;
    version: string;
    created_at: string;
    modified_at: string;
}

interface SecureOperationsConfig {
    overwrite_passes: number;
    use_random_data: boolean;
    verify_deletion: boolean;
    max_file_size: number;
    archive_compression: 'None' | 'Fast' | 'Balanced' | 'Maximum';
    encryption_algorithm: 'AES256GCM' | 'ChaCha20Poly1305';
    secure_temp_files: boolean;
    temp_directory?: string;
}

interface OCRConfig {
    language: string;
    enable_preprocessing: boolean;
    confidence_threshold: number;
    enable_text_enhancement: boolean;
    dpi_threshold: number;
}

interface ConfigurationPanelProps {
    onConfigChange?: (config: any) => void;
    initialConfig?: any;
}

const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
    onConfigChange,
    // initialConfig // TODO: Implement initial configuration loading
}) => {
    const [activeTab, setActiveTab] = useState<'privacy' | 'crypto' | 'processing' | 'performance' | 'security' | 'ocr'>('privacy');
    const [scanConfig, setScanConfig] = useState<GranularScanConfig>({
        detection_types: {
            privacy_detection: {
                enabled: true,
                ssn_detection: true,
                credit_card_detection: true,
                email_detection: true,
                phone_detection: true,
                custom_patterns: false,
                confidence_threshold: 0.8,
            },
            cryptocurrency_detection: {
                enabled: true,
                bitcoin_detection: true,
                ethereum_detection: true,
                cardano_detection: true,
                wmt_detection: true,
                ada_handle_detection: true,
                unstoppable_domains_detection: true,
                exchange_credentials_detection: true,
                seed_phrase_detection: true,
            },
            government_id_detection: {
                enabled: true,
                passport_detection: true,
                drivers_license_detection: true,
                national_id_detection: true,
                tax_id_detection: true,
            },
            file_integrity_detection: {
                enabled: true,
                corruption_detection: true,
                duplicate_detection: true,
                metadata_analysis: true,
                hash_verification: true,
            },
        },
        processing_methods: {
            ocr_processing: {
                enabled: true,
                image_ocr: true,
                pdf_ocr: true,
                languages: ['eng'],
                accuracy_level: 7,
                max_image_size_mb: 50,
            },
            ai_visual_detection: {
                enabled: true,
                privacy_classification: true,
                face_detection: true,
                text_detection: true,
                document_classification: true,
                confidence_threshold: 0.7,
            },
            metadata_extraction: {
                enabled: true,
                exif_data: true,
                document_properties: true,
                file_timestamps: true,
                author_information: true,
            },
            binary_analysis: {
                enabled: false,
                embedded_files: false,
                steganography_detection: false,
                malware_scanning: false,
                entropy_analysis: false,
            },
        },
        performance_settings: {
            max_concurrent_files: 10,
            memory_limit_mb: 1024,
            timeout_seconds: 300,
            cache_enabled: true,
            performance_mode: 'balanced',
        },
        output_config: {
            format: 'json',
            include_metadata: true,
            include_context: true,
            severity_filter: 'all',
            max_results: 1000,
        },
        metadata: {
            name: 'Default Configuration',
            description: 'Default scan configuration',
            version: '1.0.0',
            created_at: new Date().toISOString(),
            modified_at: new Date().toISOString(),
        },
        // Additional top-level properties for UI convenience
        detection_sensitivity: 0.8,
        performance_mode: 'balanced',
        enable_ai_enhancement: true,
        confidence_threshold: 0.8,
    });
    
    const [securityConfig, setSecurityConfig] = useState<SecureOperationsConfig>({
        overwrite_passes: 7,
        use_random_data: true,
        verify_deletion: true,
        max_file_size: 1024 * 1024 * 1024, // 1GB
        archive_compression: 'Balanced',
        encryption_algorithm: 'AES256GCM',
        secure_temp_files: true,
    });
    
    const [ocrConfig, setOCRConfig] = useState<OCRConfig>({
        language: 'eng',
        enable_preprocessing: true,
        confidence_threshold: 0.6,
        enable_text_enhancement: true,
        dpi_threshold: 150,
    });
    
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

    // Load initial configuration
    useEffect(() => {
        loadConfiguration();
    }, []);

    const loadConfiguration = async () => {
        try {
            setIsLoading(true);

            // Load current scan configuration
            const configResponse = await invoke<GranularScanConfig | null>('get_current_scan_config');
            if (configResponse) {
                setScanConfig(configResponse);
            }

            // Load security configuration
            const securityResponse = await invoke<SecureOperationsConfig>('get_default_secure_config');
            if (securityResponse) {
                setSecurityConfig(securityResponse);
            }

        } catch (error) {
            console.error('Failed to load configuration:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const saveConfiguration = async () => {
        try {
            setSaveStatus('saving');

            // Update scan configuration
            await invoke('update_scan_config', { config: scanConfig });

            // Validate and save security configuration
            await invoke('validate_secure_config', { config: securityConfig });

            // Notify parent component
            if (onConfigChange) {
                onConfigChange({
                    scan: scanConfig,
                    security: securityConfig,
                    ocr: ocrConfig,
                });
            }

            setSaveStatus('saved');
            setTimeout(() => setSaveStatus('idle'), 2000);

        } catch (error) {
            console.error('Failed to save configuration:', error);
            setSaveStatus('error');
            setTimeout(() => setSaveStatus('idle'), 3000);
        }
    };

    const resetToDefaults = async () => {
        if (window.confirm('Reset all settings to defaults? This cannot be undone.')) {
            await loadConfiguration();
        }
    };

    const renderPrivacyConfig = () => (
        <div className="config-section">
            <h3>Privacy Detection Settings</h3>

            <div className="config-group">
                <h4>Privacy Data Detection</h4>
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={scanConfig.detection_types.privacy_detection.enabled}
                        onChange={(e) => setScanConfig({
                            ...scanConfig,
                            detection_types: {
                                ...scanConfig.detection_types,
                                privacy_detection: {
                                    ...scanConfig.detection_types.privacy_detection,
                                    enabled: e.target.checked
                                }
                            }
                        })}
                    />
                    <span>Enable Privacy Detection</span>
                </label>

                {scanConfig.detection_types.privacy_detection.enabled && (
                    <div className="checkbox-grid">
                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={scanConfig.detection_types.privacy_detection.ssn_detection}
                                onChange={(e) => setScanConfig({
                                    ...scanConfig,
                                    detection_types: {
                                        ...scanConfig.detection_types,
                                        privacy_detection: {
                                            ...scanConfig.detection_types.privacy_detection,
                                            ssn_detection: e.target.checked
                                        }
                                    }
                                })}
                            />
                            <span>Social Security Numbers</span>
                        </label>

                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={scanConfig.detection_types.privacy_detection.credit_card_detection}
                                onChange={(e) => setScanConfig({
                                    ...scanConfig,
                                    detection_types: {
                                        ...scanConfig.detection_types,
                                        privacy_detection: {
                                            ...scanConfig.detection_types.privacy_detection,
                                            credit_card_detection: e.target.checked
                                        }
                                    }
                                })}
                            />
                            <span>Credit Card Numbers</span>
                        </label>

                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={scanConfig.detection_types.privacy_detection.email_detection}
                                onChange={(e) => setScanConfig({
                                    ...scanConfig,
                                    detection_types: {
                                        ...scanConfig.detection_types,
                                        privacy_detection: {
                                            ...scanConfig.detection_types.privacy_detection,
                                            email_detection: e.target.checked
                                        }
                                    }
                                })}
                            />
                            <span>Email Addresses</span>
                        </label>

                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={scanConfig.detection_types.privacy_detection.phone_detection}
                                onChange={(e) => setScanConfig({
                                    ...scanConfig,
                                    detection_types: {
                                        ...scanConfig.detection_types,
                                        privacy_detection: {
                                            ...scanConfig.detection_types.privacy_detection,
                                            phone_detection: e.target.checked
                                        }
                                    }
                                })}
                            />
                            <span>Phone Numbers</span>
                        </label>
                    </div>
                )}

                {scanConfig.detection_types.privacy_detection.enabled && (
                    <div className="slider-container">
                        <label>Privacy Detection Confidence: {(scanConfig.detection_types.privacy_detection.confidence_threshold * 100).toFixed(0)}%</label>
                        <input
                            type="range"
                            min="0.1"
                            max="1.0"
                            step="0.1"
                            value={scanConfig.detection_types.privacy_detection.confidence_threshold}
                            onChange={(e) => setScanConfig({
                                ...scanConfig,
                                detection_types: {
                                    ...scanConfig.detection_types,
                                    privacy_detection: {
                                        ...scanConfig.detection_types.privacy_detection,
                                        confidence_threshold: parseFloat(e.target.value)
                                    }
                                }
                            })}
                            className="confidence-slider"
                        />
                    </div>
                )}
            </div>
            
            <div className="config-group">
                <h4>Detection Sensitivity</h4>
                <div className="slider-container">
                    <label>Sensitivity: {(scanConfig.detection_sensitivity * 100).toFixed(0)}%</label>
                    <input
                        type="range"
                        min="0.1"
                        max="1.0"
                        step="0.1"
                        value={scanConfig.detection_sensitivity}
                        onChange={(e) => setScanConfig({
                            ...scanConfig,
                            detection_sensitivity: parseFloat(e.target.value)
                        })}
                        className="sensitivity-slider"
                    />
                    <div className="slider-labels">
                        <span>Low (Fast)</span>
                        <span>High (Thorough)</span>
                    </div>
                </div>
            </div>
            
            <div className="config-group">
                <h4>Performance Mode</h4>
                <div className="radio-group">
                    {(['fast', 'balanced', 'thorough'] as const).map((mode) => (
                        <label key={mode} className="radio-item">
                            <input
                                type="radio"
                                name="performance_mode"
                                value={mode}
                                checked={scanConfig.performance_mode === mode}
                                onChange={(e) => setScanConfig({
                                    ...scanConfig,
                                    performance_mode: e.target.value as any
                                })}
                            />
                            <span>{mode.charAt(0).toUpperCase() + mode.slice(1)}</span>
                        </label>
                    ))}
                </div>
            </div>
            
            <div className="config-group">
                <h4>AI Enhancement</h4>
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={scanConfig.enable_ai_enhancement}
                        onChange={(e) => setScanConfig({
                            ...scanConfig,
                            enable_ai_enhancement: e.target.checked
                        })}
                    />
                    <span>Enable AI-enhanced detection (improves accuracy)</span>
                </label>
                
                {scanConfig.enable_ai_enhancement && (
                    <div className="slider-container">
                        <label>AI Confidence Threshold: {(scanConfig.confidence_threshold * 100).toFixed(0)}%</label>
                        <input
                            type="range"
                            min="0.1"
                            max="1.0"
                            step="0.1"
                            value={scanConfig.confidence_threshold}
                            onChange={(e) => setScanConfig({
                                ...scanConfig,
                                confidence_threshold: parseFloat(e.target.value)
                            })}
                            className="confidence-slider"
                        />
                    </div>
                )}
            </div>
        </div>
    );

    const renderSecurityConfig = () => (
        <div className="config-section">
            <h3>Secure Operations Settings</h3>
            
            <div className="config-group">
                <h4>Secure Deletion (DoD 5220.22-M)</h4>
                <div className="slider-container">
                    <label>Overwrite Passes: {securityConfig.overwrite_passes} (DoD Standard: 7)</label>
                    <input
                        type="range"
                        min="1"
                        max="35"
                        step="1"
                        value={securityConfig.overwrite_passes}
                        onChange={(e) => setSecurityConfig({
                            ...securityConfig,
                            overwrite_passes: parseInt(e.target.value)
                        })}
                        className="passes-slider"
                    />
                    <div className="slider-labels">
                        <span>1 (Fast)</span>
                        <span>7 (DoD)</span>
                        <span>35 (Maximum)</span>
                    </div>
                </div>
                
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={securityConfig.verify_deletion}
                        onChange={(e) => setSecurityConfig({
                            ...securityConfig,
                            verify_deletion: e.target.checked
                        })}
                    />
                    <span>Verify deletion completion</span>
                </label>
            </div>
            
            <div className="config-group">
                <h4>Archive Encryption</h4>
                <div className="select-container">
                    <label>Encryption Algorithm:</label>
                    <select
                        value={securityConfig.encryption_algorithm}
                        onChange={(e) => setSecurityConfig({
                            ...securityConfig,
                            encryption_algorithm: e.target.value as any
                        })}
                    >
                        <option value="AES256GCM">AES-256-GCM (Recommended)</option>
                        <option value="ChaCha20Poly1305">ChaCha20-Poly1305 (High Performance)</option>
                    </select>
                </div>
                
                <div className="select-container">
                    <label>Compression Level:</label>
                    <select
                        value={securityConfig.archive_compression}
                        onChange={(e) => setSecurityConfig({
                            ...securityConfig,
                            archive_compression: e.target.value as any
                        })}
                    >
                        <option value="None">None (Fastest)</option>
                        <option value="Fast">Fast</option>
                        <option value="Balanced">Balanced (Recommended)</option>
                        <option value="Maximum">Maximum (Smallest)</option>
                    </select>
                </div>
            </div>
            
            <div className="config-group">
                <h4>Security Options</h4>
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={securityConfig.secure_temp_files}
                        onChange={(e) => setSecurityConfig({
                            ...securityConfig,
                            secure_temp_files: e.target.checked
                        })}
                    />
                    <span>Secure temporary file handling</span>
                </label>
            </div>
        </div>
    );

    const renderOCRConfig = () => (
        <div className="config-section">
            <h3>OCR Processing Settings</h3>
            
            <div className="config-group">
                <h4>Language Settings</h4>
                <div className="select-container">
                    <label>OCR Language:</label>
                    <select
                        value={ocrConfig.language}
                        onChange={(e) => setOCRConfig({
                            ...ocrConfig,
                            language: e.target.value
                        })}
                    >
                        <option value="eng">English</option>
                        <option value="spa">Spanish</option>
                        <option value="fra">French</option>
                        <option value="deu">German</option>
                        <option value="chi_sim">Chinese (Simplified)</option>
                        <option value="jpn">Japanese</option>
                    </select>
                </div>
            </div>
            
            <div className="config-group">
                <h4>Processing Options</h4>
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={ocrConfig.enable_preprocessing}
                        onChange={(e) => setOCRConfig({
                            ...ocrConfig,
                            enable_preprocessing: e.target.checked
                        })}
                    />
                    <span>Enable image preprocessing (improves accuracy)</span>
                </label>
                
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={ocrConfig.enable_text_enhancement}
                        onChange={(e) => setOCRConfig({
                            ...ocrConfig,
                            enable_text_enhancement: e.target.checked
                        })}
                    />
                    <span>Enable text enhancement</span>
                </label>
                
                <div className="slider-container">
                    <label>OCR Confidence Threshold: {(ocrConfig.confidence_threshold * 100).toFixed(0)}%</label>
                    <input
                        type="range"
                        min="0.1"
                        max="1.0"
                        step="0.1"
                        value={ocrConfig.confidence_threshold}
                        onChange={(e) => setOCRConfig({
                            ...ocrConfig,
                            confidence_threshold: parseFloat(e.target.value)
                        })}
                        className="confidence-slider"
                    />
                </div>
            </div>
        </div>
    );

    if (isLoading) {
        return (
            <div className="configuration-panel loading">
                <div className="loading-spinner"></div>
                <p>Loading configuration...</p>
            </div>
        );
    }

    return (
        <div className="configuration-panel">
            <div className="config-header">
                <h2>⚙️ Configuration Settings</h2>
                <p>Customize PrivacyAI's detection, security, and processing settings</p>
            </div>
            
            <div className="config-tabs">
                <button
                    className={`tab-button ${activeTab === 'privacy' ? 'active' : ''}`}
                    onClick={() => setActiveTab('privacy')}
                >
                    🔍 Privacy Detection
                </button>
                <button
                    className={`tab-button ${activeTab === 'security' ? 'active' : ''}`}
                    onClick={() => setActiveTab('security')}
                >
                    🔒 Security Operations
                </button>
                <button
                    className={`tab-button ${activeTab === 'ocr' ? 'active' : ''}`}
                    onClick={() => setActiveTab('ocr')}
                >
                    📄 OCR Processing
                </button>
            </div>
            
            <div className="config-content">
                {activeTab === 'privacy' && renderPrivacyConfig()}
                {activeTab === 'security' && renderSecurityConfig()}
                {activeTab === 'ocr' && renderOCRConfig()}
            </div>
            
            <div className="config-actions">
                <button
                    className="reset-button"
                    onClick={resetToDefaults}
                    disabled={saveStatus === 'saving'}
                >
                    Reset to Defaults
                </button>
                
                <button
                    className={`save-button ${saveStatus}`}
                    onClick={saveConfiguration}
                    disabled={saveStatus === 'saving'}
                >
                    {saveStatus === 'saving' && '⏳ Saving...'}
                    {saveStatus === 'saved' && '✅ Saved'}
                    {saveStatus === 'error' && '❌ Error'}
                    {saveStatus === 'idle' && '💾 Save Configuration'}
                </button>
            </div>
        </div>
    );
};

export default ConfigurationPanel;
