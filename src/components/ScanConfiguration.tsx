import React from 'react';
import SimpleConfigurationPanel from './core/SimpleConfigurationPanel';

// This component now uses the new SimpleConfigurationPanel
const ScanConfiguration: React.FC = () => {
  return (
    <div className="scan-configuration-wrapper">
      <SimpleConfigurationPanel
        onConfigChange={(config) => {
          console.log('Scan configuration updated:', config);
        }}
      />
    </div>
  );
};

export default ScanConfiguration;
