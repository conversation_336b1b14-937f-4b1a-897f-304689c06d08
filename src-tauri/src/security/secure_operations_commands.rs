/// Tauri Commands for Secure File Operations
/// 
/// This module provides Tauri command handlers for secure file operations
/// including password-protected archival, secure deletion, and privacy workflow integration.

use std::path::PathBuf;
use std::sync::Arc;
use tauri::{command, State};
use serde::{Deserialize, Serialize};

use crate::security::{
    SecureFileOperations, SecureOperationsConfig, PrivacyWorkflow,
    SecureDeletionReport, SecureArchiveResult, SecureOperationsError,
    EncryptionType, CompressionLevel, SensitiveDataDetector
};
use crate::security::secure_operations::PrivacySummary;
use crate::security::sensitive_data_detector::{DetectionConfig, FileScanResult};
use crate::report_debug;

/// State for secure operations manager
pub type SecureOperationsState = Arc<SecureFileOperations>;

/// State for privacy workflow
pub type PrivacyWorkflowState = Arc<PrivacyWorkflow>;

/// Request structure for creating password-protected archives
#[derive(Debug, Deserialize)]
pub struct CreateArchiveRequest {
    pub file_paths: Vec<String>,
    pub password: String,
    pub output_path: String,
    pub compression_level: Option<CompressionLevel>,
    pub encryption_type: Option<EncryptionType>,
}

/// Request structure for secure deletion
#[derive(Debug, Deserialize)]
pub struct SecureDeletionRequest {
    pub file_paths: Vec<String>,
    pub overwrite_passes: Option<u32>,
    pub verify_deletion: Option<bool>,
}

/// Request structure for privacy scanning
#[derive(Debug, Deserialize)]
pub struct PrivacyScanRequest {
    pub file_paths: Vec<String>,
}

/// Response structure for secure operations
#[derive(Debug, Serialize)]
pub struct SecureOperationsResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub operation_duration_ms: u64,
}

impl<T> SecureOperationsResponse<T> {
    pub fn success(data: T, duration_ms: u64) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            operation_duration_ms: duration_ms,
        }
    }

    pub fn error(error: String, duration_ms: u64) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
            operation_duration_ms: duration_ms,
        }
    }
}

/// Initialize secure operations with configuration
#[command]
pub async fn initialize_secure_operations(
    config: SecureOperationsConfig,
) -> Result<String, String> {
    match SecureFileOperations::new(config) {
        Ok(_) => {
            report_debug!("Secure operations initialized successfully");
            Ok("Secure operations initialized".to_string())
        }
        Err(e) => {
            report_debug!(format!("Failed to initialize secure operations: {}", e));
            Err(format!("Failed to initialize secure operations: {}", e))
        }
    }
}

/// Create password-protected archive
#[command]
pub async fn create_secure_archive(
    request: CreateArchiveRequest,
    secure_ops: State<'_, SecureOperationsState>,
) -> Result<SecureOperationsResponse<SecureArchiveResult>, String> {
    let start_time = std::time::Instant::now();
    
    report_debug!(format!("Creating secure archive for {} files", request.file_paths.len()));
    
    // Convert string paths to PathBuf
    let file_paths: Vec<PathBuf> = request.file_paths
        .into_iter()
        .map(PathBuf::from)
        .collect();
    
    let output_path = PathBuf::from(request.output_path);
    
    match secure_ops.create_password_protected_archive(
        file_paths,
        &request.password,
        &output_path,
    ).await {
        Ok(result) => {
            let duration = start_time.elapsed().as_millis() as u64;
            report_debug!(format!("Archive created successfully: {:?}", result.archive_path));
            Ok(SecureOperationsResponse::success(result, duration))
        }
        Err(e) => {
            let duration = start_time.elapsed().as_millis() as u64;
            let error_msg = format!("Failed to create secure archive: {}", e);
            report_debug!(error_msg.clone());
            Ok(SecureOperationsResponse::error(error_msg, duration))
        }
    }
}

/// Perform secure deletion of files
#[command]
pub async fn secure_delete_files(
    request: SecureDeletionRequest,
    secure_ops: State<'_, SecureOperationsState>,
) -> Result<SecureOperationsResponse<SecureDeletionReport>, String> {
    let start_time = std::time::Instant::now();
    
    report_debug!(format!("Securely deleting {} files", request.file_paths.len()));
    
    // Convert string paths to PathBuf
    let file_paths: Vec<PathBuf> = request.file_paths
        .into_iter()
        .map(PathBuf::from)
        .collect();
    
    match secure_ops.secure_delete_multiple(file_paths).await {
        Ok(report) => {
            let duration = start_time.elapsed().as_millis() as u64;
            report_debug!(format!("Secure deletion completed: {} files deleted, {} bytes", 
                report.files_deleted.len(), report.bytes_deleted));
            Ok(SecureOperationsResponse::success(report, duration))
        }
        Err(e) => {
            let duration = start_time.elapsed().as_millis() as u64;
            let error_msg = format!("Failed to securely delete files: {}", e);
            report_debug!(error_msg.clone());
            Ok(SecureOperationsResponse::error(error_msg, duration))
        }
    }
}

/// Get privacy summary for files
#[command]
pub async fn get_privacy_summary(
    request: PrivacyScanRequest,
    workflow: State<'_, PrivacyWorkflowState>,
) -> Result<SecureOperationsResponse<PrivacySummary>, String> {
    let start_time = std::time::Instant::now();
    
    report_debug!(format!("Getting privacy summary for {} files", request.file_paths.len()));
    
    // Convert string paths to PathBuf
    let file_paths: Vec<PathBuf> = request.file_paths
        .into_iter()
        .map(PathBuf::from)
        .collect();
    
    match workflow.get_privacy_summary(&file_paths).await {
        Ok(summary) => {
            let duration = start_time.elapsed().as_millis() as u64;
            report_debug!(format!("Privacy summary completed: {}/{} files have privacy data", 
                summary.files_with_privacy_data, summary.total_files));
            Ok(SecureOperationsResponse::success(summary, duration))
        }
        Err(e) => {
            let duration = start_time.elapsed().as_millis() as u64;
            let error_msg = format!("Failed to get privacy summary: {}", e);
            report_debug!(error_msg.clone());
            Ok(SecureOperationsResponse::error(error_msg, duration))
        }
    }
}

/// Scan files and create secure archive
#[command]
pub async fn scan_and_secure_archive(
    request: CreateArchiveRequest,
    workflow: State<'_, PrivacyWorkflowState>,
) -> Result<SecureOperationsResponse<SecureArchiveResult>, String> {
    let start_time = std::time::Instant::now();
    
    report_debug!(format!("Scanning and archiving {} files", request.file_paths.len()));
    
    // Convert string paths to PathBuf
    let file_paths: Vec<PathBuf> = request.file_paths
        .into_iter()
        .map(PathBuf::from)
        .collect();
    
    let output_path = PathBuf::from(request.output_path);
    
    match workflow.scan_and_secure_archive(
        file_paths,
        &request.password,
        &output_path,
    ).await {
        Ok(result) => {
            let duration = start_time.elapsed().as_millis() as u64;
            report_debug!(format!("Scan and archive completed: {:?}", result.archive_path));
            Ok(SecureOperationsResponse::success(result, duration))
        }
        Err(e) => {
            let duration = start_time.elapsed().as_millis() as u64;
            let error_msg = format!("Failed to scan and archive: {}", e);
            report_debug!(error_msg.clone());
            Ok(SecureOperationsResponse::error(error_msg, duration))
        }
    }
}

/// Scan files and perform secure deletion
#[command]
pub async fn scan_and_secure_delete(
    request: SecureDeletionRequest,
    workflow: State<'_, PrivacyWorkflowState>,
) -> Result<SecureOperationsResponse<(Vec<FileScanResult>, SecureDeletionReport)>, String> {
    let start_time = std::time::Instant::now();
    
    report_debug!(format!("Scanning and securely deleting {} files", request.file_paths.len()));
    
    // Convert string paths to PathBuf
    let file_paths: Vec<PathBuf> = request.file_paths
        .into_iter()
        .map(PathBuf::from)
        .collect();
    
    match workflow.scan_and_secure_delete(file_paths).await {
        Ok((scan_results, deletion_report)) => {
            let duration = start_time.elapsed().as_millis() as u64;
            report_debug!(format!("Scan and delete completed: {} files scanned, {} files deleted", 
                scan_results.len(), deletion_report.files_deleted.len()));
            Ok(SecureOperationsResponse::success((scan_results, deletion_report), duration))
        }
        Err(e) => {
            let duration = start_time.elapsed().as_millis() as u64;
            let error_msg = format!("Failed to scan and delete: {}", e);
            report_debug!(error_msg.clone());
            Ok(SecureOperationsResponse::error(error_msg, duration))
        }
    }
}

/// Get default secure operations configuration
#[command]
pub fn get_default_secure_config() -> SecureOperationsConfig {
    SecureOperationsConfig::default()
}

/// Validate secure operations configuration
#[command]
pub fn validate_secure_config(config: SecureOperationsConfig) -> Result<String, String> {
    // Basic validation
    if config.overwrite_passes == 0 {
        return Err("Overwrite passes must be greater than 0".to_string());
    }
    
    if config.overwrite_passes > 35 {
        return Err("Overwrite passes should not exceed 35 (DoD maximum)".to_string());
    }
    
    if config.max_file_size == 0 {
        return Err("Maximum file size must be greater than 0".to_string());
    }
    
    Ok("Configuration is valid".to_string())
}

/// Initialize privacy workflow
#[command]
pub async fn initialize_privacy_workflow(
    secure_config: SecureOperationsConfig,
    detection_config: DetectionConfig,
) -> Result<String, String> {
    match SecureFileOperations::new(secure_config) {
        Ok(_secure_ops) => {
            match SensitiveDataDetector::with_config(detection_config) {
                Ok(_privacy_detector) => {
                    // Note: In a real implementation, you would store these in app state
                    report_debug!("Privacy workflow initialized successfully");
                    Ok("Privacy workflow initialized".to_string())
                }
                Err(e) => {
                    report_debug!(format!("Failed to initialize privacy detector: {}", e));
                    Err(format!("Failed to initialize privacy detector: {}", e))
                }
            }
        }
        Err(e) => {
            report_debug!(format!("Failed to initialize secure operations: {}", e));
            Err(format!("Failed to initialize secure operations: {}", e))
        }
    }
}
