MIXED CONTENT TEST DOCUMENT
===========================

This document contains a mix of sensitive and non-sensitive information
to test the privacy detection engine's ability to identify specific risks
while ignoring safe content.

SAFE CONTENT SECTION
---------------------

Company Newsletter - December 2023

Dear Team,

We're excited to announce our Q4 achievements and upcoming initiatives
for the new year. Our privacy detection software has been well-received
by customers and we're seeing strong adoption rates.

Product Updates:
- Enhanced scanning algorithms
- Improved user interface
- Better performance metrics
- New file format support

Team Announcements:
- Welcome to our new developers
- Congratulations on successful project deliveries
- Holiday schedule and office closures
- Training sessions scheduled for January

SENSITIVE CONTENT SECTION
--------------------------

CONFIDENTIAL - Internal Use Only

Employee Information:
Name: <PERSON>
Employee ID: EMP-789012
SSN: ***********
Phone: (*************
Email: <EMAIL>
Email: <EMAIL>


Emergency Contact:
Name: <PERSON>
Relationship: Spouse
Phone: (*************

Banking Details for Payroll:
Bank: Regional Credit Union
Account Number: ************
Routing Number: *********

MIXED SAFE/SENSITIVE SECTION
-----------------------------

Meeting Notes - Project Alpha

Attendees:
- John D. (Project Manager)
- Sarah K. (Developer)
- Mike R. (QA Engineer)
- Lisa W. (Designer)

Discussion Points:
1. Database security improvements
2. User authentication enhancements
3. API rate limiting implementation

Action Items:
- Update encryption protocols
- Review access control lists
- Test new security features

Technical Details:
- Database: PostgreSQL 14.2
- Framework: React 18.2
- Authentication: JWT tokens
- Hosting: AWS EC2 instances

CRYPTOCURRENCY SECTION
-----------------------

Development Wallet Addresses (Test Network):

Bitcoin Testnet:
- tb1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh

Ethereum Testnet:
- ******************************************

Cardano Testnet:
- addr_test1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlhtest123

Production Wallets (SENSITIVE):
Bitcoin: **********************************
Ethereum: 0x8ba1f109551bD432803012645Hac136c22C501e
Cardano: addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlhprod456

AdaHandles:
- Test: $test_handle
- Production: $company_crypto (SENSITIVE)

CUSTOMER DATA SECTION
----------------------

Customer Support Ticket #12345

Customer: Public User
Issue: General inquiry about features
Status: Resolved
Priority: Low

Customer Details:
Name: Alex Johnson
Email: <EMAIL>
Phone: (*************

Issue Description:
Customer asking about file format support and scanning capabilities.
No sensitive information involved.

Resolution:
Provided documentation links and feature overview.
Customer satisfied with response.

SENSITIVE CUSTOMER DATA
-----------------------

VIP Customer Account

Customer: Enterprise Client
Account ID: CUST-VIP-001
Contract Value: $50,000 annually

Contact Information:
Primary: Robert Chen
Title: Chief Technology Officer
Email: <EMAIL>
Phone: (*************
SSN: *********** (for verification)

Payment Information:
Credit Card: 4532-1234-5678-9012
Expiry: 12/2025
Billing Address: 456 Corporate Blvd, Business City, CA 90210

DEVELOPMENT NOTES
-----------------

Safe Development Information:
- Code repository: GitHub
- CI/CD pipeline: Jenkins
- Testing framework: Jest
- Documentation: Confluence

Sensitive Development Information:
API Keys:
- Production: sk-prod-1234567890abcdef1234567890abcdef
- Staging: sk-stage-9876543210fedcba9876543210fedcba

Database Credentials:
- Host: prod-db.company.com
- Username: app_user
- Password: SecurePassword123!
- Database: privacy_ai_prod

CONCLUSION
----------

This mixed content document should trigger detection for:
✅ SSN numbers (multiple instances)
✅ Credit card numbers
✅ Bank account information
✅ Cryptocurrency addresses (production)
✅ API keys and passwords
✅ Personal contact information

While ignoring safe content like:
✅ General company information
✅ Meeting notes (non-sensitive)
✅ Technical specifications
✅ Test network addresses
✅ Public documentation

Expected Risk Score: MEDIUM to HIGH (0.6 - 0.8)
Expected Categories: financial, personal, cryptocurrency, credentials

This tests the engine's ability to distinguish between sensitive and
non-sensitive information within the same document.
