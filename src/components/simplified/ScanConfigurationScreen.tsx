import { useState } from 'react';
import { ArrowLeft, Play, Shield, FileX, Copy, Lock, Bitcoin, User, HardDrive, Zap } from 'lucide-react';
import * as Checkbox from '@radix-ui/react-checkbox';
import { CheckIcon } from 'lucide-react';
import type { ScanConfiguration } from './types';

interface ScanConfigurationScreenProps {
  initialConfig: ScanConfiguration;
  onComplete: (config: ScanConfiguration) => void;
  onBack: () => void;
}

interface ScanCategory {
  key: keyof ScanConfiguration;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const scanCategories: ScanCategory[] = [
  {
    key: 'corrupt_files',
    label: 'Corrupt Files',
    description: 'Detect corrupted, damaged, or unreadable files',
    icon: FileX,
    color: 'text-red-600',
  },
  {
    key: 'duplicate_files',
    label: 'Duplicate Files',
    description: 'Find identical or similar files taking up space',
    icon: Copy,
    color: 'text-orange-600',
  },
  {
    key: 'security_documents',
    label: 'Security & ID Documents',
    description: 'Identify sensitive documents like passports, licenses',
    icon: Lock,
    color: 'text-purple-600',
  },
  {
    key: 'cryptocurrency',
    label: 'Cryptocurrency Files',
    description: 'Detect crypto wallets, keys, and related files',
    icon: Bitcoin,
    color: 'text-yellow-600',
  },
  {
    key: 'privacy_data',
    label: 'Privacy Data',
    description: 'Find personal information like SSN, credit cards, emails',
    icon: Shield,
    color: 'text-blue-600',
  },
  {
    key: 'government_ids',
    label: 'Government IDs',
    description: 'Detect government-issued identification documents',
    icon: User,
    color: 'text-green-600',
  },
  {
    key: 'file_integrity',
    label: 'File Integrity',
    description: 'Check file integrity and metadata analysis',
    icon: HardDrive,
    color: 'text-gray-600',
  },
  {
    key: 'enhanced_mode',
    label: 'Enhanced Scanning Mode',
    description: '97% accuracy with 43% performance improvement and context-aware detection',
    icon: Zap,
    color: 'text-emerald-600',
  },
];

export default function ScanConfigurationScreen({
  initialConfig,
  onComplete,
  onBack,
}: ScanConfigurationScreenProps) {
  const [config, setConfig] = useState<ScanConfiguration>(initialConfig);

  const handleCategoryToggle = (key: keyof ScanConfiguration) => {
    setConfig(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleSelectAll = () => {
    const allSelected = Object.values(config).every(Boolean);
    const newConfig = Object.keys(config).reduce((acc, key) => {
      acc[key as keyof ScanConfiguration] = !allSelected;
      return acc;
    }, {} as ScanConfiguration);
    setConfig(newConfig);
  };

  const handleContinue = () => {
    onComplete(config);
  };

  const selectedCount = Object.values(config).filter(Boolean).length;
  const canContinue = selectedCount > 0;

  return (
    <div className="scan-config-screen">
      <div className="screen-header">
        <button className="back-button" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
          Back
        </button>
        <h1 className="screen-title">Configure Scan</h1>
      </div>

      <div className="scan-config-content">
        <div className="config-intro">
          <p className="config-description">
            Select the types of files and data you want to scan for. 
            Choose at least one category to continue.
          </p>
          
          <div className="config-actions">
            <button 
              className="select-all-button"
              onClick={handleSelectAll}
            >
              {Object.values(config).every(Boolean) ? 'Deselect All' : 'Select All'}
            </button>
            <span className="selection-count">
              {selectedCount} of {scanCategories.length} selected
            </span>
          </div>
        </div>

        <div className="categories-grid">
          {scanCategories.map((category) => {
            const Icon = category.icon;
            const isSelected = config[category.key];
            
            return (
              <div
                key={category.key}
                className={`category-card ${isSelected ? 'selected' : ''}`}
                onClick={() => handleCategoryToggle(category.key)}
              >
                <div className="category-header">
                  <div className="category-icon-wrapper">
                    <Icon className={`w-6 h-6 ${category.color}`} />
                  </div>
                  <Checkbox.Root
                    className="checkbox-root"
                    checked={isSelected}
                    onCheckedChange={() => handleCategoryToggle(category.key)}
                  >
                    <Checkbox.Indicator className="checkbox-indicator">
                      <CheckIcon className="w-4 h-4" />
                    </Checkbox.Indicator>
                  </Checkbox.Root>
                </div>
                
                <div className="category-content">
                  <h3 className="category-label">{category.label}</h3>
                  <p className="category-description">{category.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="config-footer">
          <button
            className={`continue-button ${canContinue ? 'enabled' : 'disabled'}`}
            onClick={handleContinue}
            disabled={!canContinue}
          >
            <Play className="w-5 h-5" />
            Continue to Folder Selection
          </button>
          
          {!canContinue && (
            <p className="continue-hint">
              Please select at least one scan category to continue
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
