import React, { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import OCRFileUpload from './OCRFileUpload';
import OCRResults from './OCRResults';
import OCRLanguageSelector from './OCRLanguageSelector';

interface OCRFile {
  id: string;
  file: File;
  preview?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
}

interface OCRResult {
  file_id: string;
  extracted_text: string;
  confidence: number;
  language: string;
  processing_time_ms: number;
  character_count: number;
  word_count: number;
  file_metadata?: {
    name: string;
    path: string;
    size: number;
    type: string;
    modified: string;
  };
}

interface OCRIntegrationProps {
  onResultsChange?: (results: OCRResult[]) => void;
}

const OCRIntegration: React.FC<OCRIntegrationProps> = ({ onResultsChange }) => {
  const [files, setFiles] = useState<OCRFile[]>([]);
  const [results, setResults] = useState<OCRResult[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState('eng');
  const [autoDetectLanguage, setAutoDetectLanguage] = useState(false);
  const [isOCREngineReady, setIsOCREngineReady] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [processingQueue, setProcessingQueue] = useState<string[]>([]);

  // Initialize OCR engine on component mount
  useEffect(() => {
    initializeOCREngine();
  }, []);

  // Notify parent component of results changes
  useEffect(() => {
    if (onResultsChange) {
      onResultsChange(results);
    }
  }, [results, onResultsChange]);

  const initializeOCREngine = async () => {
    try {
      setIsInitializing(true);
      setError(null);

      // Check if OCR engine is already initialized
      const isReady = await invoke<boolean>('is_ocr_engine_initialized');
      
      if (!isReady) {
        // Initialize OCR engine
        await invoke('initialize_ocr_engine');
      }

      setIsOCREngineReady(true);
    } catch (err) {
      console.error('Failed to initialize OCR engine:', err);
      setError(`Failed to initialize OCR engine: ${err}`);
      setIsOCREngineReady(false);
    } finally {
      setIsInitializing(false);
    }
  };

  const updateFileStatus = useCallback((fileId: string, status: OCRFile['status'], progress: number = 0, error?: string) => {
    setFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, status, progress, error }
        : file
    ));
  }, []);

  const processFile = async (file: OCRFile) => {
    if (!isOCREngineReady) {
      updateFileStatus(file.id, 'error', 0, 'OCR engine not ready');
      return;
    }

    try {
      updateFileStatus(file.id, 'processing', 10);

      // Determine the language to use
      const languageToUse = autoDetectLanguage ? 'auto' : selectedLanguage;

      let result: any;
      const startTime = Date.now();

      // Choose appropriate OCR method based on file type
      if (file.file.type === 'application/pdf') {
        updateFileStatus(file.id, 'processing', 30);
        
        // Convert File to path for PDF processing
        // Note: In a real implementation, you might need to save the file temporarily
        // For now, we'll use the file name as a placeholder
        result = await invoke('extract_text_from_pdf', {
          pdfPath: file.file.name,
          language: languageToUse
        });
      } else if (file.file.type.startsWith('image/')) {
        updateFileStatus(file.id, 'processing', 30);
        
        // Convert image file to base64 for processing
        const base64Image = await fileToBase64(file.file);
        
        result = await invoke('extract_text_from_image', {
          imageData: base64Image,
          language: languageToUse
        });
      } else {
        throw new Error(`Unsupported file type: ${file.file.type}`);
      }

      updateFileStatus(file.id, 'processing', 80);

      const processingTime = Date.now() - startTime;

      // Process the result
      const ocrResult: OCRResult = {
        file_id: file.id,
        extracted_text: result.extracted_text || result.text || '',
        confidence: result.confidence || 0.85,
        language: result.language || languageToUse,
        processing_time_ms: result.processing_time_ms || processingTime,
        character_count: (result.extracted_text || result.text || '').length,
        word_count: countWords(result.extracted_text || result.text || ''),
        file_metadata: {
          name: file.file.name,
          path: file.file.name, // In real implementation, this would be the actual path
          size: file.file.size,
          type: file.file.type,
          modified: new Date(file.file.lastModified).toISOString()
        }
      };

      updateFileStatus(file.id, 'completed', 100);
      
      setResults(prev => [...prev, ocrResult]);

    } catch (err) {
      console.error('OCR processing failed:', err);
      updateFileStatus(file.id, 'error', 0, `Processing failed: ${err}`);
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 data
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  const countWords = (text: string): number => {
    if (!text.trim()) return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  const handleFilesSelected = (newFiles: OCRFile[]) => {
    setFiles(prev => [...prev, ...newFiles]);
    
    // Add new files to processing queue
    const newFileIds = newFiles.map(f => f.id);
    setProcessingQueue(prev => [...prev, ...newFileIds]);
  };

  // Process files from queue
  useEffect(() => {
    if (processingQueue.length > 0 && isOCREngineReady) {
      const fileId = processingQueue[0];
      const file = files.find(f => f.id === fileId);
      
      if (file && file.status === 'pending') {
        processFile(file);
        setProcessingQueue(prev => prev.slice(1));
      }
    }
  }, [processingQueue, files, isOCREngineReady]);

  const handleExport = async (_result: OCRResult, _format: 'text' | 'json' | 'summary') => {
    // Export functionality is handled by the OCRResults component
    // This is a placeholder for any additional export logic
  };

  const clearResults = () => {
    setResults([]);
    setFiles([]);
    setProcessingQueue([]);
  };

  const retryFailedFiles = () => {
    const failedFiles = files.filter(f => f.status === 'error');
    const failedFileIds = failedFiles.map(f => f.id);
    
    // Reset failed files to pending
    setFiles(prev => prev.map(file => 
      failedFileIds.includes(file.id)
        ? { ...file, status: 'pending', progress: 0, error: undefined }
        : file
    ));
    
    // Add to processing queue
    setProcessingQueue(prev => [...prev, ...failedFileIds]);
  };

  if (isInitializing) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing OCR engine...</p>
        </div>
      </div>
    );
  }

  if (error && !isOCREngineReady) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center mb-4">
          <svg className="w-6 h-6 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-red-800">OCR Engine Error</h3>
        </div>
        <p className="text-red-700 mb-4">{error}</p>
        <button
          onClick={initializeOCREngine}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry Initialization
        </button>
      </div>
    );
  }

  return (
    <div className="ocr-integration space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">OCR & Document Processing</h2>
          <p className="text-gray-600 mt-1">
            Extract text from images and PDFs with 100+ language support
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isOCREngineReady ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-sm text-gray-600">
            {isOCREngineReady ? 'OCR Ready' : 'OCR Unavailable'}
          </span>
        </div>
      </div>

      {/* Language Selection */}
      <div className="bg-white p-6 border border-gray-200 rounded-lg">
        <OCRLanguageSelector
          selectedLanguage={selectedLanguage}
          onLanguageChange={setSelectedLanguage}
          autoDetect={autoDetectLanguage}
          onAutoDetectChange={setAutoDetectLanguage}
          disabled={!isOCREngineReady}
        />
      </div>

      {/* File Upload */}
      <div className="bg-white p-6 border border-gray-200 rounded-lg">
        <OCRFileUpload
          onFilesSelected={handleFilesSelected}
          // onFileProcessed removed from interface
          maxFiles={10}
          maxSize={50 * 1024 * 1024} // 50MB
        />
      </div>

      {/* Action Buttons */}
      {(files.length > 0 || results.length > 0) && (
        <div className="flex items-center space-x-4">
          {files.some(f => f.status === 'error') && (
            <button
              onClick={retryFailedFiles}
              className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
            >
              Retry Failed Files
            </button>
          )}
          
          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Clear All
          </button>
        </div>
      )}

      {/* Results */}
      {results.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg">
          <OCRResults
            results={results}
            onExport={handleExport}
          />
        </div>
      )}

      {/* Processing Status */}
      {processingQueue.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-700 font-medium">
              Processing {processingQueue.length} file(s) in queue...
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default OCRIntegration;
