import React, { useState, useEffect } from 'react';
// import { invoke } from '@tauri-apps/api/core'; // TODO: Implement backend integration
import * as Tabs from '@radix-ui/react-tabs';
import * as Slider from '@radix-ui/react-slider';
import * as Dialog from '@radix-ui/react-dialog';
import * as Toast from '@radix-ui/react-toast';
import { Settings, Shield, Zap, Database, X, Save, RotateCcw } from 'lucide-react';
import useMobileOptimization from '../../hooks/useMobileOptimization';
import useAccessibility from '../../hooks/useAccessibility';
import './EnhancedConfigurationPanel.css';

// Enhanced configuration interface with Radix UI
interface EnhancedConfig {
    privacy_detection: {
        enabled: boolean;
        ssn_detection: boolean;
        credit_card_detection: boolean;
        email_detection: boolean;
        phone_detection: boolean;
        confidence_threshold: number;
        context_window_size: number;
    };
    crypto_detection: {
        enabled: boolean;
        bitcoin_detection: boolean;
        ethereum_detection: boolean;
        cardano_detection: boolean;
        wmt_detection: boolean;
        ada_handle_detection: boolean;
        unstoppable_domains_detection: boolean;
    };
    performance: {
        mode: 'fast' | 'balanced' | 'thorough';
        max_concurrent_files: number;
        cache_enabled: boolean;
        memory_limit_mb: number;
        timeout_seconds: number;
    };
    security: {
        overwrite_passes: number;
        encryption_algorithm: 'AES256GCM' | 'ChaCha20Poly1305';
        verify_deletion: boolean;
        secure_temp_cleanup: boolean;
    };
}

interface EnhancedConfigurationPanelProps {
    onConfigChange?: (config: EnhancedConfig) => void;
}

const EnhancedConfigurationPanel: React.FC<EnhancedConfigurationPanelProps> = ({
    onConfigChange
}) => {
    // Mobile and accessibility optimizations
    const {
        isMobile,
        hasTouch,
        adaptiveConfig,
        createGestureHandler,
        isPortrait
    } = useMobileOptimization();

    const {
        announce,
        generateAriaAttributes,
        // setFocusTrap, // TODO: Implement focus trapping
        // needsReducedMotion, // TODO: Use for animation control
        needsHighContrast
    } = useAccessibility({ trapFocus: true, restoreFocus: true });

    const [config, setConfig] = useState<EnhancedConfig>({
        privacy_detection: {
            enabled: true,
            ssn_detection: true,
            credit_card_detection: true,
            email_detection: true,
            phone_detection: true,
            confidence_threshold: 0.8,
            context_window_size: 100,
        },
        crypto_detection: {
            enabled: true,
            bitcoin_detection: true,
            ethereum_detection: true,
            cardano_detection: true,
            wmt_detection: true,
            ada_handle_detection: true,
            unstoppable_domains_detection: true,
        },
        performance: {
            mode: 'balanced',
            max_concurrent_files: 10,
            cache_enabled: true,
            memory_limit_mb: 1024,
            timeout_seconds: 300,
        },
        security: {
            overwrite_passes: 7,
            encryption_algorithm: 'AES256GCM',
            verify_deletion: true,
            secure_temp_cleanup: true,
        },
    });
    
    const [activeTab, setActiveTab] = useState<'privacy' | 'crypto' | 'performance' | 'security'>('privacy');
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
    const [showResetDialog, setShowResetDialog] = useState(false);
    const [toastOpen, setToastOpen] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    useEffect(() => {
        loadConfiguration();

        // Setup mobile gesture handlers
        if (hasTouch) {
            const gestureHandlers = createGestureHandler({
                onSwipeLeft: () => {
                    // Navigate to next tab
                    const tabs = ['privacy', 'crypto', 'performance', 'security'];
                    const currentIndex = tabs.indexOf(activeTab);
                    if (currentIndex < tabs.length - 1) {
                        setActiveTab(tabs[currentIndex + 1] as any);
                        announce(`Switched to ${tabs[currentIndex + 1]} tab`, { priority: 'polite' });
                    }
                },
                onSwipeRight: () => {
                    // Navigate to previous tab
                    const tabs = ['privacy', 'crypto', 'performance', 'security'];
                    const currentIndex = tabs.indexOf(activeTab);
                    if (currentIndex > 0) {
                        setActiveTab(tabs[currentIndex - 1] as any);
                        announce(`Switched to ${tabs[currentIndex - 1]} tab`, { priority: 'polite' });
                    }
                }
            });

            // Apply gesture handlers to the component
            const element = document.querySelector('.enhanced-config-panel');
            if (element) {
                element.addEventListener('touchstart', gestureHandlers.onTouchStart as EventListener);
                element.addEventListener('touchend', gestureHandlers.onTouchEnd as EventListener);

                return () => {
                    element.removeEventListener('touchstart', gestureHandlers.onTouchStart as EventListener);
                    element.removeEventListener('touchend', gestureHandlers.onTouchEnd as EventListener);
                };
            }
        }
    }, [hasTouch, createGestureHandler, activeTab, announce]);

    const loadConfiguration = async () => {
        try {
            setIsLoading(true);
            // Load configuration from backend when available
            console.log('Loading enhanced configuration...');
        } catch (error) {
            console.error('Failed to load configuration:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const saveConfiguration = async () => {
        try {
            setSaveStatus('saving');
            announce('Saving configuration...', { priority: 'polite' });

            // Apply adaptive configuration based on device capabilities
            const optimizedConfig = {
                ...config,
                performance: {
                    ...config.performance,
                    ...adaptiveConfig
                }
            };

            // Simulate backend save
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (onConfigChange) {
                onConfigChange(optimizedConfig);
            }

            setSaveStatus('saved');
            setToastMessage('Configuration saved successfully!');
            setToastOpen(true);
            announce('Configuration saved successfully!', { priority: 'polite' });
            setTimeout(() => setSaveStatus('idle'), 2000);

        } catch (error) {
            console.error('Failed to save configuration:', error);
            setSaveStatus('error');
            setToastMessage('Failed to save configuration. Please try again.');
            setToastOpen(true);
            announce('Failed to save configuration. Please try again.', { priority: 'assertive' });
            setTimeout(() => setSaveStatus('idle'), 3000);
        }
    };

    const resetToDefaults = () => {
        setConfig({
            privacy_detection: {
                enabled: true,
                ssn_detection: true,
                credit_card_detection: true,
                email_detection: true,
                phone_detection: true,
                confidence_threshold: 0.8,
                context_window_size: 100,
            },
            crypto_detection: {
                enabled: true,
                bitcoin_detection: true,
                ethereum_detection: true,
                cardano_detection: true,
                wmt_detection: true,
                ada_handle_detection: true,
                unstoppable_domains_detection: true,
            },
            performance: {
                mode: 'balanced',
                max_concurrent_files: 10,
                cache_enabled: true,
                memory_limit_mb: 1024,
                timeout_seconds: 300,
            },
            security: {
                overwrite_passes: 7,
                encryption_algorithm: 'AES256GCM',
                verify_deletion: true,
                secure_temp_cleanup: true,
            },
        });
        setShowResetDialog(false);
        setToastMessage('Configuration reset to defaults');
        setToastOpen(true);
    };

    const updateConfig = (section: keyof EnhancedConfig, key: string, value: any) => {
        setConfig(prev => ({
            ...prev,
            [section]: {
                ...prev[section],
                [key]: value
            }
        }));
    };

    if (isLoading) {
        return (
            <div className="enhanced-config-panel loading">
                <div className="loading-spinner"></div>
                <p>Loading configuration...</p>
            </div>
        );
    }

    return (
        <div
            className={`enhanced-config-panel ${isMobile ? 'mobile' : ''} ${isPortrait ? 'portrait' : 'landscape'} ${needsHighContrast ? 'high-contrast' : ''}`}
            {...generateAriaAttributes({
                label: 'Advanced Configuration Panel',
                description: 'Configure privacy detection and security settings'
            })}
        >
            <div className="config-header">
                <div className="header-content">
                    <Settings className="header-icon" aria-hidden="true" />
                    <div>
                        <h2 id="config-title">Advanced Configuration</h2>
                        <p id="config-description">Fine-tune PrivacyAI's detection and security settings</p>
                        {isMobile && (
                            <p className="mobile-hint">
                                <span className="sr-only">Mobile tip: </span>
                                Swipe left or right to navigate between tabs
                            </p>
                        )}
                    </div>
                </div>
                
                <div className="header-actions">
                    <Dialog.Root open={showResetDialog} onOpenChange={setShowResetDialog}>
                        <Dialog.Trigger asChild>
                            <button className="reset-button" disabled={saveStatus === 'saving'}>
                                <RotateCcw size={16} />
                                Reset
                            </button>
                        </Dialog.Trigger>
                        <Dialog.Portal>
                            <Dialog.Overlay className="dialog-overlay" />
                            <Dialog.Content className="dialog-content">
                                <Dialog.Title className="dialog-title">Reset Configuration</Dialog.Title>
                                <Dialog.Description className="dialog-description">
                                    Are you sure you want to reset all settings to their default values? 
                                    This action cannot be undone.
                                </Dialog.Description>
                                <div className="dialog-actions">
                                    <Dialog.Close asChild>
                                        <button className="cancel-button">Cancel</button>
                                    </Dialog.Close>
                                    <button className="confirm-button" onClick={resetToDefaults}>
                                        Reset to Defaults
                                    </button>
                                </div>
                                <Dialog.Close asChild>
                                    <button className="dialog-close" aria-label="Close">
                                        <X size={16} />
                                    </button>
                                </Dialog.Close>
                            </Dialog.Content>
                        </Dialog.Portal>
                    </Dialog.Root>
                    
                    <button
                        className={`save-button ${saveStatus}`}
                        onClick={saveConfiguration}
                        disabled={saveStatus === 'saving'}
                    >
                        <Save size={16} />
                        {saveStatus === 'saving' && 'Saving...'}
                        {saveStatus === 'saved' && 'Saved!'}
                        {saveStatus === 'error' && 'Error'}
                        {saveStatus === 'idle' && 'Save Configuration'}
                    </button>
                </div>
            </div>

            <Tabs.Root
                value={activeTab}
                onValueChange={(value) => {
                    setActiveTab(value as any);
                    announce(`Switched to ${value} configuration tab`, { priority: 'polite' });
                }}
                className="config-tabs"
                orientation={isMobile && isPortrait ? 'vertical' : 'horizontal'}
            >
                <Tabs.List
                    className="tabs-list"
                    aria-label="Configuration categories"
                    role="tablist"
                >
                    <Tabs.Trigger
                        value="privacy"
                        className="tab-trigger"
                        {...generateAriaAttributes({
                            label: 'Privacy Detection Settings',
                            selected: activeTab === 'privacy'
                        })}
                    >
                        <Shield size={16} aria-hidden="true" />
                        Privacy Detection
                    </Tabs.Trigger>
                    <Tabs.Trigger
                        value="crypto"
                        className="tab-trigger"
                        {...generateAriaAttributes({
                            label: 'Cryptocurrency Detection Settings',
                            selected: activeTab === 'crypto'
                        })}
                    >
                        <Database size={16} aria-hidden="true" />
                        Cryptocurrency
                    </Tabs.Trigger>
                    <Tabs.Trigger
                        value="performance"
                        className="tab-trigger"
                        {...generateAriaAttributes({
                            label: 'Performance Settings',
                            selected: activeTab === 'performance'
                        })}
                    >
                        <Zap size={16} aria-hidden="true" />
                        Performance
                    </Tabs.Trigger>
                    <Tabs.Trigger
                        value="security"
                        className="tab-trigger"
                        {...generateAriaAttributes({
                            label: 'Security Settings',
                            selected: activeTab === 'security'
                        })}
                    >
                        <Settings size={16} aria-hidden="true" />
                        Security
                    </Tabs.Trigger>
                </Tabs.List>

                <Tabs.Content value="privacy" className="tab-content">
                    <div className="config-section">
                        <h3>Privacy Data Detection</h3>
                        
                        <div className="config-group">
                            <label className="switch-label">
                                <input
                                    type="checkbox"
                                    checked={config.privacy_detection.enabled}
                                    onChange={(e) => updateConfig('privacy_detection', 'enabled', e.target.checked)}
                                />
                                <span className="switch-text">Enable Privacy Detection</span>
                            </label>
                        </div>

                        {config.privacy_detection.enabled && (
                            <>
                                <div className="config-group">
                                    <h4>Detection Types</h4>
                                    <div className="checkbox-grid">
                                        {[
                                            { key: 'ssn_detection', label: 'Social Security Numbers' },
                                            { key: 'credit_card_detection', label: 'Credit Card Numbers' },
                                            { key: 'email_detection', label: 'Email Addresses' },
                                            { key: 'phone_detection', label: 'Phone Numbers' },
                                        ].map(({ key, label }) => (
                                            <label key={key} className="checkbox-item">
                                                <input
                                                    type="checkbox"
                                                    checked={config.privacy_detection[key as keyof typeof config.privacy_detection] as boolean}
                                                    onChange={(e) => updateConfig('privacy_detection', key, e.target.checked)}
                                                />
                                                <span>{label}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>

                                <div className="config-group">
                                    <label className="slider-label">
                                        Detection Confidence: {(config.privacy_detection.confidence_threshold * 100).toFixed(0)}%
                                    </label>
                                    <Slider.Root
                                        className="slider-root"
                                        value={[config.privacy_detection.confidence_threshold]}
                                        onValueChange={(value) => updateConfig('privacy_detection', 'confidence_threshold', value[0])}
                                        max={1}
                                        min={0.1}
                                        step={0.1}
                                    >
                                        <Slider.Track className="slider-track">
                                            <Slider.Range className="slider-range" />
                                        </Slider.Track>
                                        <Slider.Thumb className="slider-thumb" />
                                    </Slider.Root>
                                    <div className="slider-labels">
                                        <span>Low (Fast)</span>
                                        <span>High (Thorough)</span>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                </Tabs.Content>

                <Tabs.Content value="crypto" className="tab-content">
                    <div className="config-section">
                        <h3>Cryptocurrency Detection</h3>
                        
                        <div className="config-group">
                            <label className="switch-label">
                                <input
                                    type="checkbox"
                                    checked={config.crypto_detection.enabled}
                                    onChange={(e) => updateConfig('crypto_detection', 'enabled', e.target.checked)}
                                />
                                <span className="switch-text">Enable Cryptocurrency Detection</span>
                            </label>
                        </div>

                        {config.crypto_detection.enabled && (
                            <div className="config-group">
                                <h4>Supported Cryptocurrencies</h4>
                                <div className="checkbox-grid">
                                    {[
                                        { key: 'bitcoin_detection', label: 'Bitcoin (BTC)' },
                                        { key: 'ethereum_detection', label: 'Ethereum (ETH)' },
                                        { key: 'cardano_detection', label: 'Cardano (ADA)' },
                                        { key: 'wmt_detection', label: 'World Mobile Token (WMT)' },
                                        { key: 'ada_handle_detection', label: 'AdaHandle ($handle)' },
                                        { key: 'unstoppable_domains_detection', label: 'Unstoppable Domains' },
                                    ].map(({ key, label }) => (
                                        <label key={key} className="checkbox-item">
                                            <input
                                                type="checkbox"
                                                checked={config.crypto_detection[key as keyof typeof config.crypto_detection] as boolean}
                                                onChange={(e) => updateConfig('crypto_detection', key, e.target.checked)}
                                            />
                                            <span>{label}</span>
                                        </label>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </Tabs.Content>
            </Tabs.Root>

            <Toast.Provider swipeDirection="right">
                <Toast.Root className="toast-root" open={toastOpen} onOpenChange={setToastOpen}>
                    <Toast.Title className="toast-title">{toastMessage}</Toast.Title>
                    <Toast.Close className="toast-close" aria-label="Close">
                        <X size={16} />
                    </Toast.Close>
                </Toast.Root>
                <Toast.Viewport className="toast-viewport" />
            </Toast.Provider>
        </div>
    );
};

export default EnhancedConfigurationPanel;
