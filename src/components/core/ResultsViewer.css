.results-viewer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.results-viewer.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.results-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.results-title h2 {
    margin: 0;
    font-size: 1.8em;
    font-weight: 600;
}

.results-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.results-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-container {
    flex: 1;
    min-width: 200px;
}

.search-input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1em;
    backdrop-filter: blur(10px);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.2);
}

.filter-controls {
    display: flex;
    gap: 10px;
}

.filter-select,
.sort-select {
    padding: 8px 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9em;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.filter-select option,
.sort-select option {
    background: #333;
    color: white;
}

.view-controls {
    display: flex;
    gap: 5px;
    background: rgba(255, 255, 255, 0.1);
    padding: 5px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.view-button {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    background: transparent;
    color: white;
    font-size: 1.2em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.view-button.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.export-button {
    padding: 10px 20px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.export-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

.export-button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.results-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-results button {
    margin-top: 15px;
    padding: 10px 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

/* List View Styles */
.results-list {
    padding: 20px;
}

.result-item {
    padding: 20px;
    border-left: 4px solid #ddd;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: all 0.3s ease;
}

.result-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-item.selected {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.risk-icon {
    font-size: 1.2em;
}

.file-path {
    font-weight: 600;
    color: #2c3e50;
}

.file-size {
    color: #7f8c8d;
    font-size: 0.9em;
}

.risk-score {
    text-align: right;
}

.score-value {
    font-size: 1.5em;
    font-weight: 700;
    color: #e74c3c;
}

.score-label {
    display: block;
    font-size: 0.8em;
    color: #7f8c8d;
}

.result-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detection-summary {
    display: flex;
    gap: 20px;
    color: #7f8c8d;
    font-size: 0.9em;
}

.detection-types {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.detection-tag {
    padding: 4px 8px;
    border-radius: 12px;
    color: white;
    font-size: 0.8em;
    font-weight: 500;
}

.more-detections {
    padding: 4px 8px;
    background: #bdc3c7;
    color: white;
    border-radius: 12px;
    font-size: 0.8em;
}

/* Grid View Styles */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
}

.result-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.result-card.selected {
    border: 2px solid #2196f3;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
}

.card-header {
    padding: 15px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.risk-level {
    font-weight: 600;
    font-size: 0.9em;
}

.card-content {
    padding: 20px;
}

.file-name {
    font-weight: 600;
    font-size: 1.1em;
    color: #2c3e50;
    margin-bottom: 5px;
}

.file-path-small {
    color: #7f8c8d;
    font-size: 0.8em;
    margin-bottom: 15px;
    word-break: break-all;
}

.card-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 1.3em;
    font-weight: 700;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.8em;
    color: #7f8c8d;
    margin-top: 2px;
}

/* Table View Styles */
.results-table-container {
    overflow-x: auto;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
}

.results-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #dee2e6;
}

.table-row {
    cursor: pointer;
    transition: all 0.3s ease;
}

.table-row:hover {
    background: #f8f9fa;
}

.table-row.selected {
    background: #e3f2fd;
}

.results-table td {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
}

.risk-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.file-cell .file-name {
    font-weight: 600;
    color: #2c3e50;
}

.file-cell .file-path {
    font-size: 0.8em;
    color: #7f8c8d;
}

.detection-count {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

/* Details Panel */
.result-details-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: -4px 0 16px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    overflow-y: auto;
}

.details-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.details-header h3 {
    margin: 0;
    font-size: 1.2em;
}

.close-details {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.close-details:hover {
    background: rgba(255, 255, 255, 0.2);
}

.details-content {
    padding: 20px;
}

.detail-section {
    margin-bottom: 25px;
}

.detail-section h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.detail-section p {
    margin: 8px 0;
    color: #5a6c7d;
}

.detections-list {
    max-height: 400px;
    overflow-y: auto;
}

.detection-detail {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border-left: 4px solid #ddd;
}

.detection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.detection-type {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.detection-confidence {
    background: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
}

.detection-value {
    font-family: 'Courier New', monospace;
    background: #ecf0f1;
    padding: 8px;
    border-radius: 4px;
    margin: 8px 0;
    word-break: break-all;
}

.detection-context {
    font-size: 0.9em;
    color: #7f8c8d;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .results-viewer {
        padding: 10px;
    }
    
    .results-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .filter-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .result-details-panel {
        width: 100%;
        left: 0;
    }
    
    .results-table-container {
        font-size: 0.8em;
    }
    
    .result-details {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}
