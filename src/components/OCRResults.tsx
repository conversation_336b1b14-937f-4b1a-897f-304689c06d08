import React, { useState } from 'react';

interface OCRResult {
  file_id: string;
  extracted_text: string;
  confidence: number;
  language: string;
  processing_time_ms: number;
  character_count: number;
  word_count: number;
  file_metadata?: {
    name: string;
    path: string;
    size: number;
    type: string;
    modified: string;
  };
}

interface OCRResultsProps {
  results: OCRResult[];
  onExport?: (result: OCRResult, format: 'text' | 'json' | 'summary') => void;
}

const OCRResults: React.FC<OCRResultsProps> = ({ results, onExport }) => {
  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set());
  const [exportStatus, setExportStatus] = useState<{ [key: string]: string }>({});

  const toggleExpanded = (fileId: string) => {
    setExpandedResults(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileId)) {
        newSet.delete(fileId);
      } else {
        newSet.add(fileId);
      }
      return newSet;
    });
  };

  const handleExport = async (result: OCRResult, format: 'text' | 'json' | 'summary') => {
    try {
      setExportStatus(prev => ({ ...prev, [`${result.file_id}-${format}`]: 'processing' }));
      
      if (onExport) {
        await onExport(result, format);
      } else {
        // Default export behavior
        await defaultExport(result, format);
      }
      
      setExportStatus(prev => ({ ...prev, [`${result.file_id}-${format}`]: 'success' }));
      setTimeout(() => {
        setExportStatus(prev => {
          const newStatus = { ...prev };
          delete newStatus[`${result.file_id}-${format}`];
          return newStatus;
        });
      }, 3000);
    } catch (error) {
      console.error('Export failed:', error);
      setExportStatus(prev => ({ ...prev, [`${result.file_id}-${format}`]: 'error' }));
      setTimeout(() => {
        setExportStatus(prev => {
          const newStatus = { ...prev };
          delete newStatus[`${result.file_id}-${format}`];
          return newStatus;
        });
      }, 3000);
    }
  };

  const defaultExport = async (result: OCRResult, format: 'text' | 'json' | 'summary') => {
    switch (format) {
      case 'text':
        await navigator.clipboard.writeText(result.extracted_text);
        break;
      case 'json':
        const jsonData = {
          file_metadata: result.file_metadata,
          extraction_result: {
            text: result.extracted_text,
            confidence: result.confidence,
            language: result.language,
            processing_time_ms: result.processing_time_ms,
            character_count: result.character_count,
            word_count: result.word_count
          },
          timestamp: new Date().toISOString()
        };
        const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${result.file_metadata?.name || 'ocr-result'}_detailed.json`;
        a.click();
        URL.revokeObjectURL(url);
        break;
      case 'summary':
        const summary = `OCR Results Summary
File: ${result.file_metadata?.name || 'Unknown'}
Size: ${result.file_metadata?.size ? (result.file_metadata.size / 1024).toFixed(1) + ' KB' : 'Unknown'}
Type: ${result.file_metadata?.type || 'Unknown'}
Language: ${result.language}
Confidence: ${(result.confidence * 100).toFixed(1)}%
Processing Time: ${result.processing_time_ms}ms
Characters: ${result.character_count}
Words: ${result.word_count}

Extracted Text:
${result.extracted_text}`;
        await navigator.clipboard.writeText(summary);
        break;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.9) return 'text-green-600 bg-green-100';
    if (confidence >= 0.7) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getExportButtonStatus = (resultId: string, format: string) => {
    const status = exportStatus[`${resultId}-${format}`];
    switch (status) {
      case 'processing':
        return { text: 'Processing...', disabled: true, className: 'opacity-50' };
      case 'success':
        return { text: 'Success!', disabled: false, className: 'bg-green-100 text-green-700' };
      case 'error':
        return { text: 'Error', disabled: false, className: 'bg-red-100 text-red-700' };
      default:
        return { text: '', disabled: false, className: '' };
    }
  };

  if (results.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="text-4xl mb-4">📄</div>
        <p>No OCR results yet. Upload files to get started.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          OCR Results ({results.length})
        </h2>
      </div>

      {results.map((result) => {
        const isExpanded = expandedResults.has(result.file_id);
        
        return (
          <div key={result.file_id} className="bg-white border border-gray-200 rounded-lg shadow-sm">
            {/* Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    📄 {result.file_metadata?.name || 'Unknown File'}
                    <span className={`ml-3 px-2 py-1 text-xs font-medium rounded-full ${getConfidenceColor(result.confidence)}`}>
                      {(result.confidence * 100).toFixed(1)}% confidence
                    </span>
                  </h3>
                  
                  {result.file_metadata && (
                    <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div>📁 <strong>Size:</strong> {formatFileSize(result.file_metadata.size)}</div>
                      <div>🏷️ <strong>Type:</strong> {result.file_metadata.type.toUpperCase()}</div>
                      <div>🌐 <strong>Language:</strong> {result.language.toUpperCase()}</div>
                      <div>⚡ <strong>Time:</strong> {result.processing_time_ms}ms</div>
                    </div>
                  )}
                </div>
                
                <button
                  onClick={() => toggleExpanded(result.file_id)}
                  className="ml-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg 
                    className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Content */}
            {isExpanded && (
              <div className="p-6">
                {/* Statistics */}
                <div className="mb-6 grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{result.character_count}</div>
                    <div className="text-sm text-gray-600">Characters</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{result.word_count}</div>
                    <div className="text-sm text-gray-600">Words</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{(result.confidence * 100).toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">Confidence</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{result.processing_time_ms}</div>
                    <div className="text-sm text-gray-600">Milliseconds</div>
                  </div>
                </div>

                {/* Extracted Text */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Extracted Text</h4>
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-64 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono">
                      {result.extracted_text || 'No text extracted'}
                    </pre>
                  </div>
                </div>

                {/* Export Options */}
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={() => handleExport(result, 'text')}
                    disabled={getExportButtonStatus(result.file_id, 'text').disabled}
                    className={`px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${
                      getExportButtonStatus(result.file_id, 'text').className ||
                      'border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    📋 {getExportButtonStatus(result.file_id, 'text').text || 'Copy Text'}
                  </button>
                  
                  <button
                    onClick={() => handleExport(result, 'json')}
                    disabled={getExportButtonStatus(result.file_id, 'json').disabled}
                    className={`px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${
                      getExportButtonStatus(result.file_id, 'json').className ||
                      'border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    📊 {getExportButtonStatus(result.file_id, 'json').text || 'Export JSON'}
                  </button>
                  
                  <button
                    onClick={() => handleExport(result, 'summary')}
                    disabled={getExportButtonStatus(result.file_id, 'summary').disabled}
                    className={`px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${
                      getExportButtonStatus(result.file_id, 'summary').className ||
                      'border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    📋 {getExportButtonStatus(result.file_id, 'summary').text || 'Copy Summary'}
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default OCRResults;
