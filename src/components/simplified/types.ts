// Shared types for simplified UI components

export interface ScanConfiguration {
  corrupt_files: boolean;
  duplicate_files: boolean;
  security_documents: boolean;
  cryptocurrency: boolean;
  privacy_data: boolean;
  government_ids: boolean;
  file_integrity: boolean;
  enhanced_mode: boolean; // Enhanced Scanning Mode toggle
}

export interface FolderNode {
  id: string;
  name: string;
  path: string;
  isDirectory: boolean;
  selected: boolean;
  children?: FolderNode[];
  expanded?: boolean;
}

export interface ScanResult {
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  date_created: string;
  date_modified: string;
  detection_category: string;
  risk_score: number;
  findings: Array<{
    data_type: string;
    confidence: number;
    severity: 'Low' | 'Medium' | 'High' | 'Critical';
    context?: string;
    location?: string;
    matched_text?: string;
  }>;
}

// Enhanced Scan Result types for Enhanced Scanning Mode
export interface EnhancedScanResult {
  file_path: string;
  scan_profile: string;
  processing_time_ms: number;
  memory_usage_mb: number;
  privacy_findings: EnhancedFinding[];
  crypto_findings: CryptoFinding[];
  risk_score: number;
  compliance_status: ComplianceStatus;
  performance_metrics: ScanPerformanceMetrics;
  accuracy_metrics: AccuracyMetrics;
}

export interface EnhancedFinding {
  detection_type: string;
  content: string;
  location: FindingLocation;
  confidence: number;
  risk_level: string;
  context_info?: string;
  is_context_validated: boolean;
  recommended_actions: string[];
}

export interface FindingLocation {
  start_position: number;
  end_position: number;
  line_number?: number;
  column_number?: number;
  page_number?: number;
}

export interface CryptoFinding {
  crypto_type: string;
  content: string;
  confidence: number;
  risk_level: string;
  location: FindingLocation;
}

export interface ComplianceStatus {
  gdpr_status: ComplianceLevel;
  hipaa_status: ComplianceLevel;
  pci_dss_status: ComplianceLevel;
  sox_status: ComplianceLevel;
  overall_risk: ComplianceRisk;
}

export type ComplianceLevel = 'Compliant' | 'Warning' | 'Violation' | 'Unknown';
export type ComplianceRisk = 'Low' | 'Medium' | 'High' | 'Critical';

export interface ScanPerformanceMetrics {
  total_time_ms: number;
  context_analysis_time_ms: number;
  crypto_detection_time_ms: number;
  file_processing_time_ms: number;
  peak_memory_mb: number;
  average_memory_mb: number;
  throughput_files_per_minute: number;
}

export interface AccuracyMetrics {
  patterns_checked: number;
  initial_matches: number;
  context_validated_matches: number;
  confirmed_findings: number;
  estimated_false_positive_rate: number;
  confidence_distribution: ConfidenceDistribution;
}

export interface ConfidenceDistribution {
  high_confidence: number; // 0.8-1.0
  medium_confidence: number; // 0.5-0.8
  low_confidence: number; // 0.0-0.5
}

// OCR-related types
export interface OCRFile {
  id: string;
  name: string;
  path: string;
  type: string;
  size: number;
  preview?: string; // Base64 preview for images
  status: 'pending' | 'processing' | 'completed' | 'error' | 'cancelled';
  progress?: number;
  error_message?: string; // Detailed error message for failed files
}

export interface OCRResult {
  file_id: string;
  extracted_text: string;
  confidence: number;
  language: string;
  processing_time_ms: number;
  page_count?: number;
  privacy_findings?: ScanResult[];
}

export interface OCRLanguage {
  code: string;
  name: string;
  native_name: string;
}

export interface OCRConfiguration {
  selected_languages: string[];
  auto_detect_language: boolean;
  enable_privacy_scan: boolean;
  output_format: 'text' | 'json' | 'pdf';
}

export interface PerformanceSettings {
  cpu_limit: number; // 0-100 percentage
  memory_limit: number; // in MB
  gpu_limit: number; // 0-100 percentage
}

export interface FileTypeSettings {
  [category: string]: {
    enabled: boolean;
    extensions: string[];
    patterns: string[];
  };
}

export type AppScreen = 'main' | 'scan-config' | 'folder-selection' | 'results' | 'settings' | 'ocr';

export interface AppState {
  currentScreen: AppScreen;
  scanConfig: ScanConfiguration;
  selectedFolders: string[];
  scanResults: ScanResult[];
  selectedFiles: string[];
  isScanning: boolean;
  error: string | null;
  performanceSettings: PerformanceSettings;
  fileTypeSettings: FileTypeSettings;
}
