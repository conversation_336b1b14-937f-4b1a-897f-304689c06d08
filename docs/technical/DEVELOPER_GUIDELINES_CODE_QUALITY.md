# 👨‍💻 **Developer Guidelines - Code Quality Standards**

**Document Version**: 1.0  
**Effective Date**: July 27, 2025  
**Scope**: All PrivacyAI development activities  
**Status**: ✅ **Active Guidelines**

## 🎯 **Code Quality Philosophy**

PrivacyAI maintains **high code quality standards** to ensure security, performance, maintainability, and user trust. These guidelines establish consistent practices for all contributors while preserving the project's core strengths: simplicity, performance, and reliability.

### **Quality Principles**
1. **Security First**: All code must prioritize user privacy and data security
2. **Performance Conscious**: Maintain the 43% performance improvement achieved
3. **Maintainable**: Write code that future developers can easily understand and modify
4. **Tested**: Comprehensive testing ensures reliability and prevents regressions
5. **Documented**: Clear documentation enables effective collaboration and usage

---

## 🔧 **Development Environment Setup**

### **Required Tools**
```bash
# Rust toolchain (latest stable)
rustup update stable
rustup default stable

# Essential development tools
cargo install cargo-audit      # Security vulnerability scanning
cargo install cargo-outdated   # Dependency update checking
cargo install cargo-deny       # License and security policy enforcement
cargo install cargo-machete    # Unused dependency detection

# Code quality tools
rustup component add clippy    # Linting
rustup component add rustfmt   # Code formatting

# Frontend tools (if working on UI)
npm install -g @tauri-apps/cli
```

### **IDE Configuration**

#### **VS Code Settings (Recommended)**
```json
{
    "rust-analyzer.check.command": "clippy",
    "rust-analyzer.check.allTargets": false,
    "rust-analyzer.cargo.features": "all",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": true
    },
    "[rust]": {
        "editor.defaultFormatter": "rust-lang.rust-analyzer"
    }
}
```

#### **Required Extensions**
- `rust-lang.rust-analyzer` - Rust language support
- `tauri-apps.tauri-vscode` - Tauri development support
- `bradlc.vscode-tailwindcss` - Tailwind CSS support
- `esbenp.prettier-vscode` - Code formatting

---

## 📝 **Coding Standards**

### **Rust Code Standards**

#### **1. Naming Conventions**
```rust
// ✅ Good: Clear, descriptive names
pub struct ScanConfiguration {
    pub detection_types: DetectionTypesConfig,
    pub performance_settings: PerformanceConfig,
}

pub fn scan_file_unified(
    file_path: &Path,
    config: &ScanConfiguration,
) -> Result<ScanResult, ScanError> {
    // Implementation
}

// ❌ Bad: Unclear abbreviations
pub struct ScanCfg {
    pub det_types: DetCfg,
    pub perf_set: PerfCfg,
}

pub fn scan_file_u(path: &Path, cfg: &ScanCfg) -> Result<ScanRes, ScanErr> {
    // Implementation
}
```

#### **2. Error Handling**
```rust
// ✅ Good: Comprehensive error handling with context
use thiserror::Error;

#[derive(Error, Debug)]
pub enum ScanError {
    #[error("File access denied: {path}")]
    FileAccessDenied { path: String },
    
    #[error("Processing timeout after {seconds}s")]
    ProcessingTimeout { seconds: u32 },
    
    #[error("Configuration validation failed: {0}")]
    ConfigurationError(#[from] ConfigError),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
}

// Function with proper error handling
pub async fn scan_file(path: &Path) -> Result<ScanResult, ScanError> {
    let file = tokio::fs::File::open(path).await
        .map_err(|_| ScanError::FileAccessDenied { 
            path: path.to_string_lossy().to_string() 
        })?;
    
    // Process file with timeout
    let result = tokio::time::timeout(
        Duration::from_secs(300),
        process_file(file)
    ).await
    .map_err(|_| ScanError::ProcessingTimeout { seconds: 300 })?;
    
    Ok(result?)
}

// ❌ Bad: Generic error handling
pub async fn scan_file(path: &Path) -> Result<ScanResult, Box<dyn std::error::Error>> {
    let file = tokio::fs::File::open(path).await?;
    let result = process_file(file).await?;
    Ok(result)
}
```

#### **3. Documentation Standards**
```rust
/// Performs unified scanning with granular configuration control
/// 
/// This function implements the unified scanning architecture that processes
/// multiple detection types in a single pass for optimal performance.
/// 
/// # Arguments
/// 
/// * `file_path` - Path to the file to be scanned
/// * `config` - Granular scan configuration specifying detection types and performance settings
/// 
/// # Returns
/// 
/// Returns a `UnifiedScanResult` containing:
/// - All findings from enabled detection types
/// - Performance metrics (processing time, memory usage)
/// - Risk assessment and compliance status
/// 
/// # Errors
/// 
/// This function will return an error if:
/// - The file cannot be read or accessed
/// - Processing exceeds the configured timeout
/// - Memory usage exceeds system limits
/// - Configuration validation fails
/// 
/// # Performance
/// 
/// Expected performance ranges by profile:
/// - Quick Text: ~150ms per file
/// - Comprehensive: ~800ms per file
/// - Memory usage: 30-100MB depending on profile
/// 
/// # Example
/// 
/// ```rust
/// use privacyai::{scan_file_unified, GranularScanConfig};
/// 
/// let config = GranularScanConfig::comprehensive();
/// let result = scan_file_unified("document.pdf", &config).await?;
/// 
/// println!("Found {} privacy issues", result.findings.len());
/// println!("Risk score: {:.2}", result.risk_score);
/// ```
/// 
/// # Security
/// 
/// This function processes files locally without network access.
/// No file content is transmitted or stored outside the local system.
pub async fn scan_file_unified(
    file_path: &str,
    config: &GranularScanConfig,
) -> Result<UnifiedScanResult, ScanError> {
    // Implementation...
}
```

#### **4. Testing Standards**
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio_test;
    
    /// Test helper to create temporary test files
    fn create_test_file(content: &[u8]) -> (TempDir, PathBuf) {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test_file.txt");
        std::fs::write(&file_path, content).unwrap();
        (temp_dir, file_path)
    }
    
    #[tokio::test]
    async fn test_scan_file_unified_success() {
        // Arrange
        let (_temp_dir, file_path) = create_test_file(b"Test content with SSN: ***********");
        let config = GranularScanConfig::quick_text();
        
        // Act
        let result = scan_file_unified(file_path.to_str().unwrap(), &config).await;
        
        // Assert
        assert!(result.is_ok());
        let scan_result = result.unwrap();
        assert!(!scan_result.findings.is_empty());
        assert!(scan_result.processing_time_ms > 0);
        assert!(scan_result.risk_score > 0.0);
    }
    
    #[tokio::test]
    async fn test_scan_file_unified_file_not_found() {
        // Arrange
        let config = GranularScanConfig::quick_text();
        
        // Act
        let result = scan_file_unified("nonexistent_file.txt", &config).await;
        
        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ScanError::FileAccessDenied { path } => {
                assert_eq!(path, "nonexistent_file.txt");
            }
            _ => panic!("Expected FileAccessDenied error"),
        }
    }
    
    #[tokio::test]
    async fn test_scan_performance_benchmark() {
        // Performance test to ensure no regression
        let (_temp_dir, file_path) = create_test_file(&vec![b'A'; 1024 * 1024]); // 1MB file
        let config = GranularScanConfig::quick_text();
        
        let start = std::time::Instant::now();
        let result = scan_file_unified(file_path.to_str().unwrap(), &config).await;
        let duration = start.elapsed();
        
        assert!(result.is_ok());
        assert!(duration.as_millis() < 200, "Scan took too long: {}ms", duration.as_millis());
    }
}
```

### **TypeScript/React Code Standards**

#### **1. Component Structure**
```typescript
// ✅ Good: Well-structured React component
import React, { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';

interface AnalyticsDashboardProps {
  timePeriod: string;
  onDataUpdate?: (data: AnalyticsData) => void;
}

interface AnalyticsData {
  totalScans: number;
  avgProcessingTime: number;
  riskSummary: RiskSummary;
}

/**
 * Analytics Dashboard Component
 * 
 * Displays real-time performance metrics and risk assessment data
 * with automatic refresh capabilities.
 */
export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  timePeriod,
  onDataUpdate,
}) => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const analyticsData = await invoke<AnalyticsData>('get_analytics_dashboard', {
        timePeriod,
      });
      
      setData(analyticsData);
      onDataUpdate?.(analyticsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [timePeriod, onDataUpdate]);

  useEffect(() => {
    loadData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [loadData]);

  if (loading && !data) {
    return <div className="animate-spin">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-600">Error: {error}</div>;
  }

  return (
    <div className="analytics-dashboard">
      {/* Component implementation */}
    </div>
  );
};
```

#### **2. Type Safety**
```typescript
// ✅ Good: Comprehensive type definitions
export interface ScanConfiguration {
  readonly profile: ScanProfile;
  readonly detectionTypes: DetectionTypesConfig;
  readonly performanceSettings: PerformanceConfig;
}

export type ScanProfile = 
  | 'quick_text'
  | 'financial_audit'
  | 'identity_documents'
  | 'cryptocurrency'
  | 'file_integrity'
  | 'comprehensive';

export interface DetectionTypesConfig {
  readonly privacyDetection: boolean;
  readonly cryptocurrencyDetection: boolean;
  readonly governmentIdDetection: boolean;
  readonly fileIntegrityDetection: boolean;
}

// Type-safe API calls
export const scanFile = async (
  filePath: string,
  config: ScanConfiguration
): Promise<ScanResult> => {
  return invoke<ScanResult>('scan_file_unified', {
    filePath,
    config,
  });
};

// ❌ Bad: Loose typing
export const scanFile = async (filePath: any, config: any): Promise<any> => {
  return invoke('scan_file_unified', { filePath, config });
};
```

---

## 🔒 **Security Guidelines**

### **1. Input Validation**
```rust
// ✅ Good: Comprehensive input validation
use validator::{Validate, ValidationError};

#[derive(Debug, Validate)]
pub struct ApiRequest {
    #[validate(length(min = 1, max = 255))]
    #[validate(custom = "validate_file_path")]
    pub file_path: String,
    
    #[validate(custom = "validate_scan_profile")]
    pub profile: String,
    
    #[validate(range(min = 1, max = 3600))]
    pub timeout_seconds: u32,
}

fn validate_file_path(path: &str) -> Result<(), ValidationError> {
    // Prevent path traversal attacks
    if path.contains("..") || path.contains("~") {
        return Err(ValidationError::new("invalid_path"));
    }
    
    // Ensure path is within allowed directories
    let canonical = std::fs::canonicalize(path)
        .map_err(|_| ValidationError::new("path_not_found"))?;
    
    if !canonical.starts_with("/allowed/scan/directory") {
        return Err(ValidationError::new("path_not_allowed"));
    }
    
    Ok(())
}
```

### **2. Secure Coding Practices**
```rust
// ✅ Good: Secure random generation
use ring::rand::{SecureRandom, SystemRandom};

pub fn generate_secure_token() -> Result<String, SecurityError> {
    let rng = SystemRandom::new();
    let mut bytes = [0u8; 32];
    rng.fill(&mut bytes)?;
    Ok(base64::encode(bytes))
}

// ✅ Good: Secure file operations
use tempfile::NamedTempFile;

pub async fn process_uploaded_file(data: &[u8]) -> Result<ProcessingResult, ProcessingError> {
    // Use secure temporary file
    let mut temp_file = NamedTempFile::new()?;
    temp_file.write_all(data)?;
    
    // Process file securely
    let result = scan_file(temp_file.path()).await?;
    
    // File is automatically deleted when temp_file goes out of scope
    Ok(result)
}

// ❌ Bad: Insecure practices
use std::fs;

pub fn generate_token() -> String {
    // Insecure: predictable random generation
    format!("{}", rand::random::<u64>())
}

pub async fn process_file(path: &str) -> Result<ProcessingResult, ProcessingError> {
    // Insecure: no path validation
    let content = fs::read(path)?;
    // Process without security checks
    Ok(ProcessingResult::default())
}
```

---

## 🧪 **Testing Requirements**

### **Test Coverage Standards**
- **Unit Tests**: ≥90% line coverage for all modules
- **Integration Tests**: Complete workflow coverage
- **Performance Tests**: Benchmark critical operations
- **Security Tests**: Input validation and authentication

### **Test Organization**
```
tests/
├── unit/
│   ├── scanning/
│   ├── analytics/
│   └── config/
├── integration/
│   ├── api_tests.rs
│   ├── workflow_tests.rs
│   └── performance_tests.rs
└── security/
    ├── auth_tests.rs
    └── validation_tests.rs
```

### **Performance Testing**
```rust
// benches/scan_performance.rs
use criterion::{criterion_group, criterion_main, Criterion, BenchmarkId};

fn benchmark_unified_scanning(c: &mut Criterion) {
    let mut group = c.benchmark_group("unified_scanning");
    
    for profile in ["quick_text", "comprehensive", "financial"] {
        group.bench_with_input(
            BenchmarkId::new("profile", profile),
            &profile,
            |b, profile| {
                b.iter(|| {
                    // Benchmark scanning with specific profile
                    let config = GranularScanConfig::from_profile(profile);
                    scan_test_file(&config)
                });
            },
        );
    }
    
    group.finish();
}

criterion_group!(benches, benchmark_unified_scanning);
criterion_main!(benches);
```

---

## 📊 **Code Quality Metrics**

### **Automated Quality Checks**

#### **Clippy Configuration**
```toml
# Cargo.toml
[lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
cargo = "warn"

# Security-focused lints
integer_arithmetic = "deny"
unwrap_used = "deny"
expect_used = "warn"
panic = "deny"
```

#### **Pre-commit Hooks**
```bash
#!/bin/sh
# .git/hooks/pre-commit

# Format code
cargo fmt --all -- --check
if [ $? -ne 0 ]; then
    echo "Code formatting check failed. Run 'cargo fmt' to fix."
    exit 1
fi

# Run clippy
cargo clippy --all-targets --all-features -- -D warnings
if [ $? -ne 0 ]; then
    echo "Clippy check failed. Fix warnings before committing."
    exit 1
fi

# Run tests
cargo test
if [ $? -ne 0 ]; then
    echo "Tests failed. Fix failing tests before committing."
    exit 1
fi

# Security audit
cargo audit
if [ $? -ne 0 ]; then
    echo "Security audit failed. Address vulnerabilities before committing."
    exit 1
fi
```

### **Quality Gates**
1. **Development**: Clippy warnings, format check, unit tests
2. **Pull Request**: Full test suite, security scan, performance benchmarks
3. **Release**: Manual security review, integration testing, documentation review

---

## 📚 **Documentation Standards**

### **Code Documentation**
- **Public APIs**: Comprehensive rustdoc with examples
- **Modules**: Clear module-level documentation
- **Complex Logic**: Inline comments explaining reasoning
- **Security**: Document security considerations and assumptions

### **User Documentation**
- **API Documentation**: OpenAPI/Swagger specifications
- **User Guides**: Step-by-step instructions with screenshots
- **Technical Specifications**: Detailed implementation guides
- **Troubleshooting**: Common issues and solutions

### **Maintenance Documentation**
- **Architecture Decisions**: Document significant design choices
- **Performance Considerations**: Document optimization decisions
- **Security Model**: Document security assumptions and boundaries
- **Deployment Guide**: Production deployment instructions

---

## 🎯 **Quality Enforcement**

### **CI/CD Pipeline**
```yaml
# .github/workflows/quality.yml
name: Code Quality

on: [push, pull_request]

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          components: rustfmt, clippy
          
      - name: Format Check
        run: cargo fmt --all -- --check
        
      - name: Clippy Check
        run: cargo clippy --all-targets --all-features -- -D warnings
        
      - name: Test
        run: cargo test --all-features
        
      - name: Security Audit
        run: cargo audit
        
      - name: Performance Benchmarks
        run: cargo bench
```

### **Code Review Checklist**
- [ ] Code follows naming conventions and style guidelines
- [ ] Comprehensive error handling with appropriate error types
- [ ] Security considerations addressed (input validation, secure operations)
- [ ] Performance impact assessed and documented
- [ ] Tests cover new functionality with appropriate assertions
- [ ] Documentation updated for public APIs and significant changes
- [ ] No security vulnerabilities introduced
- [ ] Memory safety and resource management verified

**These guidelines ensure consistent, secure, and maintainable code quality across all PrivacyAI development activities while preserving the project's performance and reliability advantages.**
