#[cfg(test)]
mod tests {
    use super::*;
    use crate::analytics::performance_monitor::*;
    use crate::analytics::scan_analytics::*;
    use chrono::Utc;
    use std::collections::HashMap;

    fn create_test_data_point() -> AnalyticsDataPoint {
        let mut findings_by_severity = HashMap::new();
        findings_by_severity.insert("High".to_string(), 2);
        findings_by_severity.insert("Medium".to_string(), 3);
        
        let mut findings_by_type = HashMap::new();
        findings_by_type.insert("SSN".to_string(), 1);
        findings_by_type.insert("Credit Card".to_string(), 2);
        findings_by_type.insert("Email".to_string(), 2);
        
        AnalyticsDataPoint {
            timestamp: Utc::now(),
            scan_session_id: "test-session-1".to_string(),
            file_path: "/test/document.pdf".to_string(),
            scan_profile: "comprehensive".to_string(),
            processing_time_ms: 750,
            memory_usage_mb: 95,
            findings_count: 5,
            findings_by_severity,
            findings_by_type,
            risk_score: 0.7,
            file_size_bytes: 1024000,
            file_type: "PDF".to_string(),
            scan_success: true,
            error_message: None,
        }
    }

    #[test]
    fn test_analytics_engine_creation() {
        let config = AnalyticsConfig::default();
        let engine = AnalyticsEngine::new(config);
        
        // Verify initial state
        let metrics = engine.get_metrics(TimePeriod::Last24Hours);
        assert_eq!(metrics.total_scans, 0);
        assert_eq!(metrics.total_files, 0);
        assert_eq!(metrics.total_findings, 0);
    }

    #[test]
    fn test_analytics_data_point_processing() {
        let config = AnalyticsConfig::default();
        let mut engine = AnalyticsEngine::new(config);
        
        let data_point = create_test_data_point();
        engine.add_data_point(data_point.clone());
        
        let metrics = engine.get_metrics(TimePeriod::Last24Hours);
        assert_eq!(metrics.total_scans, 1);
        assert_eq!(metrics.total_files, 1);
        assert_eq!(metrics.total_findings, 5);
        assert_eq!(metrics.avg_processing_time_ms, 750.0);
    }

    #[test]
    fn test_performance_monitor_sample_recording() {
        let config = MonitorConfig::default();
        let mut monitor = PerformanceMonitor::new(config);
        
        let sample = PerformanceSample {
            timestamp: Utc::now(),
            scan_profile: "comprehensive".to_string(),
            processing_time_ms: 800,
            memory_usage_mb: 100,
            cpu_usage_percent: 45.0,
            file_size_bytes: 1024000,
            cache_hit_rate: 85.0,
            concurrent_operations: 1,
            success: true,
            error_type: None,
        };
        
        monitor.record_sample(sample);
        
        let metrics = monitor.get_current_metrics();
        assert_eq!(metrics.avg_scan_time_ms, 800.0);
        assert_eq!(metrics.peak_memory_usage_mb, 100);
    }

    #[test]
    fn test_scan_analytics_processing() {
        let mut analytics = ScanAnalytics::new();
        let data_points = vec![create_test_data_point()];
        
        let metrics = analytics.process_analytics(&data_points, TimePeriod::Last24Hours);
        
        assert_eq!(metrics.total_scans, 1);
        assert_eq!(metrics.total_findings, 5);
        assert!(metrics.performance_metrics.avg_scan_time_ms > 0.0);
        assert!(metrics.risk_summary.high_risk_findings > 0);
    }

    #[test]
    fn test_risk_assessment_calculation() {
        let mut analytics = ScanAnalytics::new();
        
        // Create data point with high risk
        let mut high_risk_data = create_test_data_point();
        high_risk_data.risk_score = 0.95;
        high_risk_data.findings_by_severity.insert("Critical".to_string(), 3);
        
        let data_points = vec![high_risk_data];
        let metrics = analytics.process_analytics(&data_points, TimePeriod::Last24Hours);
        
        // Should be classified as high risk due to critical findings
        match metrics.risk_summary.overall_risk_level {
            RiskLevel::Critical | RiskLevel::High => {
                // Expected result
            }
            _ => panic!("Expected high or critical risk level"),
        }
        
        assert!(metrics.risk_summary.critical_findings > 0);
    }

    #[test]
    fn test_performance_trend_analysis() {
        let config = MonitorConfig::default();
        let mut monitor = PerformanceMonitor::new(config);
        
        // Add samples with improving performance
        for i in 0..10 {
            let sample = PerformanceSample {
                timestamp: Utc::now(),
                scan_profile: "comprehensive".to_string(),
                processing_time_ms: 1000 - (i * 50), // Improving performance
                memory_usage_mb: 100,
                cpu_usage_percent: 45.0,
                file_size_bytes: 1024000,
                cache_hit_rate: 85.0,
                concurrent_operations: 1,
                success: true,
                error_type: None,
            };
            monitor.record_sample(sample);
        }
        
        let metrics = monitor.get_current_metrics();
        
        // Should detect improving trend
        match metrics.performance_trend {
            PerformanceTrend::Improving { improvement_percent } => {
                assert!(improvement_percent > 0.0);
            }
            _ => {
                // Trend analysis might be stable if variance is small
                // This is acceptable behavior
            }
        }
    }

    #[test]
    fn test_alert_generation() {
        let config = MonitorConfig::default();
        let mut monitor = PerformanceMonitor::new(config);
        
        // Add sample that should trigger memory alert
        let high_memory_sample = PerformanceSample {
            timestamp: Utc::now(),
            scan_profile: "comprehensive".to_string(),
            processing_time_ms: 800,
            memory_usage_mb: 250, // Above threshold
            cpu_usage_percent: 45.0,
            file_size_bytes: 1024000,
            cache_hit_rate: 85.0,
            concurrent_operations: 1,
            success: true,
            error_type: None,
        };
        
        monitor.record_sample(high_memory_sample);
        
        let alerts = monitor.get_active_alerts();
        
        // Should have generated a memory usage alert
        let memory_alert = alerts.iter().find(|alert| {
            matches!(alert.alert_type, AlertType::MemoryUsageHigh)
        });
        
        assert!(memory_alert.is_some(), "Expected memory usage alert to be generated");
    }

    #[test]
    fn test_export_functionality() {
        let config = AnalyticsConfig::default();
        let mut engine = AnalyticsEngine::new(config);
        
        // Add test data
        engine.add_data_point(create_test_data_point());
        
        let metrics = engine.get_metrics(TimePeriod::Last24Hours);
        
        // Test JSON serialization
        let json_result = serde_json::to_string(&metrics);
        assert!(json_result.is_ok(), "Should be able to serialize metrics to JSON");
        
        // Verify JSON contains expected data
        let json_str = json_result.unwrap();
        assert!(json_str.contains("total_scans"));
        assert!(json_str.contains("performance_metrics"));
        assert!(json_str.contains("risk_summary"));
    }

    #[test]
    fn test_time_period_filtering() {
        let config = AnalyticsConfig::default();
        let mut engine = AnalyticsEngine::new(config);
        
        // Add data points with different timestamps
        let mut old_data = create_test_data_point();
        old_data.timestamp = Utc::now() - chrono::Duration::days(2);
        
        let recent_data = create_test_data_point();
        
        engine.add_data_point(old_data);
        engine.add_data_point(recent_data);
        
        // Test last hour filter (should only include recent data)
        let hour_metrics = engine.get_metrics(TimePeriod::LastHour);
        assert_eq!(hour_metrics.total_scans, 1);
        
        // Test last week filter (should include both)
        let week_metrics = engine.get_metrics(TimePeriod::LastWeek);
        assert_eq!(week_metrics.total_scans, 2);
    }

    #[test]
    fn test_compliance_status_calculation() {
        let mut analytics = ScanAnalytics::new();
        
        // Create data with compliance violations
        let mut violation_data = create_test_data_point();
        violation_data.findings_by_severity.insert("Critical".to_string(), 5);
        
        let data_points = vec![violation_data];
        let metrics = analytics.process_analytics(&data_points, TimePeriod::Last24Hours);
        
        // Should indicate non-compliance due to critical findings
        match metrics.compliance_status.gdpr_status {
            ComplianceLevel::NonCompliant { violations_count } => {
                assert!(violations_count > 0);
            }
            ComplianceLevel::PartiallyCompliant { issues_count } => {
                assert!(issues_count > 0);
            }
            _ => {
                // Might be compliant if thresholds are high
            }
        }
    }

    #[test]
    fn test_performance_bottleneck_identification() {
        let config = MonitorConfig::default();
        let mut monitor = PerformanceMonitor::new(config);
        
        // Add samples with poor cache performance
        for _ in 0..5 {
            let sample = PerformanceSample {
                timestamp: Utc::now(),
                scan_profile: "comprehensive".to_string(),
                processing_time_ms: 800,
                memory_usage_mb: 100,
                cpu_usage_percent: 45.0,
                file_size_bytes: 1024000,
                cache_hit_rate: 50.0, // Poor cache performance
                concurrent_operations: 1,
                success: true,
                error_type: None,
            };
            monitor.record_sample(sample);
        }
        
        let metrics = monitor.get_current_metrics();
        
        // Should identify cache performance bottleneck
        let cache_bottleneck = metrics.bottlenecks.iter().find(|b| {
            b.component.contains("Cache")
        });
        
        assert!(cache_bottleneck.is_some(), "Expected cache bottleneck to be identified");
    }
}
