/* Enhanced File Drop Zone with React Dropzone and Radix UI */

.enhanced-file-dropzone {
    max-width: 800px;
    margin: 0 auto;
    padding: 24px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.dropzone-header {
    text-align: center;
    margin-bottom: 24px;
}

.dropzone-header h3 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
}

.dropzone-header p {
    margin: 0;
    color: #64748b;
    font-size: 1rem;
}

/* Drop Zone Area */
.dropzone-area {
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
    padding: 48px 24px;
    text-align: center;
    background: #f8fafc;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

.dropzone-area:hover:not(.disabled) {
    border-color: #3b82f6;
    background: #eff6ff;
}

.dropzone-area.drag-active {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: scale(1.02);
}

.dropzone-area.drag-reject {
    border-color: #ef4444;
    background: #fef2f2;
}

.dropzone-area.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.dropzone-icon {
    color: #94a3b8;
    margin-bottom: 16px;
}

.dropzone-area.drag-active .dropzone-icon {
    color: #3b82f6;
}

.dropzone-area.drag-reject .dropzone-icon {
    color: #ef4444;
}

.dropzone-content {
    max-width: 400px;
    margin: 0 auto;
}

.dropzone-text {
    margin: 0 0 8px 0;
    font-size: 1.125rem;
    color: #374151;
}

.dropzone-link {
    color: #3b82f6;
    font-weight: 500;
}

.dropzone-info {
    margin: 0;
    font-size: 0.875rem;
    color: #64748b;
}

/* Batch Controls */
.batch-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 24px;
}

.batch-stats {
    display: flex;
    gap: 24px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 2px;
}

.batch-actions {
    display: flex;
    gap: 12px;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-button.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.action-button.primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.action-button.secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.action-button.secondary:hover:not(:disabled) {
    background: #e2e8f0;
}

.action-button.warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fbbf24;
}

.action-button.warning:hover:not(:disabled) {
    background: #fde68a;
}

.action-button.danger {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fca5a5;
}

.action-button.danger:hover:not(:disabled) {
    background: #fecaca;
}

/* File List */
.file-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item:hover {
    background: #f8fafc;
}

.file-item.processing {
    background: #eff6ff;
}

.file-item.completed {
    background: #f0fdf4;
}

.file-item.error {
    background: #fef2f2;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.status-icon {
    flex-shrink: 0;
}

.status-icon.pending {
    color: #64748b;
}

.status-icon.success {
    color: #059669;
}

.status-icon.error {
    color: #dc2626;
}

.status-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: #1e293b;
    margin-bottom: 2px;
}

.file-meta {
    font-size: 0.75rem;
    color: #64748b;
}

.file-progress {
    flex: 1;
    margin: 0 16px;
}

.progress-root {
    position: relative;
    overflow: hidden;
    background: #e5e7eb;
    border-radius: 99999px;
    width: 100%;
    height: 6px;
}

.progress-indicator {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    width: 100%;
    height: 100%;
    transition: transform 660ms cubic-bezier(0.65, 0, 0.35, 1);
}

.file-results {
    font-size: 0.75rem;
    color: #059669;
    font-weight: 500;
}

.file-error {
    font-size: 0.75rem;
    color: #dc2626;
    font-weight: 500;
}

.remove-file {
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.remove-file:hover {
    color: #dc2626;
    background: #fee2e2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-file-dropzone {
        padding: 16px;
    }
    
    .dropzone-area {
        padding: 32px 16px;
    }
    
    .batch-controls {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }
    
    .batch-stats {
        justify-content: space-around;
    }
    
    .batch-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
    }
    
    .file-info {
        width: 100%;
    }
    
    .file-progress {
        width: 100%;
        margin: 0;
    }
    
    .remove-file {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .dropzone-header h3 {
        font-size: 1.25rem;
    }
    
    .dropzone-text {
        font-size: 1rem;
    }
    
    .batch-stats {
        gap: 16px;
    }
    
    .stat-value {
        font-size: 1.25rem;
    }
    
    .action-button {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}
