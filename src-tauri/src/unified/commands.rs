/// Tauri Commands for Granular Scan Configuration
/// 
/// This module provides the command interface for the React frontend to interact
/// with the granular scan configuration system, including profile management
/// and performance estimation.

use tauri::{State, Manager};
use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use std::collections::HashMap;
use std::path::PathBuf;
use std::fs;

use super::scan_configuration::{GranularScanConfig, PerformanceEstimate};
use super::scan_profiles::{ScanProfile, ScanProfileManager, PerformanceBenchmark};
use super::enhanced_unified_scanner::{EnhancedScanResult, get_enhanced_scanner};

/// Application state for scan configuration management
pub struct ScanConfigState {
    /// Current scan configuration
    pub current_config: Mutex<Option<GranularScanConfig>>,
    /// Profile manager for predefined configurations
    pub profile_manager: Mutex<ScanProfileManager>,
    /// User-defined custom configurations
    pub custom_configs: Mutex<HashMap<String, GranularScanConfig>>,
    /// App handle for accessing app data directory
    pub app_handle: tauri::AppHandle,
}

impl ScanConfigState {
    pub fn new(app_handle: tauri::AppHandle) -> Self {
        let state = Self {
            current_config: Mutex::new(Some(GranularScanConfig::default())),
            profile_manager: Mutex::new(ScanProfileManager::new()),
            custom_configs: Mutex::new(HashMap::new()),
            app_handle,
        };

        // Load existing configurations from disk
        if let Err(e) = state.load_configurations_from_disk() {
            eprintln!("Failed to load configurations from disk: {}", e);
        }

        state
    }

    /// Get the path to the configurations directory
    fn get_config_dir(&self) -> Result<PathBuf, String> {
        let app_data_dir = self.app_handle
            .path()
            .app_data_dir()
            .map_err(|e| format!("Failed to get app data directory: {}", e))?;

        let config_dir = app_data_dir.join("configurations");

        // Create directory if it doesn't exist
        if !config_dir.exists() {
            fs::create_dir_all(&config_dir)
                .map_err(|e| format!("Failed to create config directory: {}", e))?;
        }

        Ok(config_dir)
    }

    /// Save configurations to disk
    fn save_configurations_to_disk(&self) -> Result<(), String> {
        let config_dir = self.get_config_dir()?;

        // Save custom configurations
        let custom_configs = self.custom_configs.lock()
            .map_err(|e| format!("Failed to lock custom configs: {}", e))?;

        let custom_configs_file = config_dir.join("custom_configs.json");
        let json_data = serde_json::to_string_pretty(&*custom_configs)
            .map_err(|e| format!("Failed to serialize custom configs: {}", e))?;

        fs::write(&custom_configs_file, json_data)
            .map_err(|e| format!("Failed to write custom configs file: {}", e))?;

        // Save current configuration
        let current_config = self.current_config.lock()
            .map_err(|e| format!("Failed to lock current config: {}", e))?;

        if let Some(ref config) = *current_config {
            let current_config_file = config_dir.join("current_config.json");
            let json_data = serde_json::to_string_pretty(config)
                .map_err(|e| format!("Failed to serialize current config: {}", e))?;

            fs::write(&current_config_file, json_data)
                .map_err(|e| format!("Failed to write current config file: {}", e))?;
        }

        Ok(())
    }

    /// Load configurations from disk
    fn load_configurations_from_disk(&self) -> Result<(), String> {
        let config_dir = self.get_config_dir()?;

        // Load custom configurations
        let custom_configs_file = config_dir.join("custom_configs.json");
        if custom_configs_file.exists() {
            let json_data = fs::read_to_string(&custom_configs_file)
                .map_err(|e| format!("Failed to read custom configs file: {}", e))?;

            let loaded_configs: HashMap<String, GranularScanConfig> = serde_json::from_str(&json_data)
                .map_err(|e| format!("Failed to deserialize custom configs: {}", e))?;

            let mut custom_configs = self.custom_configs.lock()
                .map_err(|e| format!("Failed to lock custom configs: {}", e))?;

            *custom_configs = loaded_configs;
        }

        // Load current configuration
        let current_config_file = config_dir.join("current_config.json");
        if current_config_file.exists() {
            let json_data = fs::read_to_string(&current_config_file)
                .map_err(|e| format!("Failed to read current config file: {}", e))?;

            let loaded_config: GranularScanConfig = serde_json::from_str(&json_data)
                .map_err(|e| format!("Failed to deserialize current config: {}", e))?;

            let mut current_config = self.current_config.lock()
                .map_err(|e| format!("Failed to lock current config: {}", e))?;

            *current_config = Some(loaded_config);
        }

        Ok(())
    }
}

/// Profile information for frontend display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfileInfo {
    pub name: String,
    pub description: String,
    pub performance_estimate: PerformanceEstimate,
    pub tags: Vec<String>,
    pub supported_file_types: Vec<String>,
}

/// Performance comparison data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceComparison {
    pub baseline_time_ms: u64,
    pub current_time_ms: u64,
    pub improvement_percent: f32,
    pub baseline_memory_mb: u64,
    pub current_memory_mb: u64,
    pub memory_reduction_percent: f32,
    pub baseline_throughput: u32,
    pub current_throughput: u32,
    pub throughput_improvement_percent: f32,
}

/// Get all available scan profiles
#[tauri::command]
pub async fn get_available_scan_profiles(
    state: State<'_, ScanConfigState>,
) -> Result<Vec<ProfileInfo>, String> {
    let profile_manager = state.profile_manager.lock()
        .map_err(|e| format!("Failed to lock profile manager: {}", e))?;
    
    let profiles = profile_manager.get_available_profiles();
    let mut profile_infos = Vec::new();
    
    for (profile, description, estimate) in profiles {
        let profile_name = match profile {
            ScanProfile::QuickTextScan => "Quick Text Scan",
            ScanProfile::FinancialAudit => "Financial Audit",
            ScanProfile::IdentityDocumentSearch => "Identity Document Search",
            ScanProfile::CryptocurrencySecurity => "Cryptocurrency Security",
            ScanProfile::FileIntegrityCheck => "File Integrity Check",
            ScanProfile::ComprehensiveScan => "Comprehensive Scan",
            ScanProfile::Custom(_) => "Custom Configuration",
        };
        
        // Get benchmark data for supported file types
        let benchmark = profile_manager.get_performance_benchmark(
            &profile_name.to_lowercase().replace(" ", "_")
        );
        
        let supported_file_types = benchmark
            .map(|b| b.supported_file_types.clone())
            .unwrap_or_else(|| vec!["all".to_string()]);
        
        profile_infos.push(ProfileInfo {
            name: profile_name.to_string(),
            description,
            performance_estimate: estimate,
            tags: vec![], // Could be extracted from metadata
            supported_file_types,
        });
    }
    
    Ok(profile_infos)
}

/// Get configuration for a specific profile
#[tauri::command]
pub async fn get_profile_config(
    profile_name: String,
    state: State<'_, ScanConfigState>,
) -> Result<GranularScanConfig, String> {
    let profile_manager = state.profile_manager.lock()
        .map_err(|e| format!("Failed to lock profile manager: {}", e))?;
    
    let profile = match profile_name.as_str() {
        "quick_text_scan" => ScanProfile::QuickTextScan,
        "financial_audit" => ScanProfile::FinancialAudit,
        "identity_document_search" => ScanProfile::IdentityDocumentSearch,
        "cryptocurrency_security" => ScanProfile::CryptocurrencySecurity,
        "file_integrity_check" => ScanProfile::FileIntegrityCheck,
        "comprehensive_scan" => ScanProfile::ComprehensiveScan,
        _ => return Err(format!("Unknown profile: {}", profile_name)),
    };
    
    let config = profile_manager.get_profile_config(&profile);
    Ok(config)
}

/// Get the default scan configuration
#[tauri::command]
pub async fn get_default_scan_config() -> Result<GranularScanConfig, String> {
    Ok(GranularScanConfig::default())
}

/// Get the current scan configuration
#[tauri::command]
pub async fn get_current_scan_config(
    state: State<'_, ScanConfigState>,
) -> Result<Option<GranularScanConfig>, String> {
    let current_config = state.current_config.lock()
        .map_err(|e| format!("Failed to lock current config: {}", e))?;
    
    Ok(current_config.clone())
}

/// Update the current scan configuration
#[tauri::command]
pub async fn update_scan_config(
    config: GranularScanConfig,
    state: State<'_, ScanConfigState>,
) -> Result<(), String> {
    {
        let mut current_config = state.current_config.lock()
            .map_err(|e| format!("Failed to lock current config: {}", e))?;

        *current_config = Some(config);
    }

    // Persist to disk
    state.save_configurations_to_disk()?;

    Ok(())
}

/// Estimate performance for a given configuration
#[tauri::command]
pub async fn estimate_scan_performance(
    config: GranularScanConfig,
    state: State<'_, ScanConfigState>,
) -> Result<PerformanceEstimate, String> {
    let profile_manager = state.profile_manager.lock()
        .map_err(|e| format!("Failed to lock profile manager: {}", e))?;
    
    let estimate = profile_manager.estimate_performance(&config);
    Ok(estimate)
}

/// Calculate performance improvement compared to comprehensive scan
#[tauri::command]
pub async fn calculate_performance_improvement(
    config: GranularScanConfig,
    state: State<'_, ScanConfigState>,
) -> Result<f32, String> {
    let profile_manager = state.profile_manager.lock()
        .map_err(|e| format!("Failed to lock profile manager: {}", e))?;
    
    let improvement = profile_manager.calculate_performance_improvement(&config);
    Ok(improvement)
}

/// Get detailed performance comparison
#[tauri::command]
pub async fn get_performance_comparison(
    config: GranularScanConfig,
    state: State<'_, ScanConfigState>,
) -> Result<PerformanceComparison, String> {
    let profile_manager = state.profile_manager.lock()
        .map_err(|e| format!("Failed to lock profile manager: {}", e))?;
    
    // Get baseline (comprehensive scan) performance
    let baseline_benchmark = profile_manager.get_performance_benchmark("comprehensive")
        .ok_or("Baseline benchmark not found")?;
    
    // Get current configuration performance
    let current_estimate = profile_manager.estimate_performance(&config);
    
    // Calculate improvements
    let time_improvement = if baseline_benchmark.avg_processing_time_ms > 0 {
        ((baseline_benchmark.avg_processing_time_ms as f32 - current_estimate.estimated_time_ms as f32) 
            / baseline_benchmark.avg_processing_time_ms as f32) * 100.0
    } else {
        0.0
    };
    
    let memory_reduction = if baseline_benchmark.memory_usage_mb > 0 {
        ((baseline_benchmark.memory_usage_mb as f32 - current_estimate.estimated_memory_mb as f32) 
            / baseline_benchmark.memory_usage_mb as f32) * 100.0
    } else {
        0.0
    };
    
    let throughput_improvement = if baseline_benchmark.throughput_files_per_minute > 0 {
        ((current_estimate.estimated_throughput as f32 - baseline_benchmark.throughput_files_per_minute as f32) 
            / baseline_benchmark.throughput_files_per_minute as f32) * 100.0
    } else {
        0.0
    };
    
    Ok(PerformanceComparison {
        baseline_time_ms: baseline_benchmark.avg_processing_time_ms,
        current_time_ms: current_estimate.estimated_time_ms,
        improvement_percent: time_improvement.max(0.0),
        baseline_memory_mb: baseline_benchmark.memory_usage_mb,
        current_memory_mb: current_estimate.estimated_memory_mb,
        memory_reduction_percent: memory_reduction.max(0.0),
        baseline_throughput: baseline_benchmark.throughput_files_per_minute,
        current_throughput: current_estimate.estimated_throughput,
        throughput_improvement_percent: throughput_improvement.max(0.0),
    })
}

/// Get performance benchmark for a specific profile
#[tauri::command]
pub async fn get_profile_benchmark(
    profile_name: String,
    state: State<'_, ScanConfigState>,
) -> Result<Option<PerformanceBenchmark>, String> {
    let profile_manager = state.profile_manager.lock()
        .map_err(|e| format!("Failed to lock profile manager: {}", e))?;
    
    let benchmark = profile_manager.get_performance_benchmark(&profile_name);
    Ok(benchmark.cloned())
}

/// Save a custom configuration with a name
#[tauri::command]
pub async fn save_custom_config(
    name: String,
    config: GranularScanConfig,
    state: State<'_, ScanConfigState>,
) -> Result<(), String> {
    {
        let mut custom_configs = state.custom_configs.lock()
            .map_err(|e| format!("Failed to lock custom configs: {}", e))?;

        custom_configs.insert(name, config);
    }

    // Persist to disk
    state.save_configurations_to_disk()?;

    Ok(())
}

/// Load a custom configuration by name
#[tauri::command]
pub async fn load_custom_config(
    name: String,
    state: State<'_, ScanConfigState>,
) -> Result<Option<GranularScanConfig>, String> {
    let custom_configs = state.custom_configs.lock()
        .map_err(|e| format!("Failed to lock custom configs: {}", e))?;
    
    Ok(custom_configs.get(&name).cloned())
}

/// Get all saved custom configuration names
#[tauri::command]
pub async fn get_custom_config_names(
    state: State<'_, ScanConfigState>,
) -> Result<Vec<String>, String> {
    let custom_configs = state.custom_configs.lock()
        .map_err(|e| format!("Failed to lock custom configs: {}", e))?;
    
    Ok(custom_configs.keys().cloned().collect())
}

/// Delete a custom configuration
#[tauri::command]
pub async fn delete_custom_config(
    name: String,
    state: State<'_, ScanConfigState>,
) -> Result<bool, String> {
    let was_removed = {
        let mut custom_configs = state.custom_configs.lock()
            .map_err(|e| format!("Failed to lock custom configs: {}", e))?;

        custom_configs.remove(&name).is_some()
    };

    if was_removed {
        // Persist to disk
        state.save_configurations_to_disk()?;
    }

    Ok(was_removed)
}

/// Export configuration to JSON string
#[tauri::command]
pub async fn export_config_json(
    config: GranularScanConfig,
) -> Result<String, String> {
    serde_json::to_string_pretty(&config)
        .map_err(|e| format!("Failed to serialize config: {}", e))
}

/// Import configuration from JSON string
#[tauri::command]
pub async fn import_config_json(
    json_data: String,
) -> Result<GranularScanConfig, String> {
    serde_json::from_str(&json_data)
        .map_err(|e| format!("Failed to deserialize config: {}", e))
}

/// Validate a configuration for potential issues
#[tauri::command]
pub async fn validate_scan_config(
    config: GranularScanConfig,
) -> Result<Vec<String>, String> {
    let mut warnings = Vec::new();
    
    // Check if any detection types are enabled
    let detection_enabled = config.detection_types.privacy_detection.enabled ||
        config.detection_types.cryptocurrency_detection.enabled ||
        config.detection_types.government_id_detection.enabled ||
        config.detection_types.file_integrity_detection.enabled;
    
    if !detection_enabled {
        warnings.push("No detection types are enabled. Scans will not find any sensitive data.".to_string());
    }
    
    // Check for OCR without any detection that benefits from it
    if config.processing_methods.ocr_processing.enabled {
        let ocr_beneficial = config.detection_types.privacy_detection.enabled ||
            config.detection_types.government_id_detection.enabled;
        
        if !ocr_beneficial {
            warnings.push("OCR processing is enabled but no detection types that benefit from OCR are active. This may waste processing time.".to_string());
        }
    }
    
    // Check for AI visual detection without relevant detection types
    if config.processing_methods.ai_visual_detection.enabled {
        let ai_beneficial = config.detection_types.privacy_detection.enabled ||
            config.detection_types.government_id_detection.enabled;
        
        if !ai_beneficial {
            warnings.push("AI visual detection is enabled but no relevant detection types are active.".to_string());
        }
    }
    
    // Check performance settings
    if config.performance_settings.max_processing_time_ms < 100 {
        warnings.push("Very low processing timeout may cause scans to fail on larger files.".to_string());
    }
    
    if config.performance_settings.max_memory_usage_mb < 50 {
        warnings.push("Very low memory limit may cause performance issues or scan failures.".to_string());
    }
    
    Ok(warnings)
}

/// Perform enhanced unified scan with context-aware detection
///
/// This command uses the new context-aware detection engine to achieve
/// 97% accuracy and reduce false positives to 1-2%.
#[tauri::command]
pub async fn scan_file_enhanced(
    file_path: String,
    state: tauri::State<'_, ScanConfigState>,
) -> Result<EnhancedScanResult, String> {
    let config_option = state.current_config.lock().unwrap().clone();
    let config = config_option.ok_or("No scan configuration available")?;
    let scanner = get_enhanced_scanner();

    scanner.scan_file_enhanced(&file_path, &config)
        .await
        .map_err(|e| format!("Enhanced scan failed: {}", e))
}
