import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import App from '../../App';

describe('Privacy Detection Workflow Integration', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup comprehensive mock responses
    (global as any).mockInvoke.mockImplementation((command: string, args?: any) => {
      switch (command) {
        case 'get_system_info':
          return Promise.resolve({
            ocr_available: true,
            ai_models_available: true,
            supported_image_formats: ['jpg', 'jpeg', 'png', 'bmp', 'tiff'],
            supported_document_formats: ['pdf', 'txt', 'doc', 'docx'],
            max_file_size_mb: 100,
          });
          
        case 'initialize_privacy_engine':
          return Promise.resolve();
          
        case 'initialize_ocr_engine':
          return Promise.resolve();
          
        case 'scan_file_for_privacy':
          // Simulate different responses based on file path
          const filePath = args?.filePath || '';
          if (filePath.includes('high_risk')) {
            return Promise.resolve({
              file_path: filePath,
              risk_score: 0.95,
              findings: [
                {
                  data_type: 'SocialSecurityNumber',
                  severity: 'Critical',
                  confidence: 0.98,
                  detection_method: 'PatternMatching',
                  location: { line: 1, column_start: 5, column_end: 16 }
                },
                {
                  data_type: 'CreditCardNumber',
                  severity: 'High',
                  confidence: 0.92,
                  detection_method: 'PatternMatching',
                  location: { line: 2, column_start: 12, column_end: 31 }
                },
                {
                  data_type: 'EmailAddress',
                  severity: 'Medium',
                  confidence: 0.89,
                  detection_method: 'PatternMatching',
                  location: { line: 3, column_start: 8, column_end: 25 }
                }
              ],
              ocr_result: {
                text: 'SSN: ***********\nCC: 4111 1111 1111 1111\nEmail: <EMAIL>',
                confidence: 0.95,
                processing_time_ms: 250,
                word_count: 8,
                detected_language: 'eng'
              },
              processing_time_ms: 450,
              file_size: 2048,
              errors: []
            });
          } else if (filePath.includes('clean')) {
            return Promise.resolve({
              file_path: filePath,
              risk_score: 0.0,
              findings: [],
              processing_time_ms: 120,
              file_size: 512,
              errors: []
            });
          } else {
            return Promise.resolve({
              file_path: filePath,
              risk_score: 0.45,
              findings: [
                {
                  data_type: 'EmailAddress',
                  severity: 'Medium',
                  confidence: 0.85,
                  detection_method: 'PatternMatching',
                  location: { line: 1, column_start: 10, column_end: 27 }
                }
              ],
              processing_time_ms: 180,
              file_size: 1024,
              errors: []
            });
          }
          
        case 'quick_privacy_assessment':
          const assessmentPath = args?.filePath || '';
          if (assessmentPath.includes('high_risk')) {
            return Promise.resolve({
              risk_level: 3,
              risk_description: 'Critical privacy data detected including SSN and credit card',
              confidence: 0.95,
              processing_time_ms: 75
            });
          } else if (assessmentPath.includes('clean')) {
            return Promise.resolve({
              risk_level: 0,
              risk_description: 'No privacy risks detected',
              confidence: 0.98,
              processing_time_ms: 45
            });
          } else {
            return Promise.resolve({
              risk_level: 1,
              risk_description: 'Low privacy risk - email address detected',
              confidence: 0.82,
              processing_time_ms: 55
            });
          }
          
        default:
          return Promise.resolve();
      }
    });
  });

  it('completes full privacy scanning workflow', async () => {
    const user = userEvent.setup();
    render(<App />);

    // Wait for initialization
    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Verify system info was loaded
    expect((global as any).mockInvoke).toHaveBeenCalledWith('get_system_info');
    expect((global as any).mockInvoke).toHaveBeenCalledWith('initialize_privacy_engine');
    expect((global as any).mockInvoke).toHaveBeenCalledWith('initialize_ocr_engine');

    // Select a file
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    // Scan the file
    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Verify scan was initiated
    await waitFor(() => {
      expect((global as any).mockInvoke).toHaveBeenCalledWith('scan_file_for_privacy', 
        expect.objectContaining({ filePath: expect.any(String) })
      );
    });

    // Verify results are displayed
    await waitFor(() => {
      expect(screen.getByText(/Privacy Findings/i)).toBeInTheDocument();
      expect(screen.getByText(/EmailAddress/i)).toBeInTheDocument();
    });
  });

  it('handles high-risk file detection workflow', async () => {
    const user = userEvent.setup();
    
    // Mock high-risk file selection
    (global as any).mockInvoke.mockImplementation((command: string, args?: any) => {
      if (command === 'scan_file_for_privacy') {
        return Promise.resolve({
          file_path: 'high_risk_document.txt',
          risk_score: 0.95,
          findings: [
            {
              data_type: 'SocialSecurityNumber',
              severity: 'Critical',
              confidence: 0.98,
              detection_method: 'PatternMatching',
              location: { line: 1, column_start: 5, column_end: 16 }
            },
            {
              data_type: 'CreditCardNumber',
              severity: 'High',
              confidence: 0.92,
              detection_method: 'PatternMatching',
              location: { line: 2, column_start: 12, column_end: 31 }
            }
          ],
          processing_time_ms: 450,
          file_size: 2048,
          errors: []
        });
      }
      // Return default responses for other commands
      return Promise.resolve({
        ocr_available: true,
        ai_models_available: true,
        supported_image_formats: ['jpg', 'png'],
        supported_document_formats: ['pdf', 'txt'],
        max_file_size_mb: 100,
      });
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Perform scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Verify high-risk findings are displayed
    await waitFor(() => {
      expect(screen.getByText(/SocialSecurityNumber/i)).toBeInTheDocument();
      expect(screen.getByText(/CreditCardNumber/i)).toBeInTheDocument();
      // The severity is displayed as part of the finding, not as standalone text
      expect(screen.getByText(/Privacy Findings/i)).toBeInTheDocument();
    });

    // Verify risk score is displayed
    expect(screen.getByText(/95%/i)).toBeInTheDocument();
  });

  it('handles clean file workflow', async () => {
    const user = userEvent.setup();
    
    // Mock clean file response
    (global as any).mockInvoke.mockImplementation((command: string) => {
      if (command === 'scan_file_for_privacy') {
        return Promise.resolve({
          file_path: 'clean_document.txt',
          risk_score: 0.0,
          findings: [],
          processing_time_ms: 120,
          file_size: 512,
          errors: []
        });
      }
      return Promise.resolve({
        ocr_available: true,
        ai_models_available: true,
        supported_image_formats: ['jpg', 'png'],
        supported_document_formats: ['pdf', 'txt'],
        max_file_size_mb: 100,
      });
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Welcome dialog should be hidden in test environment

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Perform scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Verify no findings message
    await waitFor(() => {
      expect(screen.getByText(/No privacy concerns detected/i)).toBeInTheDocument();
    });
  });

  it('handles OCR workflow for image files', async () => {
    const user = userEvent.setup();
    
    // Mock OCR-enabled scan
    (global as any).mockInvoke.mockImplementation((command: string) => {
      if (command === 'scan_file_for_privacy') {
        return Promise.resolve({
          file_path: 'document_image.jpg',
          risk_score: 0.75,
          findings: [
            {
              data_type: 'EmailAddress',
              severity: 'Medium',
              confidence: 0.88,
              detection_method: 'OCRExtraction',
              location: { line: 1, column_start: 10, column_end: 27 }
            }
          ],
          ocr_result: {
            text: 'Contact: <EMAIL> for more information',
            confidence: 0.92,
            processing_time_ms: 850,
            word_count: 7,
            detected_language: 'eng'
          },
          processing_time_ms: 1200,
          file_size: 524288,
          errors: []
        });
      }
      return Promise.resolve({
        ocr_available: true,
        ai_models_available: true,
        supported_image_formats: ['jpg', 'png'],
        supported_document_formats: ['pdf', 'txt'],
        max_file_size_mb: 100,
      });
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Perform scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Verify OCR results are displayed (OCR results are included in findings, not separately displayed)
    await waitFor(() => {
      expect(screen.getByText(/EmailAddress/i)).toBeInTheDocument();
      expect(screen.getByText(/OCRExtraction/i)).toBeInTheDocument();
      expect(screen.getByText(/Privacy Findings/i)).toBeInTheDocument();
    });
  });

  it('handles error recovery workflow', async () => {
    const user = userEvent.setup();
    
    // Mock initialization success but scan failure
    (global as any).mockInvoke.mockImplementation((command: string) => {
      if (command === 'scan_file_for_privacy') {
        return Promise.reject(new Error('File access denied'));
      }
      return Promise.resolve({
        ocr_available: true,
        ai_models_available: true,
        supported_image_formats: ['jpg', 'png'],
        supported_document_formats: ['pdf', 'txt'],
        max_file_size_mb: 100,
      });
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Attempt scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Verify error is displayed
    await waitFor(() => {
      expect(screen.getByText(/File access denied/i)).toBeInTheDocument();
    });

    // Verify user can retry
    expect(scanButton).toBeEnabled();
  });

  it('displays processing time and file size information', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Perform scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Verify processing metrics are displayed
    await waitFor(() => {
      expect(screen.getByText(/180ms/i)).toBeInTheDocument(); // Processing time
      expect(screen.getByText(/Privacy Findings/i)).toBeInTheDocument(); // Results are displayed
    });
  });

  it('handles multiple consecutive scans', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    }, { timeout: 10000 });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // First scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    await waitFor(() => {
      expect(screen.getByText(/Privacy Findings/i)).toBeInTheDocument();
    });

    // Second scan (should replace results)
    await user.click(selectFileButton);
    await user.click(scanButton);

    await waitFor(() => {
      expect((global as any).mockInvoke).toHaveBeenCalledTimes(5); // 3 init + 2 scans
    });
  });
});
