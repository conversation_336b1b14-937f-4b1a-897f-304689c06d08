/// Analytics Tauri Commands
/// 
/// This module provides the command interface for the React frontend to interact
/// with the analytics system, including real-time performance monitoring,
/// trend analysis, and compliance reporting.

use tauri::State;
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use tokio::sync::Mutex as TokioMutex;
use std::collections::HashMap;
use chrono::{DateTime, Utc};

use super::{
    AnalyticsEngine, AnalyticsConfig, AnalyticsDataPoint, AnalyticsMetrics, TimePeriod,
    PerformanceMetrics, RiskSummary, ComplianceStatus
};
use super::performance_monitor::{
    PerformanceMonitor, PerformanceSample, PerformanceState, PerformanceAlert, MonitorConfig
};
use super::scan_analytics::ScanAnalytics;
use super::auto_scaling::{
    AutoScalingEngine, AutoScalingConfig, ResourceMetrics, ScalingEvent, WorkloadPrediction
};

/// Application state for analytics management
pub struct AnalyticsState {
    /// Analytics engine for processing scan results
    pub analytics_engine: Mutex<AnalyticsEngine>,

    /// Performance monitor for real-time monitoring
    pub performance_monitor: Mutex<PerformanceMonitor>,

    /// Scan analytics processor
    pub scan_analytics: Mutex<ScanAnalytics>,

    /// Analytics configuration
    pub config: Mutex<AnalyticsConfig>,

    /// Auto-scaling engine for resource management
    pub auto_scaling_engine: TokioMutex<AutoScalingEngine>,
}

impl Default for AnalyticsState {
    fn default() -> Self {
        let config = AnalyticsConfig::default();
        let monitor_config = MonitorConfig::default();

        Self {
            analytics_engine: Mutex::new(AnalyticsEngine::new(config.clone())),
            performance_monitor: Mutex::new(PerformanceMonitor::new(monitor_config)),
            scan_analytics: Mutex::new(ScanAnalytics::new()),
            config: Mutex::new(config),
            auto_scaling_engine: TokioMutex::new(AutoScalingEngine::new()),
        }
    }
}

/// Dashboard data for the analytics interface
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsDashboard {
    /// Current performance metrics
    pub performance_metrics: PerformanceMetrics,
    
    /// Risk assessment summary
    pub risk_summary: RiskSummary,
    
    /// Compliance status
    pub compliance_status: ComplianceStatus,
    
    /// Recent scan statistics
    pub scan_statistics: ScanStatistics,
    
    /// Active performance alerts
    pub active_alerts: Vec<PerformanceAlert>,
    
    /// Performance trends
    pub performance_trends: PerformanceTrends,
    
    /// Last update timestamp
    pub last_updated: DateTime<Utc>,
}

/// Scan statistics for dashboard display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanStatistics {
    /// Total scans in the current period
    pub total_scans: u64,
    
    /// Total files processed
    pub total_files: u64,
    
    /// Total findings
    pub total_findings: u64,
    
    /// Average scan time
    pub avg_scan_time_ms: f64,
    
    /// Most used scan profile
    pub most_used_profile: String,
    
    /// Scan success rate
    pub success_rate_percent: f64,
}

/// Performance trends for visualization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceTrends {
    /// Processing time trend over time
    pub processing_time_trend: Vec<TrendDataPoint>,
    
    /// Memory usage trend over time
    pub memory_usage_trend: Vec<TrendDataPoint>,
    
    /// Throughput trend over time
    pub throughput_trend: Vec<TrendDataPoint>,
    
    /// Error rate trend over time
    pub error_rate_trend: Vec<TrendDataPoint>,
}

/// Individual trend data point
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendDataPoint {
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    
    /// Value at this timestamp
    pub value: f64,
    
    /// Optional label for the data point
    pub label: Option<String>,
}

/// Get analytics dashboard data
#[tauri::command]
pub async fn get_analytics_dashboard(
    time_period: String,
    state: State<'_, AnalyticsState>,
) -> Result<AnalyticsDashboard, String> {
    let period = parse_time_period(&time_period)?;
    
    // Get analytics metrics
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    let analytics_metrics = analytics_engine.get_metrics(period);
    
    // Get current performance metrics
    let performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    let performance_metrics = performance_monitor.get_current_metrics();
    let active_alerts = performance_monitor.get_active_alerts().to_vec();
    
    // Calculate scan statistics
    let scan_statistics = ScanStatistics {
        total_scans: analytics_metrics.total_scans,
        total_files: analytics_metrics.total_files,
        total_findings: analytics_metrics.total_findings,
        avg_scan_time_ms: analytics_metrics.avg_processing_time_ms,
        most_used_profile: analytics_metrics.profile_usage_stats
            .iter()
            .max_by_key(|(_, count)| *count)
            .map(|(profile, _)| profile.clone())
            .unwrap_or_else(|| "comprehensive".to_string()),
        success_rate_percent: 100.0 - performance_metrics.error_rate_percent,
    };
    
    // Generate performance trends (simplified for now)
    let performance_trends = PerformanceTrends {
        processing_time_trend: vec![
            TrendDataPoint {
                timestamp: Utc::now() - chrono::Duration::hours(1),
                value: analytics_metrics.avg_processing_time_ms,
                label: None,
            }
        ],
        memory_usage_trend: vec![
            TrendDataPoint {
                timestamp: Utc::now() - chrono::Duration::hours(1),
                value: performance_metrics.avg_memory_usage_mb,
                label: None,
            }
        ],
        throughput_trend: vec![
            TrendDataPoint {
                timestamp: Utc::now() - chrono::Duration::hours(1),
                value: performance_metrics.throughput_files_per_minute,
                label: None,
            }
        ],
        error_rate_trend: vec![
            TrendDataPoint {
                timestamp: Utc::now() - chrono::Duration::hours(1),
                value: performance_metrics.error_rate_percent,
                label: None,
            }
        ],
    };
    
    Ok(AnalyticsDashboard {
        performance_metrics,
        risk_summary: analytics_metrics.risk_summary,
        compliance_status: analytics_metrics.compliance_status,
        scan_statistics,
        active_alerts,
        performance_trends,
        last_updated: Utc::now(),
    })
}

/// Record a new scan result for analytics
#[tauri::command]
pub async fn record_scan_result(
    scan_result: AnalyticsDataPoint,
    state: State<'_, AnalyticsState>,
) -> Result<(), String> {
    // Add to analytics engine
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    analytics_engine.add_data_point(scan_result.clone());
    
    // Create performance sample for monitoring
    let performance_sample = PerformanceSample {
        timestamp: scan_result.timestamp,
        scan_profile: scan_result.scan_profile,
        processing_time_ms: scan_result.processing_time_ms,
        memory_usage_mb: scan_result.memory_usage_mb,
        cpu_usage_percent: 0.0, // Would be measured from system
        file_size_bytes: scan_result.file_size_bytes,
        cache_hit_rate: 85.0, // Would be measured from cache system
        concurrent_operations: 1, // Would be tracked by orchestrator
        success: scan_result.scan_success,
        error_type: scan_result.error_message,
    };
    
    // Record in performance monitor
    let mut performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    performance_monitor.record_sample(performance_sample);
    
    Ok(())
}

/// Get performance metrics for a specific time period
#[tauri::command]
pub async fn get_performance_metrics(
    time_period: String,
    state: State<'_, AnalyticsState>,
) -> Result<PerformanceMetrics, String> {
    let performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    
    Ok(performance_monitor.get_current_metrics())
}

/// Get active performance alerts
#[tauri::command]
pub async fn get_performance_alerts(
    state: State<'_, AnalyticsState>,
) -> Result<Vec<PerformanceAlert>, String> {
    let performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    
    Ok(performance_monitor.get_active_alerts().to_vec())
}

/// Get current performance state
#[tauri::command]
pub async fn get_performance_state(
    state: State<'_, AnalyticsState>,
) -> Result<PerformanceState, String> {
    let performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    
    Ok(performance_monitor.get_current_state().clone())
}

/// Get risk assessment summary
#[tauri::command]
pub async fn get_risk_summary(
    time_period: String,
    state: State<'_, AnalyticsState>,
) -> Result<RiskSummary, String> {
    let period = parse_time_period(&time_period)?;
    
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    let metrics = analytics_engine.get_metrics(period);
    
    Ok(metrics.risk_summary)
}

/// Get compliance status
#[tauri::command]
pub async fn get_compliance_status(
    time_period: String,
    state: State<'_, AnalyticsState>,
) -> Result<ComplianceStatus, String> {
    let period = parse_time_period(&time_period)?;
    
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    let metrics = analytics_engine.get_metrics(period);
    
    Ok(metrics.compliance_status)
}

/// Get scan statistics for dashboard
#[tauri::command]
pub async fn get_scan_statistics(
    time_period: String,
    state: State<'_, AnalyticsState>,
) -> Result<ScanStatistics, String> {
    let period = parse_time_period(&time_period)?;
    
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    let metrics = analytics_engine.get_metrics(period);
    
    let performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    let performance_metrics = performance_monitor.get_current_metrics();
    
    Ok(ScanStatistics {
        total_scans: metrics.total_scans,
        total_files: metrics.total_files,
        total_findings: metrics.total_findings,
        avg_scan_time_ms: metrics.avg_processing_time_ms,
        most_used_profile: metrics.profile_usage_stats
            .iter()
            .max_by_key(|(_, count)| *count)
            .map(|(profile, _)| profile.clone())
            .unwrap_or_else(|| "comprehensive".to_string()),
        success_rate_percent: 100.0 - performance_metrics.error_rate_percent,
    })
}

/// Get performance trends for visualization
#[tauri::command]
pub async fn get_performance_trends(
    time_period: String,
    metric_type: String,
    state: State<'_, AnalyticsState>,
) -> Result<Vec<TrendDataPoint>, String> {
    let _period = parse_time_period(&time_period)?;
    
    // For now, return sample data - would be implemented with historical data storage
    let now = Utc::now();
    let mut trend_data = Vec::new();
    
    for i in 0..24 {
        let timestamp = now - chrono::Duration::hours(23 - i);
        let value = match metric_type.as_str() {
            "processing_time" => 750.0 + (i as f64 * 2.0) + (i as f64 % 3.0) * 50.0,
            "memory_usage" => 90.0 + (i as f64 * 0.5) + (i as f64 % 4.0) * 10.0,
            "throughput" => 80.0 - (i as f64 * 0.3) + (i as f64 % 5.0) * 5.0,
            "error_rate" => 2.0 + (i as f64 % 7.0) * 0.5,
            _ => 0.0,
        };
        
        trend_data.push(TrendDataPoint {
            timestamp,
            value,
            label: None,
        });
    }
    
    Ok(trend_data)
}

/// Update analytics configuration
#[tauri::command]
pub async fn update_analytics_config(
    new_config: AnalyticsConfig,
    state: State<'_, AnalyticsState>,
) -> Result<(), String> {
    let mut config = state.config.lock()
        .map_err(|e| format!("Failed to lock config: {}", e))?;
    
    *config = new_config.clone();
    
    // Update analytics engine with new config
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    *analytics_engine = AnalyticsEngine::new(new_config);
    
    Ok(())
}

/// Export analytics data to CSV
#[tauri::command]
pub async fn export_analytics_data(
    time_period: String,
    export_format: String,
    state: State<'_, AnalyticsState>,
) -> Result<String, String> {
    let period = parse_time_period(&time_period)?;
    
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    let metrics = analytics_engine.get_metrics(period);
    
    match export_format.as_str() {
        "csv" => {
            let mut csv_data = String::new();
            csv_data.push_str("Metric,Value\n");
            csv_data.push_str(&format!("Total Scans,{}\n", metrics.total_scans));
            csv_data.push_str(&format!("Total Files,{}\n", metrics.total_files));
            csv_data.push_str(&format!("Total Findings,{}\n", metrics.total_findings));
            csv_data.push_str(&format!("Average Processing Time (ms),{:.2}\n", metrics.avg_processing_time_ms));
            csv_data.push_str(&format!("Throughput (files/min),{:.2}\n", metrics.performance_metrics.throughput_files_per_minute));
            Ok(csv_data)
        }
        "json" => {
            serde_json::to_string_pretty(&metrics)
                .map_err(|e| format!("Failed to serialize metrics: {}", e))
        }
        _ => Err(format!("Unsupported export format: {}", export_format)),
    }
}

/// Clear analytics data
#[tauri::command]
pub async fn clear_analytics_data(
    state: State<'_, AnalyticsState>,
) -> Result<(), String> {
    let config = state.config.lock()
        .map_err(|e| format!("Failed to lock config: {}", e))?
        .clone();
    
    // Reset analytics engine
    let mut analytics_engine = state.analytics_engine.lock()
        .map_err(|e| format!("Failed to lock analytics engine: {}", e))?;
    *analytics_engine = AnalyticsEngine::new(config);
    
    // Reset performance monitor
    let monitor_config = MonitorConfig::default();
    let mut performance_monitor = state.performance_monitor.lock()
        .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
    *performance_monitor = PerformanceMonitor::new(monitor_config);
    
    // Reset scan analytics
    let mut scan_analytics = state.scan_analytics.lock()
        .map_err(|e| format!("Failed to lock scan analytics: {}", e))?;
    *scan_analytics = ScanAnalytics::new();
    
    Ok(())
}

/// Parse time period string into TimePeriod enum
fn parse_time_period(period_str: &str) -> Result<TimePeriod, String> {
    match period_str {
        "last_hour" => Ok(TimePeriod::LastHour),
        "last_24_hours" => Ok(TimePeriod::Last24Hours),
        "last_week" => Ok(TimePeriod::LastWeek),
        "last_month" => Ok(TimePeriod::LastMonth),
        "last_quarter" => Ok(TimePeriod::LastQuarter),
        "last_year" => Ok(TimePeriod::LastYear),
        _ => Err(format!("Invalid time period: {}", period_str)),
    }
}

// ============================================================================
// AUTO-SCALING COMMANDS
// ============================================================================

/// Enable auto-scaling with configuration
#[tauri::command]
pub async fn enable_auto_scaling(
    config: AutoScalingConfig,
    state: State<'_, AnalyticsState>
) -> Result<(), String> {
    let auto_scaling = state.auto_scaling_engine.lock().await;
    auto_scaling.update_config(config).await;

    Ok(())
}

/// Disable auto-scaling
#[tauri::command]
pub async fn disable_auto_scaling(
    state: State<'_, AnalyticsState>
) -> Result<(), String> {
    let auto_scaling = state.auto_scaling_engine.lock().await;
    let mut config = auto_scaling.get_config().await;
    config.enabled = false;
    auto_scaling.update_config(config).await;

    Ok(())
}

/// Update auto-scaling configuration
#[tauri::command]
pub async fn update_scaling_config(
    config: AutoScalingConfig,
    state: State<'_, AnalyticsState>
) -> Result<(), String> {
    let auto_scaling = state.auto_scaling_engine.lock().await;
    auto_scaling.update_config(config).await;

    Ok(())
}

/// Adjust thread pool size manually
#[tauri::command]
pub async fn adjust_thread_pool_size(
    size: usize,
    state: State<'_, AnalyticsState>
) -> Result<(), String> {
    // This would typically interact with the actual thread pool
    // For now, we'll just record it in the auto-scaling engine
    println!("Thread pool size adjusted to: {}", size);

    Ok(())
}

/// Get enhanced resource metrics including auto-scaling data
#[tauri::command]
pub async fn get_resource_metrics(
    state: State<'_, AnalyticsState>
) -> Result<ResourceMetrics, String> {
    // Get current performance state
    let perf_state = {
        let performance_monitor = state.performance_monitor.lock()
            .map_err(|e| format!("Failed to lock performance monitor: {}", e))?;
        performance_monitor.get_current_state().clone()
    };

    let auto_scaling = state.auto_scaling_engine.lock().await;

    // Get auto-scaling data
    let scaling_events = auto_scaling.get_scaling_events().await;
    let thread_pool_size = auto_scaling.get_current_thread_pool_size().await;
    let workload_prediction = auto_scaling.get_workload_prediction(15).await; // 15-minute horizon

    // Calculate optimal thread count
    let optimal_thread_count = auto_scaling.calculate_optimal_thread_count(
        perf_state.current_cpu_usage_percent as f64
    ).await;

    let metrics = ResourceMetrics {
        memory_usage_mb: perf_state.current_memory_usage_mb as f64,
        memory_limit_mb: 1024.0, // Default 1GB limit, should be configurable
        cpu_usage_percent: perf_state.current_cpu_usage_percent as f64,
        disk_usage_mb: 0.0, // Not tracked in current PerformanceState
        active_processes: 1, // Not tracked in current PerformanceState
        thread_pool_size,
        optimal_thread_count,
        scaling_events,
        workload_prediction,
    };

    // Process metrics through auto-scaling engine
    if let Err(e) = auto_scaling.process_metrics(&metrics).await {
        println!("Warning: Auto-scaling processing failed: {}", e);
    }

    Ok(metrics)
}
