//! Privacy Detection Engine
//! 
//! This module provides comprehensive privacy detection capabilities including:
//! - OCR text extraction from images and PDFs
//! - Pattern matching for structured privacy data (SSN, credit cards, etc.)
//! - AI-powered visual detection of privacy-sensitive content
//! - Risk assessment and confidence scoring

pub mod detector;
pub mod ocr_engine;
pub mod privacy_patterns;
pub mod ai_models;
pub mod nano_models;
pub mod progressive_processor;
pub mod intelligent_cache;
pub mod context_aware_detector;
pub mod enhanced_crypto_detector;
pub mod comprehensive_error_reporting;
pub mod ai_context_analyzer;
pub mod ai_enhanced_detector;
pub mod user_data_privacy_controls;
pub mod ocr_bridge;
pub mod document_classifier;
pub mod credit_card_processor;
pub mod image_quality_enhancer;
pub mod balanced_error_reporting;
pub mod document_template_matcher;
pub mod document_classifier_ml;
pub mod unified_document_detector;
pub mod document_detection_commands;

#[cfg(test)]
pub mod tests;

#[cfg(test)]
pub mod validation_test;

#[cfg(test)]
pub mod comprehensive_error_test;

#[cfg(test)]
pub mod ai_enhanced_test;

#[cfg(test)]
pub mod user_data_privacy_test;

#[cfg(test)]
pub mod ocr_performance_test;

#[cfg(test)]
pub mod balanced_error_reporting_test;

#[cfg(test)]
pub mod document_type_detection_test;

// Re-export main types for convenience
pub use detector::{
    PrivacyDetector, 
    PrivacyDetectionOptions, 
    PrivacyScanResult, 
    PrivacyFinding,
    PrivacyDataType,
    PrivacySeverity,
    DetectionMethod,
    FindingLocation,
    PrivacyDetectionError,
};

pub use ocr_engine::{
    OCREngine,
    OCRConfig,
    OCRResult,
    OCRError,
};

pub use nano_models::{
    NanoModel,
    NanoModelType,
    NanoResult,
    NanoModelError,
    NanoModelConfig,
    PrivacyClassifierNano,
    FaceDetectorNano,
    TextDetectorNano,
    NanoModelSuite,
    NanoModelManager,
    CacheStatistics,
};

pub use progressive_processor::{
    ProgressiveProcessor,
    ProgressiveResult,
    ProgressiveProcessingError,
};

pub use intelligent_cache::{
    IntelligentCache,
    CacheEntry,
    CachedResult,
    FileMetadata,
    CacheConfig,
    CacheStatistics as IntelligentCacheStatistics,
    CacheError,
};

pub use privacy_patterns::{
    PrivacyPatterns,
    PrivacyPatternMatch,
};

pub use ai_models::{
    AIModelManager,
    AIModelConfig,
    AIModelType,
    AIDetectionResult,
    BoundingBox,
    AIModelError,
};
