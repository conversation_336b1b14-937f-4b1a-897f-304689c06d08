/// Custom Pattern Management System for PrivacyAI
/// 
/// This module provides comprehensive custom pattern management capabilities including:
/// - User-defined cryptocurrency pattern creation and validation
/// - Pattern testing and real-time validation
/// - Import/export functionality for pattern sharing
/// - Integration with existing detection systems
/// - Performance monitoring and safety validation
/// 
/// ## Pattern Definition Structure
/// 
/// Custom patterns include:
/// - **Name**: User-defined descriptive identifier
/// - **Regex**: Validated regular expression for matching
/// - **Type**: Classification (PrivateKey, Address, ExchangeCredential, Custom)
/// - **Severity**: Risk level (Critical, High, Medium, Low)
/// - **Description**: User explanation of pattern purpose
/// - **Test Cases**: Positive and negative test examples
/// - **Validation**: Optional custom validation logic
/// 
/// ## Security Features
/// 
/// - **Regex Safety**: Prevents catastrophic backtracking
/// - **Performance Monitoring**: Tracks pattern execution time
/// - **Validation Framework**: Ensures pattern quality
/// - **Backup System**: Automatic pattern backup and restore

use serde::{Serialize, Deserialize};
use regex::Regex;
use std::collections::{HashMap, HashSet};
use std::time::{Duration, Instant};
use thiserror::Error;
use uuid::Uuid;



/// Custom pattern management errors
#[derive(Debug, Error, Clone, Serialize, Deserialize)]
pub enum CustomPatternError {
    #[error("Invalid regex pattern: {message}")]
    InvalidRegex { message: String },
    
    #[error("Pattern validation failed: {reason}")]
    ValidationFailed { reason: String },
    
    #[error("Pattern not found: {pattern_id}")]
    PatternNotFound { pattern_id: String },
    
    #[error("Pattern name already exists: {name}")]
    DuplicateName { name: String },
    
    #[error("Performance limit exceeded: {duration_ms}ms > {limit_ms}ms")]
    PerformanceLimit { duration_ms: u64, limit_ms: u64 },
    
    #[error("Import/export failed: {message}")]
    ImportExportError { message: String },
    
    #[error("Test case failed: {test_case}")]
    TestCaseFailed { test_case: String },
}

/// Custom pattern types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CustomPatternType {
    PrivateKey,
    Address,
    ExchangeCredential,
    Custom,
}

/// Custom pattern severity levels
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CustomSeverity {
    Critical,
    High,
    Medium,
    Low,
}

/// Test case for pattern validation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternTestCase {
    pub text: String,
    pub should_match: bool,
    pub description: String,
}

/// Custom pattern definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CustomPattern {
    pub id: String,
    pub name: String,
    pub regex_pattern: String,
    pub pattern_type: CustomPatternType,
    pub severity: CustomSeverity,
    pub description: String,
    pub test_cases: Vec<PatternTestCase>,
    pub is_enabled: bool,
    pub created_at: String,
    pub updated_at: String,
    pub performance_stats: PatternPerformanceStats,
    pub validation_config: PatternValidationConfig,
}

/// Pattern performance statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternPerformanceStats {
    pub total_executions: u64,
    pub total_time_ms: u64,
    pub average_time_ms: f64,
    pub max_time_ms: u64,
    pub matches_found: u64,
    pub last_execution: Option<String>,
}

/// Pattern validation configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternValidationConfig {
    pub requires_validation: bool,
    pub confidence_threshold: f32,
    pub max_execution_time_ms: u64,
    pub enable_performance_monitoring: bool,
}

/// Pattern test result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternTestResult {
    pub pattern_id: String,
    pub test_case: PatternTestCase,
    pub matched: bool,
    pub expected: bool,
    pub passed: bool,
    pub execution_time_ms: u64,
    pub match_details: Option<String>,
}

/// Pattern library category
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternCategory {
    pub id: String,
    pub name: String,
    pub description: String,
    pub pattern_ids: HashSet<String>,
    pub created_at: String,
}

/// Custom pattern manager
pub struct CustomPatternManager {
    patterns: HashMap<String, CustomPattern>,
    categories: HashMap<String, PatternCategory>,
    compiled_patterns: HashMap<String, Regex>,
    performance_limit_ms: u64,
    backup_enabled: bool,
}

impl CustomPatternManager {
    /// Create a new custom pattern manager
    pub fn new() -> Self {
        Self {
            patterns: HashMap::new(),
            categories: HashMap::new(),
            compiled_patterns: HashMap::new(),
            performance_limit_ms: 100, // 100ms limit per pattern
            backup_enabled: true,
        }
    }
    
    /// Add a new custom pattern
    pub fn add_pattern(&mut self, mut pattern: CustomPattern) -> Result<String, CustomPatternError> {
        // Validate pattern name uniqueness
        if self.patterns.values().any(|p| p.name == pattern.name) {
            return Err(CustomPatternError::DuplicateName { name: pattern.name });
        }
        
        // Generate unique ID if not provided
        if pattern.id.is_empty() {
            pattern.id = Uuid::new_v4().to_string();
        }
        
        // Validate regex pattern
        self.validate_regex(&pattern.regex_pattern)?;
        
        // Compile and cache regex
        let compiled_regex = Regex::new(&pattern.regex_pattern)
            .map_err(|e| CustomPatternError::InvalidRegex { 
                message: format!("Failed to compile regex: {}", e) 
            })?;
        
        // Run test cases
        self.validate_test_cases(&pattern, &compiled_regex)?;
        
        // Set timestamps
        let now = chrono::Utc::now().to_rfc3339();
        pattern.created_at = now.clone();
        pattern.updated_at = now;
        
        // Initialize performance stats
        pattern.performance_stats = PatternPerformanceStats {
            total_executions: 0,
            total_time_ms: 0,
            average_time_ms: 0.0,
            max_time_ms: 0,
            matches_found: 0,
            last_execution: None,
        };
        
        // Store pattern and compiled regex
        let pattern_id = pattern.id.clone();
        self.compiled_patterns.insert(pattern_id.clone(), compiled_regex);
        self.patterns.insert(pattern_id.clone(), pattern);
        
        // Create backup if enabled
        if self.backup_enabled {
            self.create_backup()?;
        }
        
        Ok(pattern_id)
    }
    
    /// Update an existing pattern
    pub fn update_pattern(&mut self, pattern_id: &str, mut updated_pattern: CustomPattern) -> Result<(), CustomPatternError> {
        // Check if pattern exists
        if !self.patterns.contains_key(pattern_id) {
            return Err(CustomPatternError::PatternNotFound { 
                pattern_id: pattern_id.to_string() 
            });
        }
        
        // Validate regex pattern
        self.validate_regex(&updated_pattern.regex_pattern)?;
        
        // Compile and cache regex
        let compiled_regex = Regex::new(&updated_pattern.regex_pattern)
            .map_err(|e| CustomPatternError::InvalidRegex { 
                message: format!("Failed to compile regex: {}", e) 
            })?;
        
        // Run test cases
        self.validate_test_cases(&updated_pattern, &compiled_regex)?;
        
        // Preserve original creation time and performance stats
        if let Some(original) = self.patterns.get(pattern_id) {
            updated_pattern.created_at = original.created_at.clone();
            updated_pattern.performance_stats = original.performance_stats.clone();
        }
        
        // Update timestamp
        updated_pattern.updated_at = chrono::Utc::now().to_rfc3339();
        updated_pattern.id = pattern_id.to_string();
        
        // Update pattern and compiled regex
        self.compiled_patterns.insert(pattern_id.to_string(), compiled_regex);
        self.patterns.insert(pattern_id.to_string(), updated_pattern);
        
        // Create backup if enabled
        if self.backup_enabled {
            self.create_backup()?;
        }
        
        Ok(())
    }
    
    /// Delete a pattern
    pub fn delete_pattern(&mut self, pattern_id: &str) -> Result<(), CustomPatternError> {
        if !self.patterns.contains_key(pattern_id) {
            return Err(CustomPatternError::PatternNotFound { 
                pattern_id: pattern_id.to_string() 
            });
        }
        
        // Remove from patterns and compiled cache
        self.patterns.remove(pattern_id);
        self.compiled_patterns.remove(pattern_id);
        
        // Remove from all categories
        for category in self.categories.values_mut() {
            category.pattern_ids.remove(pattern_id);
        }
        
        // Create backup if enabled
        if self.backup_enabled {
            self.create_backup()?;
        }
        
        Ok(())
    }
    
    /// Get a pattern by ID
    pub fn get_pattern(&self, pattern_id: &str) -> Option<&CustomPattern> {
        self.patterns.get(pattern_id)
    }
    
    /// Get all patterns
    pub fn get_all_patterns(&self) -> Vec<&CustomPattern> {
        self.patterns.values().collect()
    }
    
    /// Get enabled patterns only
    pub fn get_enabled_patterns(&self) -> Vec<&CustomPattern> {
        self.patterns.values().filter(|p| p.is_enabled).collect()
    }
    
    /// Enable/disable a pattern
    pub fn set_pattern_enabled(&mut self, pattern_id: &str, enabled: bool) -> Result<(), CustomPatternError> {
        if let Some(pattern) = self.patterns.get_mut(pattern_id) {
            pattern.is_enabled = enabled;
            pattern.updated_at = chrono::Utc::now().to_rfc3339();
            Ok(())
        } else {
            Err(CustomPatternError::PatternNotFound { 
                pattern_id: pattern_id.to_string() 
            })
        }
    }
    
    /// Test a pattern against sample text
    pub fn test_pattern(&mut self, pattern_id: &str, test_text: &str) -> Result<bool, CustomPatternError> {
        let compiled_regex = self.compiled_patterns.get(pattern_id)
            .ok_or_else(|| CustomPatternError::PatternNotFound { 
                pattern_id: pattern_id.to_string() 
            })?;
        
        let start_time = Instant::now();
        let matches = compiled_regex.is_match(test_text);
        let execution_time = start_time.elapsed();
        
        // Update performance stats
        if let Some(pattern) = self.patterns.get_mut(pattern_id) {
            let execution_ms = execution_time.as_millis() as u64;

            pattern.performance_stats.total_executions += 1;
            pattern.performance_stats.total_time_ms += execution_ms;
            pattern.performance_stats.average_time_ms = pattern.performance_stats.total_time_ms as f64 / pattern.performance_stats.total_executions as f64;
            pattern.performance_stats.max_time_ms = pattern.performance_stats.max_time_ms.max(execution_ms);

            if matches {
                pattern.performance_stats.matches_found += 1;
            }

            pattern.performance_stats.last_execution = Some(chrono::Utc::now().to_rfc3339());
        }
        
        // Check performance limit
        let execution_ms = execution_time.as_millis() as u64;
        if execution_ms > self.performance_limit_ms {
            return Err(CustomPatternError::PerformanceLimit { 
                duration_ms: execution_ms, 
                limit_ms: self.performance_limit_ms 
            });
        }
        
        Ok(matches)
    }
    
    /// Run all test cases for a pattern
    pub fn run_pattern_tests(&mut self, pattern_id: &str) -> Result<Vec<PatternTestResult>, CustomPatternError> {
        let pattern = self.patterns.get(pattern_id)
            .ok_or_else(|| CustomPatternError::PatternNotFound { 
                pattern_id: pattern_id.to_string() 
            })?.clone();
        
        let compiled_regex = self.compiled_patterns.get(pattern_id)
            .ok_or_else(|| CustomPatternError::PatternNotFound { 
                pattern_id: pattern_id.to_string() 
            })?;
        
        let mut results = Vec::new();
        
        for test_case in &pattern.test_cases {
            let start_time = Instant::now();
            let matched = compiled_regex.is_match(&test_case.text);
            let execution_time = start_time.elapsed();
            
            let passed = matched == test_case.should_match;
            
            results.push(PatternTestResult {
                pattern_id: pattern_id.to_string(),
                test_case: test_case.clone(),
                matched,
                expected: test_case.should_match,
                passed,
                execution_time_ms: execution_time.as_millis() as u64,
                match_details: if matched {
                    compiled_regex.find(&test_case.text).map(|m| m.as_str().to_string())
                } else {
                    None
                },
            });
        }
        
        Ok(results)
    }
    
    /// Validate regex pattern for safety
    fn validate_regex(&self, pattern: &str) -> Result<(), CustomPatternError> {
        // Check for potentially dangerous regex patterns
        let dangerous_patterns = [
            r"\(\?\=", // Positive lookahead
            r"\(\?\!", // Negative lookahead
            r"\(\?\<\=", // Positive lookbehind
            r"\(\?\<\!", // Negative lookbehind
            r"\(\?\>", // Atomic groups
            r"\(\?\+", // Possessive quantifiers
            r"\(\?\(", // Conditional patterns
            r"\+\+", // Possessive quantifiers (alternative form)
            r"\*\+", // Possessive quantifiers (alternative form)
            r"\?\+", // Possessive quantifiers (alternative form)
        ];
        
        for dangerous in &dangerous_patterns {
            if pattern.contains(dangerous) {
                return Err(CustomPatternError::InvalidRegex {
                    message: format!("Potentially unsafe regex feature detected: {}", dangerous)
                });
            }
        }

        // Additional check for possessive quantifiers in simpler form
        if pattern.contains("++") || pattern.contains("*+") || pattern.contains("?+") {
            return Err(CustomPatternError::InvalidRegex {
                message: "Possessive quantifiers are not allowed".to_string()
            });
        }
        
        // Check for excessive nesting
        let nesting_level = pattern.chars().filter(|&c| c == '(').count();
        if nesting_level > 10 {
            return Err(CustomPatternError::InvalidRegex { 
                message: "Excessive nesting detected (>10 levels)".to_string() 
            });
        }
        
        // Test compilation with timeout
        match Regex::new(pattern) {
            Ok(_) => Ok(()),
            Err(e) => Err(CustomPatternError::InvalidRegex { 
                message: format!("Regex compilation failed: {}", e) 
            }),
        }
    }
    
    /// Validate test cases against pattern
    fn validate_test_cases(&self, pattern: &CustomPattern, compiled_regex: &Regex) -> Result<(), CustomPatternError> {
        for test_case in &pattern.test_cases {
            let start_time = Instant::now();
            let matches = compiled_regex.is_match(&test_case.text);
            let execution_time = start_time.elapsed();
            
            // Check performance
            if execution_time.as_millis() as u64 > self.performance_limit_ms {
                return Err(CustomPatternError::PerformanceLimit { 
                    duration_ms: execution_time.as_millis() as u64, 
                    limit_ms: self.performance_limit_ms 
                });
            }
            
            // Check if test case passes
            if matches != test_case.should_match {
                return Err(CustomPatternError::TestCaseFailed { 
                    test_case: format!("'{}' - expected: {}, got: {}", 
                                     test_case.text, test_case.should_match, matches) 
                });
            }
        }
        
        Ok(())
    }
    
    /// Update performance statistics
    fn update_performance_stats(&self, stats: &mut PatternPerformanceStats, execution_time: Duration, found_match: bool) {
        let execution_ms = execution_time.as_millis() as u64;
        
        stats.total_executions += 1;
        stats.total_time_ms += execution_ms;
        stats.average_time_ms = stats.total_time_ms as f64 / stats.total_executions as f64;
        stats.max_time_ms = stats.max_time_ms.max(execution_ms);
        
        if found_match {
            stats.matches_found += 1;
        }
        
        stats.last_execution = Some(chrono::Utc::now().to_rfc3339());
    }
    
    /// Create backup of patterns
    fn create_backup(&self) -> Result<(), CustomPatternError> {
        // Implementation would save patterns to backup file
        // For now, just return Ok
        Ok(())
    }
    
    /// Get performance statistics for all patterns
    pub fn get_performance_summary(&self) -> HashMap<String, PatternPerformanceStats> {
        self.patterns.iter()
            .map(|(id, pattern)| (id.clone(), pattern.performance_stats.clone()))
            .collect()
    }
    
    /// Set performance limit
    pub fn set_performance_limit(&mut self, limit_ms: u64) {
        self.performance_limit_ms = limit_ms;
    }
    
    /// Get pattern count
    pub fn get_pattern_count(&self) -> usize {
        self.patterns.len()
    }
    
    /// Get enabled pattern count
    pub fn get_enabled_pattern_count(&self) -> usize {
        self.patterns.values().filter(|p| p.is_enabled).count()
    }

    /// Export patterns to JSON
    pub fn export_patterns(&self, pattern_ids: Option<Vec<String>>) -> Result<String, CustomPatternError> {
        let patterns_to_export: Vec<&CustomPattern> = if let Some(ids) = pattern_ids {
            ids.iter()
                .filter_map(|id| self.patterns.get(id))
                .collect()
        } else {
            self.patterns.values().collect()
        };

        serde_json::to_string_pretty(&patterns_to_export)
            .map_err(|e| CustomPatternError::ImportExportError {
                message: format!("Failed to serialize patterns: {}", e)
            })
    }

    /// Import patterns from JSON
    pub fn import_patterns(&mut self, json_data: &str, overwrite_existing: bool) -> Result<Vec<String>, CustomPatternError> {
        let imported_patterns: Vec<CustomPattern> = serde_json::from_str(json_data)
            .map_err(|e| CustomPatternError::ImportExportError {
                message: format!("Failed to parse JSON: {}", e)
            })?;

        let mut imported_ids = Vec::new();

        for mut pattern in imported_patterns {
            // Check for name conflicts
            if !overwrite_existing && self.patterns.values().any(|p| p.name == pattern.name) {
                return Err(CustomPatternError::DuplicateName { name: pattern.name });
            }

            // Generate new ID to avoid conflicts
            pattern.id = Uuid::new_v4().to_string();

            // Add pattern
            let pattern_id = self.add_pattern(pattern)?;
            imported_ids.push(pattern_id);
        }

        Ok(imported_ids)
    }

    /// Create a new category
    pub fn create_category(&mut self, name: String, description: String) -> String {
        let category_id = Uuid::new_v4().to_string();
        let category = PatternCategory {
            id: category_id.clone(),
            name,
            description,
            pattern_ids: HashSet::new(),
            created_at: chrono::Utc::now().to_rfc3339(),
        };

        self.categories.insert(category_id.clone(), category);
        category_id
    }

    /// Add pattern to category
    pub fn add_pattern_to_category(&mut self, pattern_id: &str, category_id: &str) -> Result<(), CustomPatternError> {
        // Verify pattern exists
        if !self.patterns.contains_key(pattern_id) {
            return Err(CustomPatternError::PatternNotFound {
                pattern_id: pattern_id.to_string()
            });
        }

        // Add to category
        if let Some(category) = self.categories.get_mut(category_id) {
            category.pattern_ids.insert(pattern_id.to_string());
            Ok(())
        } else {
            Err(CustomPatternError::ValidationFailed {
                reason: format!("Category not found: {}", category_id)
            })
        }
    }

    /// Get patterns in category
    pub fn get_patterns_in_category(&self, category_id: &str) -> Vec<&CustomPattern> {
        if let Some(category) = self.categories.get(category_id) {
            category.pattern_ids.iter()
                .filter_map(|id| self.patterns.get(id))
                .collect()
        } else {
            Vec::new()
        }
    }

    /// Get all categories
    pub fn get_all_categories(&self) -> Vec<&PatternCategory> {
        self.categories.values().collect()
    }

    /// Delete category
    pub fn delete_category(&mut self, category_id: &str) -> Result<(), CustomPatternError> {
        if self.categories.remove(category_id).is_some() {
            Ok(())
        } else {
            Err(CustomPatternError::ValidationFailed {
                reason: format!("Category not found: {}", category_id)
            })
        }
    }
}

impl Default for CustomPatternManager {
    fn default() -> Self {
        Self::new()
    }
}
