import { describe, it, expect } from 'vitest';

describe('Basic Test Infrastructure', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should have access to vitest globals', () => {
    expect(describe).toBeDefined();
    expect(it).toBeDefined();
    expect(expect).toBeDefined();
  });

  it('should have mocked Tauri API', () => {
    expect((global as any).__TAURI__).toBeDefined();
    expect((global as any).__TAURI__.core.invoke).toBeDefined();
  });
});
