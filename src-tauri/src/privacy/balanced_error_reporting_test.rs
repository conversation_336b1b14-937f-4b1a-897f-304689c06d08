/// Balanced Error Reporting Validation Tests
/// 
/// Tests the balanced error reporting system to ensure proper log level filtering,
/// smart aggregation, and production-ready error handling.

use std::collections::HashMap;
use crate::privacy::balanced_error_reporting::{
    BalancedErrorReporter, Log<PERSON><PERSON><PERSON>, ErrorReportingConfig
};

#[cfg(test)]
mod balanced_error_reporting_tests {
    use super::*;

    /// Test production-level logging (INFO and above only)
    #[test]
    fn test_production_log_level_filtering() {
        println!("🧪 Testing production log level filtering");
        
        let config = ErrorReportingConfig {
            log_level: LogLevel::Info,
            enable_aggregation: true,
            max_debug_messages: 50,
            ..Default::default()
        };
        
        let reporter = BalancedErrorReporter::new(config);
        
        // Start operation
        reporter.start_operation("test_operation", 3);
        
        // Log messages at different levels
        reporter.log(LogLevel::Error, "Critical error occurred", "test_component");
        reporter.log(LogLevel::Warn, "Warning: low confidence detected", "test_component");
        reporter.log(LogLevel::Info, "Processing completed successfully", "test_component");
        reporter.log(LogLevel::Debug, "Debug: detailed processing info", "test_component"); // Should be filtered
        reporter.log(LogLevel::Trace, "Trace: extreme detail", "test_component"); // Should be filtered
        
        // End operation and get report
        let report = reporter.end_operation();
        
        println!("   📊 Production Logging Results:");
        println!("      Operation: {}", report.operation_summary.operation_type);
        println!("      Total time: {}ms", report.operation_summary.total_time_ms);
        println!("      Items processed: {}", report.operation_summary.items_processed);
        println!("      Success rate: {:.1}%", report.operation_summary.success_rate * 100.0);
        
        // Verify operation completed
        assert_eq!(report.operation_summary.operation_type, "test_operation");
        assert_eq!(report.operation_summary.items_processed, 3);
        assert!(report.operation_summary.total_time_ms >= 0); // Allow zero for fast operations
        
        // In production mode, we expect minimal but meaningful output
        println!("   ✅ Production log level filtering test completed");
    }
    
    /// Test development-level logging (DEBUG and above)
    #[test]
    fn test_development_log_level_filtering() {
        println!("🧪 Testing development log level filtering");
        
        let config = ErrorReportingConfig {
            log_level: LogLevel::Debug,
            enable_aggregation: false, // Individual messages for debugging
            max_debug_messages: 200,
            ..Default::default()
        };
        
        let reporter = BalancedErrorReporter::new(config);
        
        reporter.start_operation("development_test", 1);
        
        // Log various levels
        reporter.log(LogLevel::Error, "Error message", "dev_component");
        reporter.log(LogLevel::Warn, "Warning message", "dev_component");
        reporter.log(LogLevel::Info, "Info message", "dev_component");
        reporter.log(LogLevel::Debug, "Debug message", "dev_component"); // Should be included
        reporter.log(LogLevel::Trace, "Trace message", "dev_component"); // Should be filtered
        
        let report = reporter.end_operation();
        
        println!("   📊 Development Logging Results:");
        println!("      Operation: {}", report.operation_summary.operation_type);
        println!("      Issues summary - Errors: {}, Warnings: {}", 
                report.issues_summary.total_errors, report.issues_summary.total_warnings);
        
        assert_eq!(report.operation_summary.operation_type, "development_test");
        
        println!("   ✅ Development log level filtering test completed");
    }
    
    /// Test smart aggregation functionality
    #[test]
    fn test_smart_aggregation() {
        println!("🧪 Testing smart aggregation functionality");
        
        let config = ErrorReportingConfig {
            log_level: LogLevel::Debug,
            enable_aggregation: true,
            ..Default::default()
        };
        
        let mut reporter = BalancedErrorReporter::new(config);
        
        reporter.start_operation("aggregation_test", 5);
        
        // Test aggregated logging
        let mut classification_scores = HashMap::new();
        classification_scores.insert("employment".to_string(), 0.92);
        classification_scores.insert("financial".to_string(), 0.15);
        classification_scores.insert("medical".to_string(), 0.08);
        classification_scores.insert("legal".to_string(), 0.12);
        
        reporter.log_aggregated(LogLevel::Debug, "document_classification", &classification_scores, "ai_classifier");
        
        // Test performance logging
        reporter.log_performance("ai_analysis", 45, "ai_component");
        reporter.log_performance("traditional_detection", 120, "traditional_component");
        reporter.log_performance("slow_operation", 2500, "slow_component"); // Should trigger warning
        
        // Test confidence logging
        reporter.log_confidence("ssn_detection", 0.95, 0.7, "pattern_matcher"); // High confidence
        reporter.log_confidence("weak_pattern", 0.25, 0.7, "pattern_matcher"); // Low confidence warning
        
        let report = reporter.end_operation();
        
        println!("   📊 Aggregation Test Results:");
        println!("      Performance metrics:");
        println!("         Avg processing time: {:.2}ms", report.performance_metrics.avg_processing_time_ms);
        println!("         Throughput: {:.2} items/sec", report.performance_metrics.throughput);
        println!("      Key findings: {}", report.key_findings.len());
        
        // Verify aggregation worked (allow zero for fast operations)
        assert!(report.performance_metrics.avg_processing_time_ms >= 0.0);
        assert!(report.performance_metrics.throughput >= 0.0);
        
        for finding in &report.key_findings {
            println!("         Finding: {} (confidence: {:.2})", finding.finding_type, finding.confidence);
        }
        
        println!("   ✅ Smart aggregation test completed");
    }
    
    /// Test context-aware filtering
    #[test]
    fn test_context_aware_filtering() {
        println!("🧪 Testing context-aware filtering");
        
        let config = ErrorReportingConfig {
            log_level: LogLevel::Debug,
            enable_context_filtering: true,
            ..Default::default()
        };
        
        let reporter = BalancedErrorReporter::new(config);
        
        reporter.start_operation("filtering_test", 10);
        
        // These should be filtered out as repetitive
        reporter.log(LogLevel::Debug, "validation step 1 completed", "validator");
        reporter.log(LogLevel::Debug, "checking format for pattern", "validator");
        reporter.log(LogLevel::Debug, "pattern match iteration 5", "matcher");
        reporter.log(LogLevel::Debug, "cache lookup performed", "cache");
        
        // These should be kept as important
        reporter.log(LogLevel::Info, "Document processing completed", "processor");
        reporter.log(LogLevel::Warn, "Unusual confidence pattern detected", "analyzer");
        reporter.log(LogLevel::Error, "Critical validation failure", "validator");
        
        let report = reporter.end_operation();
        
        println!("   📊 Context Filtering Results:");
        println!("      Total errors: {}", report.issues_summary.total_errors);
        println!("      Total warnings: {}", report.issues_summary.total_warnings);
        println!("      Recommendations: {}", report.recommendations.len());
        
        // Verify filtering worked - should have fewer debug messages due to filtering
        assert!(report.issues_summary.total_errors > 0); // Should capture the error
        assert!(report.issues_summary.total_warnings > 0); // Should capture the warning
        
        for recommendation in &report.recommendations {
            println!("         Recommendation: {}", recommendation);
        }
        
        println!("   ✅ Context-aware filtering test completed");
    }
    
    /// Test performance monitoring and thresholds
    #[test]
    fn test_performance_monitoring() {
        println!("🧪 Testing performance monitoring");
        
        let config = ErrorReportingConfig {
            log_level: LogLevel::Info,
            enable_performance_monitoring: true,
            performance_warning_threshold: 1000, // 1 second threshold
            confidence_warning_threshold: 0.3,
            ..Default::default()
        };
        
        let mut reporter = BalancedErrorReporter::new(config);
        
        reporter.start_operation("performance_test", 3);
        
        // Fast operations (should not trigger warnings)
        reporter.log_performance("fast_operation_1", 150, "fast_component");
        reporter.log_performance("fast_operation_2", 300, "fast_component");
        
        // Slow operation (should trigger warning)
        reporter.log_performance("slow_operation", 1500, "slow_component");
        
        // Confidence tests
        reporter.log_confidence("high_confidence_detection", 0.95, 0.7, "detector");
        reporter.log_confidence("low_confidence_detection", 0.25, 0.7, "detector"); // Should warn
        
        let report = reporter.end_operation();
        
        println!("   📊 Performance Monitoring Results:");
        println!("      Performance issues: {}", report.issues_summary.performance_issues);
        println!("      Confidence issues: {}", report.issues_summary.confidence_issues);
        println!("      Peak memory: {:.2}MB", report.performance_metrics.peak_memory_mb);
        println!("      Cache hit rate: {:.1}%", report.performance_metrics.cache_hit_rate * 100.0);
        
        // Verify performance monitoring (allow zero for fast operations)
        assert!(report.performance_metrics.avg_processing_time_ms >= 0.0);
        
        println!("   ✅ Performance monitoring test completed");
    }
    
    /// Test configuration updates
    #[test]
    fn test_configuration_updates() {
        println!("🧪 Testing configuration updates");
        
        let initial_config = ErrorReportingConfig {
            log_level: LogLevel::Error,
            ..Default::default()
        };
        
        let mut reporter = BalancedErrorReporter::new(initial_config);
        
        // Test with ERROR level (should only log errors)
        reporter.start_operation("config_test_1", 1);
        reporter.log(LogLevel::Error, "Error message", "test");
        reporter.log(LogLevel::Warn, "Warning message", "test"); // Should be filtered
        reporter.log(LogLevel::Info, "Info message", "test"); // Should be filtered
        let report1 = reporter.end_operation();
        
        // Update configuration to INFO level
        let updated_config = ErrorReportingConfig {
            log_level: LogLevel::Info,
            ..Default::default()
        };
        reporter.update_config(updated_config);
        
        // Test with INFO level (should log errors, warnings, and info)
        reporter.start_operation("config_test_2", 1);
        reporter.log(LogLevel::Error, "Error message", "test");
        reporter.log(LogLevel::Warn, "Warning message", "test"); // Should be included now
        reporter.log(LogLevel::Info, "Info message", "test"); // Should be included now
        let report2 = reporter.end_operation();
        
        println!("   📊 Configuration Update Results:");
        println!("      Test 1 (ERROR level): {} items processed", report1.operation_summary.items_processed);
        println!("      Test 2 (INFO level): {} items processed", report2.operation_summary.items_processed);
        
        // Verify configuration changes took effect
        assert_eq!(report1.operation_summary.items_processed, 1);
        assert_eq!(report2.operation_summary.items_processed, 1);
        
        // Verify current config
        let current_config = reporter.get_config();
        assert_eq!(current_config.log_level, LogLevel::Info);
        
        println!("   ✅ Configuration updates test completed");
    }
    
    /// Test integration with existing error reporting
    #[test]
    fn test_integration_with_existing_system() {
        println!("🧪 Testing integration with existing error reporting system");
        
        let config = ErrorReportingConfig::default(); // Production defaults
        let mut reporter = BalancedErrorReporter::new(config);
        
        reporter.start_operation("integration_test", 2);
        
        // Simulate AI-enhanced detection workflow
        reporter.log(LogLevel::Info, "Starting AI-enhanced detection (45 chars)", "ai_enhanced_detector");
        reporter.log_performance("traditional_detection", 69, "context_aware_detector");
        reporter.log(LogLevel::Info, "Traditional detection: 1 findings", "context_aware_detector");
        
        // Simulate AI analysis
        let mut ai_scores = HashMap::new();
        ai_scores.insert("employment".to_string(), 0.92);
        ai_scores.insert("financial".to_string(), 0.15);
        reporter.log_aggregated(LogLevel::Debug, "document_classification", &ai_scores, "ai_classifier");
        
        reporter.log_performance("ai_analysis", 5, "ai_context_analyzer");
        reporter.log(LogLevel::Info, "AI analysis: employment (confidence: 0.92)", "ai_context_analyzer");
        
        // Final results
        reporter.log_performance("total_detection", 82, "ai_enhanced_detector");
        reporter.log(LogLevel::Info, "Detection completed: 1 findings, risk: Low Risk, time: 82ms", "ai_enhanced_detector");
        
        let report = reporter.end_operation();
        
        println!("   📊 Integration Test Results:");
        println!("      Operation: {}", report.operation_summary.operation_type);
        println!("      Total time: {}ms", report.operation_summary.total_time_ms);
        println!("      Success rate: {:.1}%", report.operation_summary.success_rate * 100.0);
        println!("      Average processing time: {:.2}ms", report.performance_metrics.avg_processing_time_ms);
        
        // Verify integration worked
        assert_eq!(report.operation_summary.operation_type, "integration_test");
        assert!(report.operation_summary.success_rate > 0.0);
        assert!(report.performance_metrics.avg_processing_time_ms >= 0.0); // Allow zero for fast operations
        
        println!("   ✅ Integration test completed");
    }
}
