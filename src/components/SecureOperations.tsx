import React, { useState, useRef, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import './SecureOperations.css';

// Types for secure operations
interface SecureOperationsConfig {
    overwrite_passes: number;
    use_random_data: boolean;
    verify_deletion: boolean;
    max_file_size: number;
    archive_compression: 'None' | 'Fast' | 'Balanced' | 'Maximum';
    encryption_algorithm: 'AES256GCM' | 'ChaCha20Poly1305';
    secure_temp_files: boolean;
    temp_directory?: string;
}

interface SecureArchiveResult {
    archive_path: string;
    files_archived: string[];
    archive_size: number;
    compression_ratio: number;
    encryption_type: string;
    privacy_scan_results: any[];
    operation_duration_ms: number;
}

interface SecureDeletionReport {
    files_deleted: string[];
    bytes_deleted: number;
    failed_deletions: Array<[string, string]>;
    operation_duration_ms: number;
    verification_passed: boolean;
    overwrite_passes_completed: number;
}

interface PrivacySummary {
    total_files: number;
    files_with_privacy_data: number;
    total_detections: number;
    high_risk_files: number;
    scan_timestamp: string;
}

interface SecureOperationsResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    operation_duration_ms: number;
}

const SecureOperations: React.FC = () => {
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [selectedPaths, setSelectedPaths] = useState<string[]>([]);
    const [isProcessing, setIsProcessing] = useState(false);
    const [results, setResults] = useState<any>(null);
    const [error, setError] = useState<string | null>(null);
    const [config, setConfig] = useState<SecureOperationsConfig>({
        overwrite_passes: 7,
        use_random_data: true,
        verify_deletion: true,
        max_file_size: 1024 * 1024 * 1024, // 1GB
        archive_compression: 'Balanced',
        encryption_algorithm: 'AES256GCM',
        secure_temp_files: true,
    });
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [operation, setOperation] = useState<'archive' | 'delete' | 'scan'>('scan');
    const [progress, setProgress] = useState(0);
    
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        setSelectedFiles(files);
        setError(null);
        setResults(null);
    }, []);

    const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        const files = Array.from(event.dataTransfer.files);
        setSelectedFiles(files);
        setError(null);
        setResults(null);
    }, []);

    const handleSelectFiles = async () => {
        try {
            setError('🔍 Searching for files...');
            const filePaths = await invoke<string[]>('select_files');
            if (filePaths && filePaths.length > 0) {
                setSelectedPaths(filePaths);
                setError(`✅ Selected ${filePaths.length} file(s): ${filePaths.map(p => p.split(/[/\\]/).pop()).join(', ')}`);
                setResults(null);
                // Clear success message after 4 seconds
                setTimeout(() => setError(null), 4000);
            } else {
                setError('⚠️ No files found. Please ensure you have files in your Documents or Desktop folders.');
            }
        } catch (err) {
            setError(`❌ Failed to select files: ${err}`);
        }
    };

    const handleSelectFolder = async () => {
        try {
            setError('📁 Selecting folder...');
            const folderPath = await invoke<string | null>('select_directory');
            if (folderPath) {
                setSelectedPaths([folderPath]);
                setError(`✅ Folder selected: ${folderPath.split(/[/\\]/).pop()}`);
                setResults(null);
                // Clear success message after 4 seconds
                setTimeout(() => setError(null), 4000);
            } else {
                setError('⚠️ No folder selected. Using default user directories.');
            }
        } catch (err) {
            setError(`❌ Failed to select folder: ${err}`);
        }
    };

    const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
    }, []);

    const validatePassword = (): boolean => {
        if (operation === 'archive') {
            if (password.length < 8) {
                setError('Password must be at least 8 characters long');
                return false;
            }
            if (password !== confirmPassword) {
                setError('Passwords do not match');
                return false;
            }
        }
        return true;
    };

    const validateFiles = (): boolean => {
        if (selectedFiles.length === 0) {
            setError('Please select files to process');
            return false;
        }
        return true;
    };

    const performPrivacyScan = async () => {
        try {
            setIsProcessing(true);
            setProgress(25);

            const filePaths = selectedFiles.map(file => (file as any).path || file.name);
            
            const response = await invoke<SecureOperationsResponse<PrivacySummary>>('get_privacy_summary', {
                request: { file_paths: filePaths }
            });

            setProgress(100);

            if (response.success && response.data) {
                setResults({
                    type: 'privacy_scan',
                    data: response.data,
                    duration: response.operation_duration_ms
                });
            } else {
                setError(response.error || 'Privacy scan failed');
            }
        } catch (err) {
            setError(`Privacy scan error: ${err}`);
        } finally {
            setIsProcessing(false);
            setProgress(0);
        }
    };

    const performSecureArchive = async () => {
        try {
            setIsProcessing(true);
            setProgress(25);

            // Combine file paths from both drag/drop and dialog selection
            const dragDropPaths = selectedFiles.map(file => (file as any).path || file.name);
            const allFilePaths = [...dragDropPaths, ...selectedPaths];

            if (allFilePaths.length === 0) {
                setError('Please select files or folders first');
                setIsProcessing(false);
                return;
            }
            const outputPath = `secure_archive_${Date.now()}.zip`;

            setProgress(50);

            const response = await invoke<SecureOperationsResponse<SecureArchiveResult>>('scan_and_secure_archive', {
                request: {
                    file_paths: allFilePaths,
                    password: password,
                    output_path: outputPath,
                    compression_level: config.archive_compression,
                    encryption_type: config.encryption_algorithm
                }
            });

            setProgress(100);

            if (response.success && response.data) {
                setResults({
                    type: 'secure_archive',
                    data: response.data,
                    duration: response.operation_duration_ms
                });
            } else {
                setError(response.error || 'Secure archive creation failed');
            }
        } catch (err) {
            setError(`Archive creation error: ${err}`);
        } finally {
            setIsProcessing(false);
            setProgress(0);
        }
    };

    const performSecureDeletion = async () => {
        try {
            setIsProcessing(true);
            setProgress(25);

            // Combine file paths from both drag/drop and dialog selection
            const dragDropPaths = selectedFiles.map(file => (file as any).path || file.name);
            const allFilePaths = [...dragDropPaths, ...selectedPaths];

            if (allFilePaths.length === 0) {
                setError('Please select files or folders first');
                setIsProcessing(false);
                return;
            }

            setProgress(50);

            const response = await invoke<SecureOperationsResponse<[any[], SecureDeletionReport]>>('scan_and_secure_delete', {
                request: {
                    file_paths: allFilePaths,
                    overwrite_passes: config.overwrite_passes,
                    verify_deletion: config.verify_deletion
                }
            });

            setProgress(100);

            if (response.success && response.data) {
                setResults({
                    type: 'secure_deletion',
                    data: response.data[1], // SecureDeletionReport
                    scan_results: response.data[0], // Privacy scan results
                    duration: response.operation_duration_ms
                });
            } else {
                setError(response.error || 'Secure deletion failed');
            }
        } catch (err) {
            setError(`Secure deletion error: ${err}`);
        } finally {
            setIsProcessing(false);
            setProgress(0);
        }
    };

    const handleProcess = async () => {
        setError(null);
        setResults(null);

        if (!validateFiles()) return;
        if (!validatePassword()) return;

        switch (operation) {
            case 'scan':
                await performPrivacyScan();
                break;
            case 'archive':
                await performSecureArchive();
                break;
            case 'delete':
                await performSecureDeletion();
                break;
        }
    };

    const formatFileSize = (bytes: number): string => {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(2)} ${units[unitIndex]}`;
    };

    const formatDuration = (ms: number): string => {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        return `${(ms / 60000).toFixed(1)}m`;
    };

    return (
        <div className="secure-operations">
            <div className="secure-operations-header">
                <h2>🔒 Secure File Operations</h2>
                <p>Privacy-aware secure file archival and deletion with DoD 5220.22-M compliance</p>
            </div>

            <div className="operation-selector">
                <label>
                    <input
                        type="radio"
                        value="scan"
                        checked={operation === 'scan'}
                        onChange={(e) => setOperation(e.target.value as any)}
                    />
                    Privacy Scan
                </label>
                <label>
                    <input
                        type="radio"
                        value="archive"
                        checked={operation === 'archive'}
                        onChange={(e) => setOperation(e.target.value as any)}
                    />
                    Secure Archive
                </label>
                <label>
                    <input
                        type="radio"
                        value="delete"
                        checked={operation === 'delete'}
                        onChange={(e) => setOperation(e.target.value as any)}
                    />
                    Secure Delete
                </label>
            </div>

            {/* File Selection Buttons */}
            <div className="file-selection-buttons" style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
                    <button
                        onClick={handleSelectFiles}
                        style={{
                            flex: 1,
                            padding: '12px 16px',
                            backgroundColor: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontWeight: '500',
                            fontSize: '14px',
                            transition: 'background-color 0.2s'
                        }}
                        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
                        onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#007bff'}
                    >
                        📄 Select Files
                    </button>
                    <button
                        onClick={handleSelectFolder}
                        style={{
                            flex: 1,
                            padding: '12px 16px',
                            backgroundColor: '#28a745',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontWeight: '500',
                            fontSize: '14px',
                            transition: 'background-color 0.2s'
                        }}
                        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#1e7e34'}
                        onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#28a745'}
                    >
                        📁 Select Folder
                    </button>
                </div>
            </div>

            {/* Drag and Drop Zone */}
            <div
                className="file-drop-zone"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={handleFileSelect}
                    style={{ display: 'none' }}
                />
                <div className="drop-zone-content">
                    <p>📁 Drop files here or use buttons above</p>
                    {(selectedFiles.length > 0 || selectedPaths.length > 0) && (
                        <div className="selected-files">
                            {selectedFiles.length > 0 && (
                                <>
                                    <p>{selectedFiles.length} file(s) selected via drag/drop:</p>
                                    <ul>
                                        {selectedFiles.slice(0, 5).map((file, index) => (
                                            <li key={index}>{file.name}</li>
                                        ))}
                                        {selectedFiles.length > 5 && <li>... and {selectedFiles.length - 5} more</li>}
                                    </ul>
                                </>
                            )}
                            {selectedPaths.length > 0 && (
                                <>
                                    <p>{selectedPaths.length} path(s) selected:</p>
                                    <ul>
                                        {selectedPaths.slice(0, 5).map((path, index) => (
                                            <li key={index}>{path.split(/[/\\]/).pop()}</li>
                                        ))}
                                        {selectedPaths.length > 5 && <li>... and {selectedPaths.length - 5} more</li>}
                                    </ul>
                                </>
                            )}
                        </div>
                    )}
                </div>
            </div>

            {operation === 'archive' && (
                <div className="password-section">
                    <div className="password-input">
                        <label>Archive Password:</label>
                        <input
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder="Enter secure password (min 8 characters)"
                        />
                    </div>
                    <div className="password-input">
                        <label>Confirm Password:</label>
                        <input
                            type="password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            placeholder="Confirm password"
                        />
                    </div>
                </div>
            )}

            <div className="config-section">
                <h3>Security Configuration</h3>
                <div className="config-grid">
                    <div className="config-item">
                        <label>Overwrite Passes (DoD Standard: 7):</label>
                        <input
                            type="number"
                            min="1"
                            max="35"
                            value={config.overwrite_passes}
                            onChange={(e) => setConfig({...config, overwrite_passes: parseInt(e.target.value)})}
                        />
                    </div>
                    <div className="config-item">
                        <label>Compression Level:</label>
                        <select
                            value={config.archive_compression}
                            onChange={(e) => setConfig({...config, archive_compression: e.target.value as any})}
                        >
                            <option value="None">None</option>
                            <option value="Fast">Fast</option>
                            <option value="Balanced">Balanced</option>
                            <option value="Maximum">Maximum</option>
                        </select>
                    </div>
                    <div className="config-item">
                        <label>Encryption Algorithm:</label>
                        <select
                            value={config.encryption_algorithm}
                            onChange={(e) => setConfig({...config, encryption_algorithm: e.target.value as any})}
                        >
                            <option value="AES256GCM">AES-256-GCM</option>
                            <option value="ChaCha20Poly1305">ChaCha20-Poly1305</option>
                        </select>
                    </div>
                </div>
            </div>

            {isProcessing && (
                <div className="progress-section">
                    <div className="progress-bar">
                        <div className="progress-fill" style={{ width: `${progress}%` }}></div>
                    </div>
                    <p>Processing... {progress}%</p>
                </div>
            )}

            <button
                className="process-button"
                onClick={handleProcess}
                disabled={isProcessing || selectedFiles.length === 0}
            >
                {isProcessing ? 'Processing...' : `${operation === 'scan' ? 'Scan' : operation === 'archive' ? 'Create Archive' : 'Secure Delete'}`}
            </button>

            {error && (
                <div className="error-message">
                    <p>❌ {error}</p>
                </div>
            )}

            {results && (
                <div className="results-section">
                    <h3>Operation Results</h3>
                    {results.type === 'privacy_scan' && (
                        <div className="privacy-results">
                            <p>✅ Privacy scan completed in {formatDuration(results.duration)}</p>
                            <div className="scan-stats">
                                <div className="stat">
                                    <span className="stat-label">Total Files:</span>
                                    <span className="stat-value">{results.data.total_files}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Files with Privacy Data:</span>
                                    <span className="stat-value">{results.data.files_with_privacy_data}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Total Detections:</span>
                                    <span className="stat-value">{results.data.total_detections}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">High Risk Files:</span>
                                    <span className="stat-value">{results.data.high_risk_files}</span>
                                </div>
                            </div>
                        </div>
                    )}
                    
                    {results.type === 'secure_archive' && (
                        <div className="archive-results">
                            <p>✅ Secure archive created in {formatDuration(results.duration)}</p>
                            <div className="archive-stats">
                                <div className="stat">
                                    <span className="stat-label">Archive Path:</span>
                                    <span className="stat-value">{results.data.archive_path}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Files Archived:</span>
                                    <span className="stat-value">{results.data.files_archived.length}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Archive Size:</span>
                                    <span className="stat-value">{formatFileSize(results.data.archive_size)}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Compression Ratio:</span>
                                    <span className="stat-value">{(results.data.compression_ratio * 100).toFixed(1)}%</span>
                                </div>
                            </div>
                        </div>
                    )}
                    
                    {results.type === 'secure_deletion' && (
                        <div className="deletion-results">
                            <p>✅ Secure deletion completed in {formatDuration(results.duration)}</p>
                            <div className="deletion-stats">
                                <div className="stat">
                                    <span className="stat-label">Files Deleted:</span>
                                    <span className="stat-value">{results.data.files_deleted.length}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Bytes Deleted:</span>
                                    <span className="stat-value">{formatFileSize(results.data.bytes_deleted)}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Overwrite Passes:</span>
                                    <span className="stat-value">{results.data.overwrite_passes_completed}</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-label">Verification:</span>
                                    <span className="stat-value">{results.data.verification_passed ? '✅ Passed' : '❌ Failed'}</span>
                                </div>
                            </div>
                            {results.data.failed_deletions.length > 0 && (
                                <div className="failed-deletions">
                                    <h4>Failed Deletions:</h4>
                                    <ul>
                                        {results.data.failed_deletions.map(([path, error]: [string, string], index: number) => (
                                            <li key={index}>{path}: {error}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default SecureOperations;
