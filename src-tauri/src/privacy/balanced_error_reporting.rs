/// Balanced Error Reporting System
/// 
/// Provides production-ready error detection with configurable verbosity,
/// smart aggregation, and context-aware filtering for usable data insights.

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// Log levels for balanced reporting
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum LogLevel {
    /// Critical system failures
    Error = 0,
    /// Important issues that don't break functionality
    Warn = 1,
    /// Key operation milestones and results
    Info = 2,
    /// Detailed troubleshooting information
    Debug = 3,
    /// Extreme detail for development
    Trace = 4,
}

/// Error reporting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorReportingConfig {
    /// Current log level threshold
    pub log_level: LogLevel,
    /// Enable performance monitoring
    pub enable_performance_monitoring: bool,
    /// Enable error aggregation
    pub enable_aggregation: bool,
    /// Maximum debug messages per operation
    pub max_debug_messages: usize,
    /// Performance threshold for warnings (ms)
    pub performance_warning_threshold: u64,
    /// Confidence threshold for warnings
    pub confidence_warning_threshold: f32,
    /// Enable context-aware filtering
    pub enable_context_filtering: bool,
}

/// Aggregated error report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedErrorReport {
    /// Operation summary
    pub operation_summary: OperationSummary,
    /// Key findings
    pub key_findings: Vec<KeyFinding>,
    /// Performance metrics
    pub performance_metrics: PerformanceMetrics,
    /// Issues summary
    pub issues_summary: IssuesSummary,
    /// Recommendations
    pub recommendations: Vec<String>,
}

/// Operation summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperationSummary {
    /// Operation type
    pub operation_type: String,
    /// Total processing time
    pub total_time_ms: u64,
    /// Items processed
    pub items_processed: usize,
    /// Success rate
    pub success_rate: f32,
    /// Overall confidence
    pub average_confidence: f32,
}

/// Key finding from operation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyFinding {
    /// Finding type
    pub finding_type: String,
    /// Confidence score
    pub confidence: f32,
    /// Context information
    pub context: String,
    /// Importance level
    pub importance: ImportanceLevel,
}

/// Importance levels for findings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ImportanceLevel {
    Critical,
    High,
    Medium,
    Low,
}

/// Performance metrics summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// Average processing time per item
    pub avg_processing_time_ms: f32,
    /// Memory usage peak
    pub peak_memory_mb: f32,
    /// Cache hit rate
    pub cache_hit_rate: f32,
    /// Throughput (items per second)
    pub throughput: f32,
}

/// Issues summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IssuesSummary {
    /// Total errors
    pub total_errors: usize,
    /// Total warnings
    pub total_warnings: usize,
    /// Performance issues
    pub performance_issues: usize,
    /// Confidence issues
    pub confidence_issues: usize,
}

/// Balanced error reporter
pub struct BalancedErrorReporter {
    /// Configuration
    config: ErrorReportingConfig,
    /// Collected messages
    messages: Arc<Mutex<Vec<LogMessage>>>,
    /// Operation context
    current_operation: Arc<Mutex<Option<OperationContext>>>,
    /// Performance tracker
    performance_tracker: PerformanceTracker,
}

/// Log message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogMessage {
    /// Log level
    pub level: LogLevel,
    /// Message content
    pub message: String,
    /// Timestamp
    pub timestamp: DateTime<Utc>,
    /// Operation context
    pub operation: String,
    /// Component that generated the message
    pub component: String,
}

/// Operation context
#[derive(Debug, Clone)]
pub struct OperationContext {
    /// Operation name
    pub name: String,
    /// Start time
    pub start_time: std::time::Instant,
    /// Items being processed
    pub item_count: usize,
    /// Current item index
    pub current_item: usize,
}

/// Performance tracker
#[derive(Debug, Clone)]
pub struct PerformanceTracker {
    /// Operation timings
    pub operation_times: HashMap<String, Vec<u64>>,
    /// Memory usage samples
    pub memory_samples: Vec<f32>,
    /// Cache statistics
    pub cache_stats: CacheStats,
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// Cache hits
    pub hits: usize,
    /// Cache misses
    pub misses: usize,
}

impl BalancedErrorReporter {
    /// Create new balanced error reporter
    pub fn new(config: ErrorReportingConfig) -> Self {
        Self {
            config,
            messages: Arc::new(Mutex::new(Vec::new())),
            current_operation: Arc::new(Mutex::new(None)),
            performance_tracker: PerformanceTracker::new(),
        }
    }
    
    /// Start operation tracking
    pub fn start_operation(&self, name: &str, item_count: usize) {
        if let Ok(mut op) = self.current_operation.lock() {
            *op = Some(OperationContext {
                name: name.to_string(),
                start_time: std::time::Instant::now(),
                item_count,
                current_item: 0,
            });
        }
        
        self.log(LogLevel::Info, &format!("Starting operation: {} ({} items)", name, item_count), "system");
    }
    
    /// End operation tracking
    pub fn end_operation(&self) -> AggregatedErrorReport {
        let operation_context = if let Ok(mut op) = self.current_operation.lock() {
            op.take()
        } else {
            None
        };
        
        if let Some(context) = operation_context {
            let total_time = context.start_time.elapsed().as_millis() as u64;
            self.log(LogLevel::Info, &format!("Completed operation: {} in {}ms", context.name, total_time), "system");
            
            self.generate_aggregated_report(context, total_time)
        } else {
            AggregatedErrorReport::default()
        }
    }
    
    /// Log message with level filtering
    pub fn log(&self, level: LogLevel, message: &str, component: &str) {
        // Filter based on configured log level
        if level > self.config.log_level {
            return;
        }
        
        // Apply context-aware filtering
        if self.config.enable_context_filtering && self.should_filter_message(level, message) {
            return;
        }
        
        let operation_name = if let Ok(op) = self.current_operation.lock() {
            op.as_ref().map(|o| o.name.clone()).unwrap_or_else(|| "unknown".to_string())
        } else {
            "unknown".to_string()
        };
        
        let log_message = LogMessage {
            level,
            message: message.to_string(),
            timestamp: Utc::now(),
            operation: operation_name,
            component: component.to_string(),
        };
        
        if let Ok(mut messages) = self.messages.lock() {
            messages.push(log_message);
            
            // Limit debug messages to prevent overflow
            if level == LogLevel::Debug {
                let debug_count = messages.iter().filter(|m| m.level == LogLevel::Debug).count();
                if debug_count > self.config.max_debug_messages {
                    messages.retain(|m| m.level != LogLevel::Debug);
                    // Keep only the most recent debug messages
                    let recent_debug: Vec<_> = messages.iter().cloned()
                        .filter(|m| m.level == LogLevel::Debug)
                        .rev()
                        .take(self.config.max_debug_messages)
                        .collect();
                    messages.retain(|m| m.level != LogLevel::Debug);
                    messages.extend(recent_debug);
                }
            }
        }
    }
    
    /// Log with smart aggregation
    pub fn log_aggregated(&self, level: LogLevel, operation: &str, results: &HashMap<String, f32>, component: &str) {
        if !self.config.enable_aggregation {
            // Fall back to individual logging
            for (key, value) in results {
                self.log(level, &format!("{}: {} = {:.2}", operation, key, value), component);
            }
            return;
        }
        
        // Create aggregated message
        let summary = results.iter()
            .map(|(k, v)| format!("{}={:.2}", k, v))
            .collect::<Vec<_>>()
            .join(", ");
        
        self.log(level, &format!("{} results: {}", operation, summary), component);
    }
    
    /// Log performance metric
    pub fn log_performance(&mut self, operation: &str, duration_ms: u64, component: &str) {
        if !self.config.enable_performance_monitoring {
            return;
        }
        
        // Track performance
        self.performance_tracker.record_operation_time(operation, duration_ms);
        
        // Log warning if performance is poor
        if duration_ms > self.config.performance_warning_threshold {
            self.log(LogLevel::Warn, 
                    &format!("Slow operation: {} took {}ms (threshold: {}ms)", 
                            operation, duration_ms, self.config.performance_warning_threshold), 
                    component);
        } else if self.config.log_level >= LogLevel::Debug {
            self.log(LogLevel::Debug, 
                    &format!("Performance: {} completed in {}ms", operation, duration_ms), 
                    component);
        }
    }
    
    /// Log confidence issue
    pub fn log_confidence(&self, operation: &str, confidence: f32, threshold: f32, component: &str) {
        if confidence < self.config.confidence_warning_threshold {
            self.log(LogLevel::Warn, 
                    &format!("Low confidence: {} = {:.2} < {:.2}", operation, confidence, threshold), 
                    component);
        } else if confidence > 0.95 {
            self.log(LogLevel::Debug, 
                    &format!("High confidence: {} = {:.2}", operation, confidence), 
                    component);
        }
    }
    
    /// Check if message should be filtered
    fn should_filter_message(&self, level: LogLevel, message: &str) -> bool {
        // Filter repetitive debug messages
        if level == LogLevel::Debug {
            let repetitive_patterns = [
                "validation step",
                "checking format",
                "pattern match",
                "cache lookup",
            ];
            
            for pattern in &repetitive_patterns {
                if message.to_lowercase().contains(pattern) {
                    return true;
                }
            }
        }
        
        false
    }
    
    /// Generate aggregated report
    fn generate_aggregated_report(&self, context: OperationContext, total_time: u64) -> AggregatedErrorReport {
        let messages = if let Ok(msgs) = self.messages.lock() {
            msgs.clone()
        } else {
            Vec::new()
        };
        
        // Calculate metrics
        let error_count = messages.iter().filter(|m| m.level == LogLevel::Error).count();
        let warning_count = messages.iter().filter(|m| m.level == LogLevel::Warn).count();
        let success_rate = if context.item_count > 0 {
            1.0 - (error_count as f32 / context.item_count as f32)
        } else {
            1.0
        };
        
        // Extract key findings
        let key_findings = self.extract_key_findings(&messages);
        
        // Generate recommendations
        let recommendations = self.generate_recommendations(&messages, &context);
        
        AggregatedErrorReport {
            operation_summary: OperationSummary {
                operation_type: context.name,
                total_time_ms: total_time,
                items_processed: context.item_count,
                success_rate,
                average_confidence: 0.85, // Would calculate from actual data
            },
            key_findings,
            performance_metrics: PerformanceMetrics {
                avg_processing_time_ms: total_time as f32 / context.item_count.max(1) as f32,
                peak_memory_mb: 25.0, // Would track actual memory usage
                cache_hit_rate: self.performance_tracker.cache_stats.hit_rate(),
                throughput: context.item_count as f32 / (total_time as f32 / 1000.0),
            },
            issues_summary: IssuesSummary {
                total_errors: error_count,
                total_warnings: warning_count,
                performance_issues: 0, // Would calculate from performance data
                confidence_issues: 0,  // Would calculate from confidence data
            },
            recommendations,
        }
    }
    
    /// Extract key findings from messages
    fn extract_key_findings(&self, messages: &[LogMessage]) -> Vec<KeyFinding> {
        let mut findings = Vec::new();
        
        // Extract high-importance findings
        for message in messages {
            if message.level == LogLevel::Error {
                findings.push(KeyFinding {
                    finding_type: "Error".to_string(),
                    confidence: 1.0,
                    context: message.message.clone(),
                    importance: ImportanceLevel::Critical,
                });
            } else if message.level == LogLevel::Warn && message.message.contains("confidence") {
                findings.push(KeyFinding {
                    finding_type: "Low Confidence".to_string(),
                    confidence: 0.5, // Would extract actual confidence
                    context: message.message.clone(),
                    importance: ImportanceLevel::Medium,
                });
            }
        }
        
        findings
    }
    
    /// Generate recommendations based on messages
    fn generate_recommendations(&self, messages: &[LogMessage], _context: &OperationContext) -> Vec<String> {
        let mut recommendations = Vec::new();
        
        let error_count = messages.iter().filter(|m| m.level == LogLevel::Error).count();
        let warning_count = messages.iter().filter(|m| m.level == LogLevel::Warn).count();
        
        if error_count > 0 {
            recommendations.push("Review error logs and fix critical issues".to_string());
        }
        
        if warning_count > 5 {
            recommendations.push("Consider adjusting confidence thresholds".to_string());
        }
        
        recommendations
    }
    
    /// Get current configuration
    pub fn get_config(&self) -> &ErrorReportingConfig {
        &self.config
    }
    
    /// Update configuration
    pub fn update_config(&mut self, config: ErrorReportingConfig) {
        self.config = config;
    }
}

impl Default for ErrorReportingConfig {
    fn default() -> Self {
        Self {
            log_level: LogLevel::Info,  // Production default
            enable_performance_monitoring: true,
            enable_aggregation: true,
            max_debug_messages: 50,     // Reasonable limit
            performance_warning_threshold: 1000, // 1 second
            confidence_warning_threshold: 0.3,
            enable_context_filtering: true,
        }
    }
}

impl Default for AggregatedErrorReport {
    fn default() -> Self {
        Self {
            operation_summary: OperationSummary {
                operation_type: "unknown".to_string(),
                total_time_ms: 0,
                items_processed: 0,
                success_rate: 0.0,
                average_confidence: 0.0,
            },
            key_findings: Vec::new(),
            performance_metrics: PerformanceMetrics {
                avg_processing_time_ms: 0.0,
                peak_memory_mb: 0.0,
                cache_hit_rate: 0.0,
                throughput: 0.0,
            },
            issues_summary: IssuesSummary {
                total_errors: 0,
                total_warnings: 0,
                performance_issues: 0,
                confidence_issues: 0,
            },
            recommendations: Vec::new(),
        }
    }
}

impl PerformanceTracker {
    fn new() -> Self {
        Self {
            operation_times: HashMap::new(),
            memory_samples: Vec::new(),
            cache_stats: CacheStats { hits: 0, misses: 0 },
        }
    }
    
    fn record_operation_time(&mut self, operation: &str, duration_ms: u64) {
        self.operation_times
            .entry(operation.to_string())
            .or_insert_with(Vec::new)
            .push(duration_ms);
    }
}

impl CacheStats {
    fn hit_rate(&self) -> f32 {
        let total = self.hits + self.misses;
        if total > 0 {
            self.hits as f32 / total as f32
        } else {
            0.0
        }
    }
}

/// Convenience macros for balanced logging
#[macro_export]
macro_rules! log_error {
    ($reporter:expr, $msg:expr, $component:expr) => {
        $reporter.log(LogLevel::Error, $msg, $component)
    };
}

#[macro_export]
macro_rules! log_warn {
    ($reporter:expr, $msg:expr, $component:expr) => {
        $reporter.log(LogLevel::Warn, $msg, $component)
    };
}

#[macro_export]
macro_rules! log_info {
    ($reporter:expr, $msg:expr, $component:expr) => {
        $reporter.log(LogLevel::Info, $msg, $component)
    };
}

#[macro_export]
macro_rules! log_debug {
    ($reporter:expr, $msg:expr, $component:expr) => {
        $reporter.log(LogLevel::Debug, $msg, $component)
    };
}
