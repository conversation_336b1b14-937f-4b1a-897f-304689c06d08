/// Unified scanning architecture module
/// 
/// This module provides the unified scanning system that coordinates
/// all detection types (privacy, security, corruption, duplicates) in
/// a single optimized operation with granular configuration control.

pub mod scan_configuration;
pub mod scan_profiles;
pub mod commands;
pub mod enhanced_unified_scanner;

// Re-export key types for easier access
pub use scan_configuration::{
    GranularScanConfig,
    DetectionTypeConfig,
    ProcessingMethodConfig,
    PerformanceConfig,
    OutputConfig,
    PerformanceEstimate,
    PerformanceMode,
    OutputFormat,
    SeverityLevel,
};

pub use scan_profiles::{
    ScanProfile,
    ScanProfileManager,
    PerformanceBenchmark,
    BenchmarkResults,
    FileTypeBenchmark,
};

pub use commands::{
    ScanConfigState,
    ProfileInfo,
    PerformanceComparison,
};
