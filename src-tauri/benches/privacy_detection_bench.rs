use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use tauri_appprivacy_ai_lib::security::{PatternMatcher, SensitiveDataType};
use tauri_appprivacy_ai_lib::privacy::{PrivacyDetector, PrivacyDetectionOptions};
use tauri_appprivacy_ai_lib::core::DuplicateDetector;
use tempfile::{TempDir, NamedTempFile};
use std::fs;
use std::io::Write;

fn benchmark_pattern_matching(c: &mut Criterion) {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::Email);
    matcher.add_enabled_type(SensitiveDataType::CreditCard);
    matcher.add_enabled_type(SensitiveDataType::SocialSecurityNumber);

    let test_content = "Contact: <EMAIL>, SSN: ***********, CC: 4111 1111 1111 1111".repeat(100);

    c.bench_function("pattern_matching_basic", |b| {
        b.iter(|| {
            matcher.scan_content(black_box(&test_content), black_box("test.txt"))
        })
    });

    // Benchmark different content sizes
    let mut group = c.benchmark_group("pattern_matching_scaling");
    for size in [100, 1000, 10000, 100000].iter() {
        let large_content = "Contact: <EMAIL>, Phone: (*************\n".repeat(*size);
        group.bench_with_input(BenchmarkId::new("content_size", size), size, |b, _| {
            b.iter(|| {
                matcher.scan_content(black_box(&large_content), black_box("test.txt"))
            })
        });
    }
    group.finish();
}

fn benchmark_privacy_detector(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();

    c.bench_function("privacy_detector_creation", |b| {
        b.iter(|| {
            black_box(PrivacyDetector::new().unwrap())
        })
    });

    // Benchmark file scanning
    let mut group = c.benchmark_group("privacy_file_scanning");

    // Create test files of different sizes
    let temp_dir = TempDir::new().unwrap();
    let small_file = temp_dir.path().join("small.txt");
    let medium_file = temp_dir.path().join("medium.txt");
    let large_file = temp_dir.path().join("large.txt");

    fs::write(&small_file, "Email: <EMAIL>\nPhone: (*************").unwrap();
    fs::write(&medium_file, "Contact Information:\n".repeat(100) + "Email: <EMAIL>\nSSN: ***********").unwrap();
    fs::write(&large_file, "Document content with sensitive data:\n".repeat(1000) + "Email: <EMAIL>\nCC: 4111 1111 1111 1111\nSSN: ***********").unwrap();

    let detector = PrivacyDetector::new().unwrap();

    group.bench_function("small_file", |b| {
        b.to_async(&rt).iter(|| async {
            black_box(detector.scan_file(&small_file).await.unwrap())
        })
    });

    group.bench_function("medium_file", |b| {
        b.to_async(&rt).iter(|| async {
            black_box(detector.scan_file(&medium_file).await.unwrap())
        })
    });

    group.bench_function("large_file", |b| {
        b.to_async(&rt).iter(|| async {
            black_box(detector.scan_file(&large_file).await.unwrap())
        })
    });

    group.finish();
}

fn benchmark_duplicate_detection(c: &mut Criterion) {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create test files for duplicate detection
    let content1 = "This is duplicate content for testing";
    let content2 = "This is unique content";

    // Create various scenarios
    for i in 0..10 {
        fs::write(temp_path.join(format!("dup_{}.txt", i)), content1).unwrap();
        fs::write(temp_path.join(format!("unique_{}.txt", i)), format!("{} {}", content2, i)).unwrap();
    }

    let detector = DuplicateDetector::new(Default::default()).unwrap();

    c.bench_function("duplicate_detection_20_files", |b| {
        b.iter(|| {
            black_box(detector.detect_duplicates(temp_path).unwrap())
        })
    });
}

fn benchmark_sensitive_data_types(c: &mut Criterion) {
    let mut group = c.benchmark_group("sensitive_data_detection");

    let test_cases = vec![
        ("email", "Contact <NAME_EMAIL> for assistance"),
        ("ssn", "Social Security Number: ***********"),
        ("credit_card", "Payment: 4111 1111 1111 1111"),
        ("phone", "Call us at (*************"),
        ("mixed", "Contact: <EMAIL>, Phone: (*************, SSN: ***********, CC: 4111 1111 1111 1111"),
    ];

    for (name, content) in test_cases {
        let mut matcher = PatternMatcher::new().unwrap();
        matcher.add_enabled_type(SensitiveDataType::Email);
        matcher.add_enabled_type(SensitiveDataType::SocialSecurityNumber);
        matcher.add_enabled_type(SensitiveDataType::CreditCard);
        matcher.add_enabled_type(SensitiveDataType::PhoneNumber);

        group.bench_function(name, |b| {
            b.iter(|| {
                matcher.scan_content(black_box(content), black_box("test.txt"))
            })
        });
    }

    group.finish();
}

fn benchmark_confidence_calculation(c: &mut Criterion) {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::CreditCard);

    let test_cases = vec![
        ("valid_cc", "****************"), // Valid Luhn
        ("invalid_cc", "1234567890123456"), // Invalid Luhn
        ("formatted_cc", "4111 1111 1111 1111"), // Formatted valid
    ];

    let mut group = c.benchmark_group("confidence_calculation");

    for (name, content) in test_cases {
        group.bench_function(name, |b| {
            b.iter(|| {
                matcher.scan_content(black_box(content), black_box("test.txt"))
            })
        });
    }

    group.finish();
}

fn benchmark_memory_usage(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_efficiency");

    // Test with increasingly large content
    for size in [1_000, 10_000, 100_000, 1_000_000].iter() {
        let large_content = "a".repeat(*size);
        let mut matcher = PatternMatcher::new().unwrap();
        matcher.add_enabled_type(SensitiveDataType::Email);

        group.bench_with_input(BenchmarkId::new("large_content", size), size, |b, _| {
            b.iter(|| {
                matcher.scan_content(black_box(&large_content), black_box("test.txt"))
            })
        });
    }

    group.finish();
}

criterion_group!(
    benches,
    benchmark_pattern_matching,
    benchmark_privacy_detector,
    benchmark_duplicate_detection,
    benchmark_sensitive_data_types,
    benchmark_confidence_calculation,
    benchmark_memory_usage
);
criterion_main!(benches);
