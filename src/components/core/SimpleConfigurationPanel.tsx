import React, { useState, useEffect } from 'react';
// import { invoke } from '@tauri-apps/api/core'; // TODO: Implement backend integration
import './ConfigurationPanel.css';

// Simplified configuration interface for immediate integration
interface SimpleConfig {
    privacy_detection: {
        enabled: boolean;
        ssn_detection: boolean;
        credit_card_detection: boolean;
        email_detection: boolean;
        phone_detection: boolean;
        confidence_threshold: number;
    };
    crypto_detection: {
        enabled: boolean;
        bitcoin_detection: boolean;
        ethereum_detection: boolean;
        cardano_detection: boolean;
    };
    performance: {
        mode: 'fast' | 'balanced' | 'thorough';
        max_concurrent_files: number;
        cache_enabled: boolean;
    };
    security: {
        overwrite_passes: number;
        encryption_algorithm: 'AES256GCM' | 'ChaCha20Poly1305';
        verify_deletion: boolean;
    };
}

interface SimpleConfigurationPanelProps {
    onConfigChange?: (config: SimpleConfig) => void;
}

const SimpleConfigurationPanel: React.FC<SimpleConfigurationPanelProps> = ({
    onConfigChange
}) => {
    const [activeTab, setActiveTab] = useState<'privacy' | 'crypto' | 'performance' | 'security'>('privacy');
    const [config, setConfig] = useState<SimpleConfig>({
        privacy_detection: {
            enabled: true,
            ssn_detection: true,
            credit_card_detection: true,
            email_detection: true,
            phone_detection: true,
            confidence_threshold: 0.8,
        },
        crypto_detection: {
            enabled: true,
            bitcoin_detection: true,
            ethereum_detection: true,
            cardano_detection: true,
        },
        performance: {
            mode: 'balanced',
            max_concurrent_files: 10,
            cache_enabled: true,
        },
        security: {
            overwrite_passes: 7,
            encryption_algorithm: 'AES256GCM',
            verify_deletion: true,
        },
    });
    
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');

    useEffect(() => {
        loadConfiguration();
    }, []);

    const loadConfiguration = async () => {
        try {
            setIsLoading(true);
            // For now, use default configuration
            // In the future, this will load from backend
            console.log('Loading configuration...');
        } catch (error) {
            console.error('Failed to load configuration:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const saveConfiguration = async () => {
        try {
            setSaveStatus('saving');
            
            // Notify parent component
            if (onConfigChange) {
                onConfigChange(config);
            }
            
            console.log('Configuration saved:', config);
            setSaveStatus('saved');
            setTimeout(() => setSaveStatus('idle'), 2000);
            
        } catch (error) {
            console.error('Failed to save configuration:', error);
            setSaveStatus('error');
            setTimeout(() => setSaveStatus('idle'), 3000);
        }
    };

    const resetToDefaults = () => {
        if (window.confirm('Reset all settings to defaults? This cannot be undone.')) {
            setConfig({
                privacy_detection: {
                    enabled: true,
                    ssn_detection: true,
                    credit_card_detection: true,
                    email_detection: true,
                    phone_detection: true,
                    confidence_threshold: 0.8,
                },
                crypto_detection: {
                    enabled: true,
                    bitcoin_detection: true,
                    ethereum_detection: true,
                    cardano_detection: true,
                },
                performance: {
                    mode: 'balanced',
                    max_concurrent_files: 10,
                    cache_enabled: true,
                },
                security: {
                    overwrite_passes: 7,
                    encryption_algorithm: 'AES256GCM',
                    verify_deletion: true,
                },
            });
        }
    };

    const renderPrivacyConfig = () => (
        <div className="config-section">
            <h3>Privacy Detection Settings</h3>
            
            <div className="config-group">
                <h4>Enable Privacy Detection</h4>
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={config.privacy_detection.enabled}
                        onChange={(e) => setConfig({
                            ...config,
                            privacy_detection: {
                                ...config.privacy_detection,
                                enabled: e.target.checked
                            }
                        })}
                    />
                    <span>Enable Privacy Data Detection</span>
                </label>
                
                {config.privacy_detection.enabled && (
                    <>
                        <div className="checkbox-grid">
                            <label className="checkbox-item">
                                <input
                                    type="checkbox"
                                    checked={config.privacy_detection.ssn_detection}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        privacy_detection: {
                                            ...config.privacy_detection,
                                            ssn_detection: e.target.checked
                                        }
                                    })}
                                />
                                <span>Social Security Numbers</span>
                            </label>
                            
                            <label className="checkbox-item">
                                <input
                                    type="checkbox"
                                    checked={config.privacy_detection.credit_card_detection}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        privacy_detection: {
                                            ...config.privacy_detection,
                                            credit_card_detection: e.target.checked
                                        }
                                    })}
                                />
                                <span>Credit Card Numbers</span>
                            </label>
                            
                            <label className="checkbox-item">
                                <input
                                    type="checkbox"
                                    checked={config.privacy_detection.email_detection}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        privacy_detection: {
                                            ...config.privacy_detection,
                                            email_detection: e.target.checked
                                        }
                                    })}
                                />
                                <span>Email Addresses</span>
                            </label>
                            
                            <label className="checkbox-item">
                                <input
                                    type="checkbox"
                                    checked={config.privacy_detection.phone_detection}
                                    onChange={(e) => setConfig({
                                        ...config,
                                        privacy_detection: {
                                            ...config.privacy_detection,
                                            phone_detection: e.target.checked
                                        }
                                    })}
                                />
                                <span>Phone Numbers</span>
                            </label>
                        </div>
                        
                        <div className="slider-container">
                            <label>Detection Confidence: {(config.privacy_detection.confidence_threshold * 100).toFixed(0)}%</label>
                            <input
                                type="range"
                                min="0.1"
                                max="1.0"
                                step="0.1"
                                value={config.privacy_detection.confidence_threshold}
                                onChange={(e) => setConfig({
                                    ...config,
                                    privacy_detection: {
                                        ...config.privacy_detection,
                                        confidence_threshold: parseFloat(e.target.value)
                                    }
                                })}
                                className="confidence-slider"
                            />
                            <div className="slider-labels">
                                <span>Low (Fast)</span>
                                <span>High (Thorough)</span>
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );

    const renderCryptoConfig = () => (
        <div className="config-section">
            <h3>Cryptocurrency Detection</h3>
            
            <div className="config-group">
                <h4>Enable Cryptocurrency Detection</h4>
                <label className="checkbox-item">
                    <input
                        type="checkbox"
                        checked={config.crypto_detection.enabled}
                        onChange={(e) => setConfig({
                            ...config,
                            crypto_detection: {
                                ...config.crypto_detection,
                                enabled: e.target.checked
                            }
                        })}
                    />
                    <span>Enable Cryptocurrency Address Detection</span>
                </label>
                
                {config.crypto_detection.enabled && (
                    <div className="checkbox-grid">
                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={config.crypto_detection.bitcoin_detection}
                                onChange={(e) => setConfig({
                                    ...config,
                                    crypto_detection: {
                                        ...config.crypto_detection,
                                        bitcoin_detection: e.target.checked
                                    }
                                })}
                            />
                            <span>Bitcoin Addresses</span>
                        </label>
                        
                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={config.crypto_detection.ethereum_detection}
                                onChange={(e) => setConfig({
                                    ...config,
                                    crypto_detection: {
                                        ...config.crypto_detection,
                                        ethereum_detection: e.target.checked
                                    }
                                })}
                            />
                            <span>Ethereum Addresses</span>
                        </label>
                        
                        <label className="checkbox-item">
                            <input
                                type="checkbox"
                                checked={config.crypto_detection.cardano_detection}
                                onChange={(e) => setConfig({
                                    ...config,
                                    crypto_detection: {
                                        ...config.crypto_detection,
                                        cardano_detection: e.target.checked
                                    }
                                })}
                            />
                            <span>Cardano (ADA) Addresses</span>
                        </label>
                    </div>
                )}
            </div>
        </div>
    );

    if (isLoading) {
        return (
            <div className="configuration-panel loading">
                <div className="loading-spinner"></div>
                <p>Loading configuration...</p>
            </div>
        );
    }

    return (
        <div className="configuration-panel">
            <div className="config-header">
                <h2>⚙️ Configuration Settings</h2>
                <p>Customize PrivacyAI's detection and security settings</p>
            </div>
            
            <div className="config-tabs">
                <button
                    className={`tab-button ${activeTab === 'privacy' ? 'active' : ''}`}
                    onClick={() => setActiveTab('privacy')}
                >
                    🔍 Privacy Detection
                </button>
                <button
                    className={`tab-button ${activeTab === 'crypto' ? 'active' : ''}`}
                    onClick={() => setActiveTab('crypto')}
                >
                    ₿ Cryptocurrency
                </button>
            </div>
            
            <div className="config-content">
                {activeTab === 'privacy' && renderPrivacyConfig()}
                {activeTab === 'crypto' && renderCryptoConfig()}
            </div>
            
            <div className="config-actions">
                <button
                    className="reset-button"
                    onClick={resetToDefaults}
                    disabled={saveStatus === 'saving'}
                >
                    Reset to Defaults
                </button>
                
                <button
                    className={`save-button ${saveStatus}`}
                    onClick={saveConfiguration}
                    disabled={saveStatus === 'saving'}
                >
                    {saveStatus === 'saving' && '⏳ Saving...'}
                    {saveStatus === 'saved' && '✅ Saved'}
                    {saveStatus === 'error' && '❌ Error'}
                    {saveStatus === 'idle' && '💾 Save Configuration'}
                </button>
            </div>
        </div>
    );
};

export default SimpleConfigurationPanel;
