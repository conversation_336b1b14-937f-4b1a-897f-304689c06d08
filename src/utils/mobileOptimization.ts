/**
 * Mobile Optimization Utilities
 * Provides device detection, performance monitoring, and mobile-specific optimizations
 */

export interface DeviceCapabilities {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    hasTouch: boolean;
    screenSize: 'small' | 'medium' | 'large' | 'xlarge';
    pixelRatio: number;
    maxMemory?: number;
    connectionType?: string;
    reducedMotion: boolean;
    highContrast: boolean;
}

export interface PerformanceMetrics {
    loadTime: number;
    renderTime: number;
    interactionTime: number;
    memoryUsage: number;
    bundleSize: number;
}

export interface TouchGesture {
    type: 'tap' | 'swipe' | 'pinch' | 'long-press';
    direction?: 'up' | 'down' | 'left' | 'right';
    distance?: number;
    duration?: number;
}

class MobileOptimizationManager {
    private deviceCapabilities: DeviceCapabilities;
    private performanceObserver?: PerformanceObserver;
    private touchStartTime: number = 0;
    private touchStartPosition: { x: number; y: number } = { x: 0, y: 0 };

    constructor() {
        this.deviceCapabilities = this.detectDeviceCapabilities();
        this.initializePerformanceMonitoring();
        this.setupTouchOptimizations();
    }

    /**
     * Detect device capabilities and constraints
     */
    private detectDeviceCapabilities(): DeviceCapabilities {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) || 
                        (window.innerWidth >= 768 && window.innerWidth <= 1024);
        const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Determine screen size category
        const width = window.innerWidth;
        let screenSize: 'small' | 'medium' | 'large' | 'xlarge';
        if (width < 640) screenSize = 'small';
        else if (width < 1024) screenSize = 'medium';
        else if (width < 1440) screenSize = 'large';
        else screenSize = 'xlarge';

        // Check for accessibility preferences
        const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        const highContrast = window.matchMedia('(prefers-contrast: high)').matches;

        return {
            isMobile,
            isTablet,
            isDesktop: !isMobile && !isTablet,
            hasTouch,
            screenSize,
            pixelRatio: window.devicePixelRatio || 1,
            maxMemory: (navigator as any).deviceMemory,
            connectionType: (navigator as any).connection?.effectiveType,
            reducedMotion,
            highContrast,
        };
    }

    /**
     * Initialize performance monitoring
     */
    private initializePerformanceMonitoring(): void {
        if ('PerformanceObserver' in window) {
            this.performanceObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'navigation') {
                        this.handleNavigationTiming(entry as PerformanceNavigationTiming);
                    } else if (entry.entryType === 'paint') {
                        this.handlePaintTiming(entry);
                    }
                });
            });

            try {
                this.performanceObserver.observe({ entryTypes: ['navigation', 'paint', 'measure'] });
            } catch (error) {
                console.warn('Performance monitoring not fully supported:', error);
            }
        }
    }

    /**
     * Setup touch-specific optimizations
     */
    private setupTouchOptimizations(): void {
        if (!this.deviceCapabilities.hasTouch) return;

        // Add touch event listeners for gesture detection
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true });

        // Optimize scroll performance
        this.optimizeScrolling();
    }

    /**
     * Handle touch start events
     */
    private handleTouchStart(event: TouchEvent): void {
        this.touchStartTime = Date.now();
        const touch = event.touches[0];
        this.touchStartPosition = { x: touch.clientX, y: touch.clientY };
    }

    /**
     * Handle touch end events
     */
    private handleTouchEnd(event: TouchEvent): void {
        const duration = Date.now() - this.touchStartTime;
        const touch = event.changedTouches[0];
        const endPosition = { x: touch.clientX, y: touch.clientY };
        
        const distance = Math.sqrt(
            Math.pow(endPosition.x - this.touchStartPosition.x, 2) +
            Math.pow(endPosition.y - this.touchStartPosition.y, 2)
        );

        // Detect gesture type
        let gesture: TouchGesture;
        
        if (duration > 500 && distance < 10) {
            gesture = { type: 'long-press', duration };
        } else if (distance > 50) {
            const deltaX = endPosition.x - this.touchStartPosition.x;
            const deltaY = endPosition.y - this.touchStartPosition.y;
            
            let direction: 'up' | 'down' | 'left' | 'right';
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                direction = deltaX > 0 ? 'right' : 'left';
            } else {
                direction = deltaY > 0 ? 'down' : 'up';
            }
            
            gesture = { type: 'swipe', direction, distance, duration };
        } else {
            gesture = { type: 'tap', duration };
        }

        // Dispatch custom gesture event
        const gestureEvent = new CustomEvent('customGesture', { detail: gesture });
        event.target?.dispatchEvent(gestureEvent);
    }

    /**
     * Handle touch move events
     */
    private handleTouchMove(event: TouchEvent): void {
        // Prevent default behavior for certain gestures to improve performance
        if (event.touches.length > 1) {
            // Multi-touch gesture (pinch/zoom)
            event.preventDefault();
        }
    }

    /**
     * Optimize scrolling performance
     */
    private optimizeScrolling(): void {
        // Add smooth scrolling behavior
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Use passive event listeners for scroll events
        let scrollTimeout: number;
        document.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = window.setTimeout(() => {
                // Scroll ended - can perform expensive operations
                this.onScrollEnd();
            }, 150);
        }, { passive: true });
    }

    /**
     * Handle scroll end events
     */
    private onScrollEnd(): void {
        // Trigger lazy loading or other optimizations
        const event = new CustomEvent('scrollEnd');
        document.dispatchEvent(event);
    }

    /**
     * Handle navigation timing
     */
    private handleNavigationTiming(entry: PerformanceNavigationTiming): void {
        const metrics: PerformanceMetrics = {
            loadTime: entry.loadEventEnd - entry.loadEventStart,
            renderTime: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            interactionTime: entry.domInteractive - (entry as any).domLoading,
            memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
            bundleSize: entry.transferSize || 0,
        };

        // Log performance metrics for mobile devices
        if (this.deviceCapabilities.isMobile) {
            console.log('Mobile Performance Metrics:', metrics);
            
            // Alert if performance is poor
            if (metrics.loadTime > 3000) {
                console.warn('Slow load time detected on mobile device');
            }
        }
    }

    /**
     * Handle paint timing
     */
    private handlePaintTiming(entry: PerformanceEntry): void {
        if (entry.name === 'first-contentful-paint') {
            const fcp = entry.startTime;
            if (this.deviceCapabilities.isMobile && fcp > 2000) {
                console.warn('Slow First Contentful Paint on mobile:', fcp);
            }
        }
    }

    /**
     * Get device capabilities
     */
    public getDeviceCapabilities(): DeviceCapabilities {
        return { ...this.deviceCapabilities };
    }

    /**
     * Check if device has limited resources
     */
    public hasLimitedResources(): boolean {
        return this.deviceCapabilities.isMobile || 
               (this.deviceCapabilities.maxMemory && this.deviceCapabilities.maxMemory < 4) ||
               this.deviceCapabilities.connectionType === 'slow-2g' ||
               this.deviceCapabilities.connectionType === '2g';
    }

    /**
     * Get optimal configuration for current device
     */
    public getOptimalConfiguration(): {
        enableAnimations: boolean;
        maxConcurrentFiles: number;
        enableCaching: boolean;
        imageQuality: 'low' | 'medium' | 'high';
        enableLazyLoading: boolean;
    } {
        const hasLimitedResources = this.hasLimitedResources();
        
        return {
            enableAnimations: !this.deviceCapabilities.reducedMotion && !hasLimitedResources,
            maxConcurrentFiles: hasLimitedResources ? 3 : 10,
            enableCaching: true,
            imageQuality: hasLimitedResources ? 'low' : 'high',
            enableLazyLoading: hasLimitedResources || this.deviceCapabilities.isMobile,
        };
    }

    /**
     * Apply mobile-specific CSS optimizations
     */
    public applyMobileOptimizations(): void {
        if (!this.deviceCapabilities.isMobile) return;

        const style = document.createElement('style');
        style.textContent = `
            /* Mobile-specific optimizations */
            * {
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }
            
            input, textarea, [contenteditable] {
                -webkit-user-select: text;
                user-select: text;
            }
            
            /* Improve touch targets */
            button, [role="button"], a {
                min-height: 44px;
                min-width: 44px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
            
            /* Optimize animations for mobile */
            ${this.deviceCapabilities.reducedMotion ? `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            ` : ''}
            
            /* High contrast mode support */
            ${this.deviceCapabilities.highContrast ? `
                * {
                    border-color: ButtonText !important;
                    color: ButtonText !important;
                }
                
                button, [role="button"] {
                    background: ButtonFace !important;
                    border: 2px solid ButtonText !important;
                }
            ` : ''}
        `;
        
        document.head.appendChild(style);
    }

    /**
     * Cleanup resources
     */
    public cleanup(): void {
        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
        }
    }
}

// Create singleton instance
export const mobileOptimization = new MobileOptimizationManager();

// Export utility functions
export const isMobileDevice = () => mobileOptimization.getDeviceCapabilities().isMobile;
export const hasTouch = () => mobileOptimization.getDeviceCapabilities().hasTouch;
export const getScreenSize = () => mobileOptimization.getDeviceCapabilities().screenSize;
export const hasLimitedResources = () => mobileOptimization.hasLimitedResources();
export const getOptimalConfig = () => mobileOptimization.getOptimalConfiguration();
