/// Pattern Templates for Custom Pattern Creation
/// 
/// This module provides pre-built pattern templates to help users create
/// custom cryptocurrency detection patterns. Templates include common
/// patterns for various cryptocurrency types, exchange formats, and
/// security-related data.
/// 
/// ## Template Categories
/// 
/// ### Cryptocurrency Templates
/// - Bitcoin variants and forks
/// - Ethereum and ERC-20 tokens
/// - Alternative cryptocurrencies
/// - DeFi protocol addresses
/// 
/// ### Exchange Templates
/// - API key formats for major exchanges
/// - Trading bot credentials
/// - Wallet service identifiers
/// 
/// ### Security Templates
/// - Private key formats
/// - Seed phrase patterns
/// - Hardware wallet identifiers

use serde::{Serialize, Deserialize};
use super::custom_patterns::{CustomPattern, CustomPatternType, CustomSeverity, PatternTestCase, PatternValidationConfig, PatternPerformanceStats};

/// Pattern template definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatternTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub category: String,
    pub pattern_type: CustomPatternType,
    pub severity: CustomSeverity,
    pub regex_pattern: String,
    pub example_matches: Vec<String>,
    pub example_non_matches: Vec<String>,
    pub usage_notes: String,
    pub difficulty_level: TemplateDifficulty,
}

/// Template difficulty levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TemplateDifficulty {
    Beginner,    // Simple patterns with clear examples
    Intermediate, // Moderate complexity with some regex knowledge needed
    Advanced,    // Complex patterns requiring regex expertise
}

/// Pattern template manager
pub struct PatternTemplateManager {
    templates: Vec<PatternTemplate>,
}

impl PatternTemplateManager {
    /// Create a new template manager with built-in templates
    pub fn new() -> Self {
        Self {
            templates: Self::create_builtin_templates(),
        }
    }
    
    /// Get all available templates
    pub fn get_all_templates(&self) -> &Vec<PatternTemplate> {
        &self.templates
    }
    
    /// Get templates by category
    pub fn get_templates_by_category(&self, category: &str) -> Vec<&PatternTemplate> {
        self.templates.iter()
            .filter(|t| t.category == category)
            .collect()
    }
    
    /// Get templates by difficulty
    pub fn get_templates_by_difficulty(&self, difficulty: TemplateDifficulty) -> Vec<&PatternTemplate> {
        self.templates.iter()
            .filter(|t| std::mem::discriminant(&t.difficulty_level) == std::mem::discriminant(&difficulty))
            .collect()
    }
    
    /// Get template by ID
    pub fn get_template(&self, template_id: &str) -> Option<&PatternTemplate> {
        self.templates.iter().find(|t| t.id == template_id)
    }
    
    /// Create custom pattern from template
    pub fn create_pattern_from_template(&self, template_id: &str, custom_name: Option<String>) -> Option<CustomPattern> {
        if let Some(template) = self.get_template(template_id) {
            let pattern_name = custom_name.unwrap_or_else(|| format!("Custom {}", template.name));
            
            // Create test cases from examples
            let mut test_cases = Vec::new();
            
            // Add positive test cases
            for example in &template.example_matches {
                test_cases.push(PatternTestCase {
                    text: example.clone(),
                    should_match: true,
                    description: format!("Should match: {}", example),
                });
            }
            
            // Add negative test cases
            for example in &template.example_non_matches {
                test_cases.push(PatternTestCase {
                    text: example.clone(),
                    should_match: false,
                    description: format!("Should NOT match: {}", example),
                });
            }
            
            Some(CustomPattern {
                id: String::new(), // Will be generated when added to manager
                name: pattern_name,
                regex_pattern: template.regex_pattern.clone(),
                pattern_type: template.pattern_type.clone(),
                severity: template.severity.clone(),
                description: format!("{} (Created from template: {})", template.description, template.name),
                test_cases,
                is_enabled: true,
                created_at: String::new(), // Will be set when added
                updated_at: String::new(), // Will be set when added
                performance_stats: PatternPerformanceStats {
                    total_executions: 0,
                    total_time_ms: 0,
                    average_time_ms: 0.0,
                    max_time_ms: 0,
                    matches_found: 0,
                    last_execution: None,
                },
                validation_config: PatternValidationConfig {
                    requires_validation: matches!(template.pattern_type, CustomPatternType::PrivateKey | CustomPatternType::Address),
                    confidence_threshold: match template.severity {
                        CustomSeverity::Critical => 0.9,
                        CustomSeverity::High => 0.8,
                        CustomSeverity::Medium => 0.7,
                        CustomSeverity::Low => 0.6,
                    },
                    max_execution_time_ms: 50,
                    enable_performance_monitoring: true,
                },
            })
        } else {
            None
        }
    }
    
    /// Create built-in pattern templates
    fn create_builtin_templates() -> Vec<PatternTemplate> {
        vec![
            // === CRYPTOCURRENCY ADDRESS TEMPLATES ===
            
            PatternTemplate {
                id: "bitcoin-testnet".to_string(),
                name: "Bitcoin Testnet Address".to_string(),
                description: "Bitcoin testnet addresses starting with 'm', 'n', or '2'".to_string(),
                category: "Cryptocurrency Addresses".to_string(),
                pattern_type: CustomPatternType::Address,
                severity: CustomSeverity::Medium,
                regex_pattern: r"\b[mn2][a-km-zA-HJ-NP-Z1-9]{25,34}\b".to_string(),
                example_matches: vec![
                    "mipcBbFg9gMiCh81Kj8tqqdgoZub1ZJRfn".to_string(),
                    "2MzQwSSnBHWHqSAqtTVQ6v47XtaisrJa1Vc".to_string(),
                    "n2eMqTT929pb1RDNuqEnxdaLau1rxy3efi".to_string(),
                ],
                example_non_matches: vec![
                    "**********************************".to_string(), // Mainnet
                    "******************************************".to_string(), // Bech32
                    "not_an_address".to_string(),
                ],
                usage_notes: "Use this template to detect Bitcoin testnet addresses for development and testing environments.".to_string(),
                difficulty_level: TemplateDifficulty::Beginner,
            },
            
            PatternTemplate {
                id: "ethereum-erc20-token".to_string(),
                name: "Ethereum ERC-20 Token Address".to_string(),
                description: "Standard Ethereum addresses that could be ERC-20 token contracts".to_string(),
                category: "Cryptocurrency Addresses".to_string(),
                pattern_type: CustomPatternType::Address,
                severity: CustomSeverity::High,
                regex_pattern: r"\b0x[a-fA-F0-9]{40}\b".to_string(),
                example_matches: vec![
                    "******************************************".to_string(),
                    "******************************************".to_string(), // USDT
                    "******************************************".to_string(), // DAI
                ],
                example_non_matches: vec![
                    "0x1234".to_string(), // Too short
                    "**********************************".to_string(), // Bitcoin
                    "ethereum_address".to_string(),
                ],
                usage_notes: "Detects Ethereum addresses which could be wallets or smart contracts. Note: This pattern matches all Ethereum addresses.".to_string(),
                difficulty_level: TemplateDifficulty::Beginner,
            },
            
            // === EXCHANGE API TEMPLATES ===
            
            PatternTemplate {
                id: "generic-exchange-api".to_string(),
                name: "Generic Exchange API Key".to_string(),
                description: "Generic pattern for cryptocurrency exchange API keys".to_string(),
                category: "Exchange Credentials".to_string(),
                pattern_type: CustomPatternType::ExchangeCredential,
                severity: CustomSeverity::Critical,
                regex_pattern: r"(?i)(?:api[_\s]?key|access[_\s]?key)[\s:=]+[a-zA-Z0-9+/]{20,}".to_string(),
                example_matches: vec![
                    "api_key: abc123def456ghi789jkl012mno345pqr678".to_string(),
                    "API KEY = XYZ789ABC123DEF456GHI789JKL012MNO345".to_string(),
                    "access_key:1234567890abcdef1234567890abcdef".to_string(),
                ],
                example_non_matches: vec![
                    "api_key: short".to_string(), // Too short
                    "not an api key".to_string(),
                    "api_key:".to_string(), // No value
                ],
                usage_notes: "Generic pattern for detecting API keys. Adjust the minimum length based on your specific exchange requirements.".to_string(),
                difficulty_level: TemplateDifficulty::Intermediate,
            },
            
            PatternTemplate {
                id: "bybit-api-key".to_string(),
                name: "Bybit API Key".to_string(),
                description: "Bybit cryptocurrency exchange API key pattern".to_string(),
                category: "Exchange Credentials".to_string(),
                pattern_type: CustomPatternType::ExchangeCredential,
                severity: CustomSeverity::Critical,
                regex_pattern: r"(?i)(?:bybit.*api.*key|api.*key.*bybit)[\s:=]+[a-zA-Z0-9]{20,}".to_string(),
                example_matches: vec![
                    "bybit_api_key: ABCDEFGHIJKLMNOPQRSTUVWXYZ123456".to_string(),
                    "API_KEY_BYBIT = xyz789abc123def456ghi789jkl012".to_string(),
                ],
                example_non_matches: vec![
                    "binance_api_key: ABCDEFGHIJKLMNOPQRSTUVWXYZ123456".to_string(),
                    "bybit_api_key: short".to_string(),
                ],
                usage_notes: "Specific pattern for Bybit exchange API keys. Modify the pattern if Bybit changes their key format.".to_string(),
                difficulty_level: TemplateDifficulty::Intermediate,
            },
            
            // === PRIVATE KEY TEMPLATES ===
            
            PatternTemplate {
                id: "ethereum-private-key-raw".to_string(),
                name: "Ethereum Private Key (Raw Hex)".to_string(),
                description: "Raw Ethereum private key in hexadecimal format without 0x prefix".to_string(),
                category: "Private Keys".to_string(),
                pattern_type: CustomPatternType::PrivateKey,
                severity: CustomSeverity::Critical,
                regex_pattern: r"\b[a-fA-F0-9]{64}\b".to_string(),
                example_matches: vec![
                    "4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d".to_string(),
                    "6cbed15c793ce57650b9877cf6fa156fbef513c4e6134f022a85b1ffdd59b2a1".to_string(),
                ],
                example_non_matches: vec![
                    "0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d".to_string(), // Has 0x prefix
                    "4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1".to_string(), // Too short
                    "not_a_private_key".to_string(),
                ],
                usage_notes: "Detects raw Ethereum private keys. Be very careful with this pattern as it may match other hex data.".to_string(),
                difficulty_level: TemplateDifficulty::Advanced,
            },
            
            // === DEFI TEMPLATES ===
            
            PatternTemplate {
                id: "uniswap-pool-address".to_string(),
                name: "Uniswap Pool Address".to_string(),
                description: "Uniswap liquidity pool addresses (Ethereum format)".to_string(),
                category: "DeFi Protocols".to_string(),
                pattern_type: CustomPatternType::Address,
                severity: CustomSeverity::Medium,
                regex_pattern: r"(?i)(?:uniswap.*pool|pool.*uniswap).*0x[a-fA-F0-9]{40}".to_string(),
                example_matches: vec![
                    "Uniswap pool: ******************************************".to_string(),
                    "Pool address uniswap ******************************************".to_string(),
                ],
                example_non_matches: vec![
                    "******************************************".to_string(), // No context
                    "Sushiswap pool: ******************************************".to_string(), // Different protocol
                ],
                usage_notes: "Detects Uniswap pool addresses with context. Useful for DeFi transaction analysis.".to_string(),
                difficulty_level: TemplateDifficulty::Intermediate,
            },
            
            // === WALLET SEED TEMPLATES ===
            
            PatternTemplate {
                id: "custom-seed-phrase".to_string(),
                name: "Custom Seed Phrase Pattern".to_string(),
                description: "Custom pattern for detecting seed phrases with specific word counts".to_string(),
                category: "Seed Phrases".to_string(),
                pattern_type: CustomPatternType::PrivateKey,
                severity: CustomSeverity::Critical,
                regex_pattern: r"(?i)(?:seed|mnemonic|recovery).*(?:\b\w+\b\s*){11}\b\w+\b".to_string(),
                example_matches: vec![
                    "seed phrase: abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about".to_string(),
                    "Mnemonic: word1 word2 word3 word4 word5 word6 word7 word8 word9 word10 word11 word12".to_string(),
                ],
                example_non_matches: vec![
                    "seed phrase: abandon abandon abandon".to_string(), // Too few words
                    "not a seed phrase".to_string(),
                ],
                usage_notes: "Detects 12-word seed phrases with context. Modify the word count pattern for different lengths.".to_string(),
                difficulty_level: TemplateDifficulty::Advanced,
            },
        ]
    }
    
    /// Get template categories
    pub fn get_categories(&self) -> Vec<String> {
        let mut categories: Vec<String> = self.templates.iter()
            .map(|t| t.category.clone())
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();
        categories.sort();
        categories
    }
    
    /// Search templates by keyword
    pub fn search_templates(&self, keyword: &str) -> Vec<&PatternTemplate> {
        let keyword_lower = keyword.to_lowercase();
        self.templates.iter()
            .filter(|t| {
                t.name.to_lowercase().contains(&keyword_lower) ||
                t.description.to_lowercase().contains(&keyword_lower) ||
                t.category.to_lowercase().contains(&keyword_lower)
            })
            .collect()
    }
}

impl Default for PatternTemplateManager {
    fn default() -> Self {
        Self::new()
    }
}
