use tauri_appprivacy_ai_lib::security::{SensitiveDataDetector, SensitiveDataType, DetectionResult, SeverityLevel};
use tempfile::{TempDir, NamedTempFile};
use std::fs;
use std::io::Write;

#[tokio::test]
async fn test_sensitive_data_detector_creation() {
    let detector = SensitiveDataDetector::new();
    assert!(detector.is_ok());
}

#[tokio::test]
async fn test_sensitive_data_detector_email_detection() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Please contact <NAME_EMAIL> for assistance.";
    let results = detector.scan_text(test_content);
    
    let email_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::Email)
        .collect();
    
    assert!(!email_results.is_empty());
    assert!(email_results[0].confidence > 0.5);
}

#[tokio::test]
async fn test_sensitive_data_detector_multiple_emails() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Contact john@example.<NAME_EMAIL> for more info.";
    let results = detector.scan_text(test_content);
    
    let email_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::Email)
        .collect();
    
    assert_eq!(email_results.len(), 2);
}

#[tokio::test]
async fn test_sensitive_data_detector_credit_card() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Payment info: 4111 1111 1111 1111 (Visa)";
    let results = detector.scan_text(test_content);
    
    let cc_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::CreditCard)
        .collect();
    
    assert!(!cc_results.is_empty());
    assert!(cc_results[0].confidence > 0.7);
}

#[tokio::test]
async fn test_sensitive_data_detector_ssn() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Social Security Number: ***********";
    let results = detector.scan_text(test_content);
    
    let ssn_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::SocialSecurityNumber)
        .collect();
    
    assert!(!ssn_results.is_empty());
    assert_eq!(ssn_results[0].severity, SeverityLevel::High);
}

#[tokio::test]
async fn test_sensitive_data_detector_phone_numbers() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Call us at (************* or ************";
    let results = detector.scan_text(test_content);
    
    let phone_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::PhoneNumber)
        .collect();
    
    assert_eq!(phone_results.len(), 2);
}

#[tokio::test]
async fn test_sensitive_data_detector_crypto_addresses() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Bitcoin address: **********************************";
    let results = detector.scan_text(test_content);
    
    let crypto_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::CryptocurrencyAddress)
        .collect();
    
    assert!(!crypto_results.is_empty());
    assert_eq!(crypto_results[0].severity, SeverityLevel::High);
}

#[tokio::test]
async fn test_sensitive_data_detector_api_keys() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "API_KEY=sk-1234567890abcdef1234567890abcdef";
    let results = detector.scan_text(test_content);
    
    let api_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::ApiKey)
        .collect();
    
    assert!(!api_results.is_empty());
    assert_eq!(api_results[0].severity, SeverityLevel::Critical);
}

#[tokio::test]
async fn test_sensitive_data_detector_passwords() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "password=MySecretPassword123!";
    let results = detector.scan_text(test_content);
    
    let password_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::Password)
        .collect();
    
    assert!(!password_results.is_empty());
    assert_eq!(password_results[0].severity, SeverityLevel::Critical);
}

#[tokio::test]
async fn test_sensitive_data_detector_file_scanning() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Employee Data:").unwrap();
    writeln!(temp_file, "Email: <EMAIL>").unwrap();
    writeln!(temp_file, "SSN: ***********").unwrap();
    writeln!(temp_file, "Phone: (*************").unwrap();
    
    let results = detector.scan_file(temp_file.path()).unwrap();
    
    assert!(results.len() >= 3); // Should find email, SSN, and phone
    
    // Check that line numbers are tracked
    for result in &results {
        assert!(result.line_number.is_some());
        assert!(result.line_number.unwrap() > 0);
    }
}

#[tokio::test]
async fn test_sensitive_data_detector_confidence_scoring() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    // High confidence case
    let high_confidence_text = "Email: <EMAIL>";
    let high_results = detector.scan_text(high_confidence_text);
    
    // Lower confidence case
    let low_confidence_text = "Contact: john@invalid";
    let low_results = detector.scan_text(low_confidence_text);
    
    if !high_results.is_empty() && !low_results.is_empty() {
        let high_email = high_results.iter().find(|r| r.data_type == SensitiveDataType::Email);
        let low_email = low_results.iter().find(|r| r.data_type == SensitiveDataType::Email);
        
        if let (Some(high), Some(low)) = (high_email, low_email) {
            assert!(high.confidence > low.confidence);
        }
    }
}

#[tokio::test]
async fn test_sensitive_data_detector_context_extraction() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Employee John Smith's <NAME_EMAIL> and phone is (*************";
    let results = detector.scan_text(test_content);
    
    for result in &results {
        assert!(result.context.is_some());
        let context = result.context.as_ref().unwrap();
        assert!(!context.is_empty());
        assert!(context.len() <= 100); // Context should be reasonably sized
    }
}

#[tokio::test]
async fn test_sensitive_data_detector_severity_levels() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = r#"
        Email: <EMAIL>
        SSN: ***********
        API Key: sk-1234567890abcdef
        Password: secret123
    "#;
    
    let results = detector.scan_text(test_content);
    
    // Check severity levels are assigned correctly
    for result in &results {
        match result.data_type {
            SensitiveDataType::Email => {
                assert!(matches!(result.severity, SeverityLevel::Low | SeverityLevel::Medium));
            }
            SensitiveDataType::SocialSecurityNumber => {
                assert!(matches!(result.severity, SeverityLevel::High | SeverityLevel::Critical));
            }
            SensitiveDataType::ApiKey | SensitiveDataType::Password => {
                assert_eq!(result.severity, SeverityLevel::Critical);
            }
            _ => {} // Other types can vary
        }
    }
}

#[tokio::test]
async fn test_sensitive_data_detector_false_positive_reduction() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    // Text that might trigger false positives
    let test_content = r#"
        Today's date is 12/34/5678 (not a real date)
        Version number: *******
        IP address: ***********
        Random numbers: 123 456 7890
    "#;
    
    let results = detector.scan_text(test_content);
    
    // Should not detect these as SSNs or credit cards
    let ssn_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::SocialSecurityNumber)
        .collect();
    
    let cc_results: Vec<_> = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::CreditCard)
        .collect();
    
    // These should be minimal or have low confidence
    for result in ssn_results {
        assert!(result.confidence < 0.7);
    }
    
    for result in cc_results {
        assert!(result.confidence < 0.7);
    }
}

#[tokio::test]
async fn test_sensitive_data_detector_multiline_content() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = r#"
Line 1: Header information
Line 2: Contact <NAME_EMAIL>
Line 3: Emergency phone: (*************
Line 4: SSN for verification: ***********
Line 5: End of document
    "#;
    
    let results = detector.scan_text(test_content);
    
    // Verify line numbers are correct
    let email_result = results.iter().find(|r| r.data_type == SensitiveDataType::Email);
    if let Some(email) = email_result {
        assert_eq!(email.line_number, Some(2));
    }
    
    let phone_result = results.iter().find(|r| r.data_type == SensitiveDataType::PhoneNumber);
    if let Some(phone) = phone_result {
        assert_eq!(phone.line_number, Some(3));
    }
    
    let ssn_result = results.iter().find(|r| r.data_type == SensitiveDataType::SocialSecurityNumber);
    if let Some(ssn) = ssn_result {
        assert_eq!(ssn.line_number, Some(4));
    }
}

#[tokio::test]
async fn test_sensitive_data_detector_empty_content() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let results = detector.scan_text("");
    assert_eq!(results.len(), 0);
    
    let results = detector.scan_text("   \n\t  \n  ");
    assert_eq!(results.len(), 0);
}

#[tokio::test]
async fn test_sensitive_data_detector_large_content() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    // Create large content with scattered sensitive data
    let mut large_content = String::new();
    for i in 0..1000 {
        if i % 100 == 0 {
            large_content.push_str(&format!("Line {}: Email contact-{}@company.com\n", i, i));
        } else {
            large_content.push_str(&format!("Line {}: Regular content here\n", i));
        }
    }
    
    let results = detector.scan_text(&large_content);
    
    // Should find approximately 10 emails
    let email_count = results.iter()
        .filter(|r| r.data_type == SensitiveDataType::Email)
        .count();
    
    assert!(email_count >= 8 && email_count <= 12); // Allow some variance
}

#[tokio::test]
async fn test_sensitive_data_detector_performance() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    let test_content = "Email: <EMAIL>, Phone: (*************";
    
    let start_time = std::time::Instant::now();
    let results = detector.scan_text(test_content);
    let duration = start_time.elapsed();
    
    // Should complete quickly
    assert!(duration.as_millis() < 1000);
    assert!(!results.is_empty());
}

#[tokio::test]
async fn test_sensitive_data_detector_file_error_handling() {
    let detector = SensitiveDataDetector::new().unwrap();
    
    // Test with non-existent file
    let result = detector.scan_file("nonexistent_file.txt");
    assert!(result.is_err());
    
    // Test with directory instead of file
    let temp_dir = TempDir::new().unwrap();
    let result = detector.scan_file(temp_dir.path());
    assert!(result.is_err());
}
