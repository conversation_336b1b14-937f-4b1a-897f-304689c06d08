import React, { useState } from 'react';
import { Settings, Shield, Zap, Save } from 'lucide-react';

interface BasicConfig {
    performance: {
        mode: 'fast' | 'balanced' | 'thorough';
        max_concurrent_files: number;
        cache_enabled: boolean;
        memory_limit_mb: number;
    };
    security: {
        overwrite_passes: number;
        encryption_algorithm: 'AES256GCM' | 'ChaCha20Poly1305';
        verify_deletion: boolean;
        secure_temp_cleanup: boolean;
    };
    privacy_detection: {
        enabled: boolean;
        ssn_detection: boolean;
        credit_card_detection: boolean;
        email_detection: boolean;
        phone_detection: boolean;
        confidence_threshold: number;
    };
}

interface BasicConfigurationPanelProps {
    onConfigChange?: (config: BasicConfig) => void;
}

const BasicConfigurationPanel: React.FC<BasicConfigurationPanelProps> = ({
    onConfigChange
}) => {
    const [activeTab, setActiveTab] = useState<'performance' | 'security' | 'privacy'>('performance');
    const [config, setConfig] = useState<BasicConfig>({
        performance: {
            mode: 'balanced',
            max_concurrent_files: 10,
            cache_enabled: true,
            memory_limit_mb: 1024,
        },
        security: {
            overwrite_passes: 7,
            encryption_algorithm: 'AES256GCM',
            verify_deletion: true,
            secure_temp_cleanup: true,
        },
        privacy_detection: {
            enabled: true,
            ssn_detection: true,
            credit_card_detection: true,
            email_detection: true,
            phone_detection: true,
            confidence_threshold: 0.8,
        },
    });

    const updateConfig = (section: keyof BasicConfig, updates: any) => {
        const newConfig = {
            ...config,
            [section]: { ...config[section], ...updates }
        };
        setConfig(newConfig);
        onConfigChange?.(newConfig);
    };

    const saveConfig = () => {
        // In a real implementation, this would save to backend
        console.log('Saving configuration:', config);
        alert('Configuration saved successfully!');
    };

    const resetToDefaults = () => {
        const defaultConfig: BasicConfig = {
            performance: {
                mode: 'balanced',
                max_concurrent_files: 10,
                cache_enabled: true,
                memory_limit_mb: 1024,
            },
            security: {
                overwrite_passes: 7,
                encryption_algorithm: 'AES256GCM',
                verify_deletion: true,
                secure_temp_cleanup: true,
            },
            privacy_detection: {
                enabled: true,
                ssn_detection: true,
                credit_card_detection: true,
                email_detection: true,
                phone_detection: true,
                confidence_threshold: 0.8,
            },
        };
        setConfig(defaultConfig);
        onConfigChange?.(defaultConfig);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
            <div style={{ marginBottom: '30px' }}>
                <h2 style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
                    <Settings size={24} />
                    Configuration Settings
                </h2>
                <p style={{ color: '#666', margin: 0 }}>
                    Configure performance, security, and privacy detection settings
                </p>
            </div>

            {/* Tab Navigation */}
            <div style={{ 
                display: 'flex', 
                borderBottom: '2px solid #e0e0e0', 
                marginBottom: '30px',
                gap: '20px'
            }}>
                <button
                    onClick={() => setActiveTab('performance')}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '12px 16px',
                        border: 'none',
                        background: 'none',
                        borderBottom: activeTab === 'performance' ? '2px solid #007bff' : '2px solid transparent',
                        color: activeTab === 'performance' ? '#007bff' : '#666',
                        cursor: 'pointer',
                        fontWeight: activeTab === 'performance' ? '600' : '400',
                    }}
                >
                    <Zap size={18} />
                    Performance
                </button>
                <button
                    onClick={() => setActiveTab('security')}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '12px 16px',
                        border: 'none',
                        background: 'none',
                        borderBottom: activeTab === 'security' ? '2px solid #007bff' : '2px solid transparent',
                        color: activeTab === 'security' ? '#007bff' : '#666',
                        cursor: 'pointer',
                        fontWeight: activeTab === 'security' ? '600' : '400',
                    }}
                >
                    <Shield size={18} />
                    Security
                </button>
                <button
                    onClick={() => setActiveTab('privacy')}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '12px 16px',
                        border: 'none',
                        background: 'none',
                        borderBottom: activeTab === 'privacy' ? '2px solid #007bff' : '2px solid transparent',
                        color: activeTab === 'privacy' ? '#007bff' : '#666',
                        cursor: 'pointer',
                        fontWeight: activeTab === 'privacy' ? '600' : '400',
                    }}
                >
                    <Settings size={18} />
                    Privacy Detection
                </button>
            </div>

            {/* Performance Settings */}
            {activeTab === 'performance' && (
                <div>
                    <h3 style={{ marginBottom: '20px', color: '#2c3e50', fontWeight: '600' }}>Performance Settings</h3>
                    
                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' }}>
                            Performance Mode
                        </label>
                        <select
                            value={config.performance.mode}
                            onChange={(e) => updateConfig('performance', { mode: e.target.value })}
                            style={{
                                width: '100%',
                                padding: '10px',
                                border: '1px solid #ddd',
                                borderRadius: '4px',
                                fontSize: '14px'
                            }}
                        >
                            <option value="fast">Fast - Quick processing, lower accuracy</option>
                            <option value="balanced">Balanced - Good speed and accuracy</option>
                            <option value="thorough">Thorough - Maximum accuracy, slower processing</option>
                        </select>
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' }}>
                            Max Concurrent Files: {config.performance.max_concurrent_files}
                        </label>
                        <input
                            type="range"
                            min="1"
                            max="20"
                            value={config.performance.max_concurrent_files}
                            onChange={(e) => updateConfig('performance', { max_concurrent_files: parseInt(e.target.value) })}
                            style={{ width: '100%' }}
                        />
                        <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
                            <span>1 (Slow)</span>
                            <span>10 (Balanced)</span>
                            <span>20 (Fast)</span>
                        </div>
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500', color: '#2c3e50' }}>
                            Memory Limit: {config.performance.memory_limit_mb} MB
                        </label>
                        <input
                            type="range"
                            min="256"
                            max="4096"
                            step="256"
                            value={config.performance.memory_limit_mb}
                            onChange={(e) => updateConfig('performance', { memory_limit_mb: parseInt(e.target.value) })}
                            style={{ width: '100%' }}
                        />
                        <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
                            <span>256 MB</span>
                            <span>2048 MB</span>
                            <span>4096 MB</span>
                        </div>
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                            <input
                                type="checkbox"
                                checked={config.performance.cache_enabled}
                                onChange={(e) => updateConfig('performance', { cache_enabled: e.target.checked })}
                            />
                            <span style={{ color: '#2c3e50', fontWeight: '500' }}>Enable Intelligent Caching</span>
                        </label>
                        <p style={{ fontSize: '12px', color: '#666', margin: '4px 0 0 24px' }}>
                            Improves performance by caching frequently accessed data
                        </p>
                    </div>
                </div>
            )}

            {/* Security Settings */}
            {activeTab === 'security' && (
                <div>
                    <h3 style={{ marginBottom: '20px', color: '#2c3e50', fontWeight: '600' }}>Security Settings</h3>
                    
                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                            Secure Deletion Passes: {config.security.overwrite_passes} (DoD Standard: 7)
                        </label>
                        <input
                            type="range"
                            min="1"
                            max="35"
                            value={config.security.overwrite_passes}
                            onChange={(e) => updateConfig('security', { overwrite_passes: parseInt(e.target.value) })}
                            style={{ width: '100%' }}
                        />
                        <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
                            <span>1 (Fast)</span>
                            <span>7 (DoD Standard)</span>
                            <span>35 (Maximum Security)</span>
                        </div>
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                            Encryption Algorithm
                        </label>
                        <select
                            value={config.security.encryption_algorithm}
                            onChange={(e) => updateConfig('security', { encryption_algorithm: e.target.value })}
                            style={{
                                width: '100%',
                                padding: '10px',
                                border: '1px solid #ddd',
                                borderRadius: '4px',
                                fontSize: '14px'
                            }}
                        >
                            <option value="AES256GCM">AES-256-GCM (Recommended)</option>
                            <option value="ChaCha20Poly1305">ChaCha20-Poly1305 (Alternative)</option>
                        </select>
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                            <input
                                type="checkbox"
                                checked={config.security.verify_deletion}
                                onChange={(e) => updateConfig('security', { verify_deletion: e.target.checked })}
                            />
                            <span>Verify Secure Deletion</span>
                        </label>
                        <p style={{ fontSize: '12px', color: '#666', margin: '4px 0 0 24px' }}>
                            Confirms that files are completely overwritten
                        </p>
                    </div>

                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                            <input
                                type="checkbox"
                                checked={config.security.secure_temp_cleanup}
                                onChange={(e) => updateConfig('security', { secure_temp_cleanup: e.target.checked })}
                            />
                            <span>Secure Temporary File Cleanup</span>
                        </label>
                        <p style={{ fontSize: '12px', color: '#666', margin: '4px 0 0 24px' }}>
                            Securely deletes temporary files created during operations
                        </p>
                    </div>
                </div>
            )}

            {/* Privacy Detection Settings */}
            {activeTab === 'privacy' && (
                <div>
                    <h3 style={{ marginBottom: '20px', color: '#2c3e50', fontWeight: '600' }}>Privacy Detection Settings</h3>
                    
                    <div style={{ marginBottom: '25px' }}>
                        <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                            <input
                                type="checkbox"
                                checked={config.privacy_detection.enabled}
                                onChange={(e) => updateConfig('privacy_detection', { enabled: e.target.checked })}
                            />
                            <span style={{ fontWeight: '500' }}>Enable Privacy Detection</span>
                        </label>
                    </div>

                    {config.privacy_detection.enabled && (
                        <>
                            <div style={{ marginBottom: '20px', paddingLeft: '20px' }}>
                                <h4 style={{ marginBottom: '15px', color: '#2c3e50', fontWeight: '600' }}>Detection Types</h4>
                                
                                <div style={{ marginBottom: '10px' }}>
                                    <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                                        <input
                                            type="checkbox"
                                            checked={config.privacy_detection.ssn_detection}
                                            onChange={(e) => updateConfig('privacy_detection', { ssn_detection: e.target.checked })}
                                        />
                                        <span style={{ color: '#2c3e50', fontWeight: '500' }}>Social Security Numbers</span>
                                    </label>
                                </div>

                                <div style={{ marginBottom: '10px' }}>
                                    <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                                        <input
                                            type="checkbox"
                                            checked={config.privacy_detection.credit_card_detection}
                                            onChange={(e) => updateConfig('privacy_detection', { credit_card_detection: e.target.checked })}
                                        />
                                        <span style={{ color: '#2c3e50', fontWeight: '500' }}>Credit Card Numbers</span>
                                    </label>
                                </div>

                                <div style={{ marginBottom: '10px' }}>
                                    <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                                        <input
                                            type="checkbox"
                                            checked={config.privacy_detection.email_detection}
                                            onChange={(e) => updateConfig('privacy_detection', { email_detection: e.target.checked })}
                                        />
                                        <span style={{ color: '#2c3e50', fontWeight: '500' }}>Email Addresses</span>
                                    </label>
                                </div>

                                <div style={{ marginBottom: '10px' }}>
                                    <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                                        <input
                                            type="checkbox"
                                            checked={config.privacy_detection.phone_detection}
                                            onChange={(e) => updateConfig('privacy_detection', { phone_detection: e.target.checked })}
                                        />
                                        <span style={{ color: '#2c3e50', fontWeight: '500' }}>Phone Numbers</span>
                                    </label>
                                </div>
                            </div>

                            <div style={{ marginBottom: '25px', paddingLeft: '20px' }}>
                                <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                                    Confidence Threshold: {Math.round(config.privacy_detection.confidence_threshold * 100)}%
                                </label>
                                <input
                                    type="range"
                                    min="0.5"
                                    max="1.0"
                                    step="0.05"
                                    value={config.privacy_detection.confidence_threshold}
                                    onChange={(e) => updateConfig('privacy_detection', { confidence_threshold: parseFloat(e.target.value) })}
                                    style={{ width: '100%' }}
                                />
                                <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
                                    <span>50% (More Results)</span>
                                    <span>80% (Balanced)</span>
                                    <span>100% (High Precision)</span>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            )}

            {/* Action Buttons */}
            <div style={{ 
                display: 'flex', 
                gap: '12px', 
                marginTop: '40px', 
                paddingTop: '20px', 
                borderTop: '1px solid #e0e0e0' 
            }}>
                <button
                    onClick={saveConfig}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '12px 24px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        fontWeight: '500',
                        fontSize: '14px'
                    }}
                >
                    <Save size={16} />
                    Save Configuration
                </button>
                <button
                    onClick={resetToDefaults}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '12px 24px',
                        backgroundColor: '#6c757d',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        fontWeight: '500',
                        fontSize: '14px'
                    }}
                >
                    <Settings size={16} />
                    Reset to Defaults
                </button>
            </div>
        </div>
    );
};

export default BasicConfigurationPanel;
