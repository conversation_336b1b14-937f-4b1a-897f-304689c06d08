// PrivacyAI Scanner Theme Configuration
// Centralized theme system for consistent UI/UX

export interface ThemeColors {
  // Primary colors
  primary: {
    50: string;
    100: string;
    500: string;
    600: string;
    700: string;
  };
  
  // Gray scale
  gray: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  
  // Status colors
  success: {
    50: string;
    500: string;
    600: string;
    700: string;
  };
  
  warning: {
    50: string;
    500: string;
    600: string;
  };
  
  error: {
    50: string;
    500: string;
    600: string;
    700: string;
  };
  
  // Background and text
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
  };
}

export const lightTheme: ThemeColors = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
  },
  
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  
  success: {
    50: '#f0fdf4',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
  },
  
  warning: {
    50: '#fefce8',
    500: '#eab308',
    600: '#ca8a04',
  },
  
  error: {
    50: '#fef2f2',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
  },
  
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6',
  },
  
  text: {
    primary: '#111827',
    secondary: '#374151',
    tertiary: '#6b7280',
    inverse: '#ffffff',
  },
};

export const darkTheme: ThemeColors = {
  primary: {
    50: '#1e3a8a',
    100: '#1e40af',
    500: '#3b82f6',
    600: '#60a5fa',
    700: '#93c5fd',
  },
  
  gray: {
    50: '#1f2937',
    100: '#374151',
    200: '#4b5563',
    300: '#6b7280',
    400: '#9ca3af',
    500: '#d1d5db',
    600: '#e5e7eb',
    700: '#f3f4f6',
    800: '#f9fafb',
    900: '#ffffff',
  },
  
  success: {
    50: '#064e3b',
    500: '#10b981',
    600: '#34d399',
    700: '#6ee7b7',
  },
  
  warning: {
    50: '#451a03',
    500: '#f59e0b',
    600: '#fbbf24',
  },
  
  error: {
    50: '#7f1d1d',
    500: '#ef4444',
    600: '#f87171',
    700: '#fca5a5',
  },
  
  background: {
    primary: '#111827',
    secondary: '#1f2937',
    tertiary: '#374151',
  },
  
  text: {
    primary: '#f9fafb',
    secondary: '#e5e7eb',
    tertiary: '#9ca3af',
    inverse: '#111827',
  },
};

export type ThemeMode = 'light' | 'dark';

export interface Theme {
  mode: ThemeMode;
  colors: ThemeColors;
}

export const getTheme = (mode: ThemeMode): Theme => ({
  mode,
  colors: mode === 'light' ? lightTheme : darkTheme,
});

// CSS custom properties for theme
export const generateThemeCSS = (theme: Theme): string => {
  const { colors } = theme;
  
  return `
    :root {
      /* Primary colors */
      --color-primary-50: ${colors.primary[50]};
      --color-primary-100: ${colors.primary[100]};
      --color-primary-500: ${colors.primary[500]};
      --color-primary-600: ${colors.primary[600]};
      --color-primary-700: ${colors.primary[700]};
      
      /* Gray scale */
      --color-gray-50: ${colors.gray[50]};
      --color-gray-100: ${colors.gray[100]};
      --color-gray-200: ${colors.gray[200]};
      --color-gray-300: ${colors.gray[300]};
      --color-gray-400: ${colors.gray[400]};
      --color-gray-500: ${colors.gray[500]};
      --color-gray-600: ${colors.gray[600]};
      --color-gray-700: ${colors.gray[700]};
      --color-gray-800: ${colors.gray[800]};
      --color-gray-900: ${colors.gray[900]};
      
      /* Status colors */
      --color-success-50: ${colors.success[50]};
      --color-success-500: ${colors.success[500]};
      --color-success-600: ${colors.success[600]};
      --color-success-700: ${colors.success[700]};
      
      --color-warning-50: ${colors.warning[50]};
      --color-warning-500: ${colors.warning[500]};
      --color-warning-600: ${colors.warning[600]};
      
      --color-error-50: ${colors.error[50]};
      --color-error-500: ${colors.error[500]};
      --color-error-600: ${colors.error[600]};
      --color-error-700: ${colors.error[700]};
      
      /* Background and text */
      --color-bg-primary: ${colors.background.primary};
      --color-bg-secondary: ${colors.background.secondary};
      --color-bg-tertiary: ${colors.background.tertiary};
      
      --color-text-primary: ${colors.text.primary};
      --color-text-secondary: ${colors.text.secondary};
      --color-text-tertiary: ${colors.text.tertiary};
      --color-text-inverse: ${colors.text.inverse};
    }
  `;
};

// Utility functions for consistent styling
export const getButtonStyles = (variant: 'primary' | 'secondary' | 'danger' = 'primary') => {
  const baseStyles = {
    padding: '10px 20px',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontWeight: '500',
    transition: 'all 0.2s ease',
  };

  const variants = {
    primary: {
      ...baseStyles,
      background: 'var(--color-primary-600)',
      color: 'var(--color-text-inverse)',
    },
    secondary: {
      ...baseStyles,
      background: 'var(--color-gray-500)',
      color: 'var(--color-text-inverse)',
    },
    danger: {
      ...baseStyles,
      background: 'var(--color-error-600)',
      color: 'var(--color-text-inverse)',
    },
  };

  return variants[variant];
};

export const getTextStyles = (variant: 'primary' | 'secondary' | 'tertiary' = 'primary') => {
  const variants = {
    primary: {
      color: 'var(--color-text-primary)',
    },
    secondary: {
      color: 'var(--color-text-secondary)',
    },
    tertiary: {
      color: 'var(--color-text-tertiary)',
    },
  };

  return variants[variant];
};
