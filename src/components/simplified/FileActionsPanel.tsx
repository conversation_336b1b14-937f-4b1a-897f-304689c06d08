import { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { 
  Copy, 
  Move, 
  Trash2, 
  ExternalLink, 
  Info, 
  GitCompare, 
  MoreHorizontal,
  FolderOpen,
  AlertTriangle,
  Download
} from 'lucide-react';
import * as Dialog from '@radix-ui/react-dialog';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';

interface FileActionsPanelProps {
  selectedFiles: string[];
  onAction: (action: string, files: string[]) => void;
  disabled?: boolean;
}

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText: string;
  isDestructive?: boolean;
}

function ConfirmDialog({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title, 
  description, 
  confirmText, 
  isDestructive = false 
}: ConfirmDialogProps) {
  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="dialog-overlay" />
        <Dialog.Content className="dialog-content">
          <Dialog.Title className="dialog-title">{title}</Dialog.Title>
          <Dialog.Description className="dialog-description">
            {description}
          </Dialog.Description>
          
          <div className="dialog-actions">
            <button className="dialog-button secondary" onClick={onClose}>
              Cancel
            </button>
            <button 
              className={`dialog-button ${isDestructive ? 'destructive' : 'primary'}`}
              onClick={onConfirm}
            >
              {confirmText}
            </button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}

export default function FileActionsPanel({ 
  selectedFiles, 
  onAction, 
  disabled = false 
}: FileActionsPanelProps) {
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean;
    action: string;
    title: string;
    description: string;
    confirmText: string;
    isDestructive: boolean;
  }>({
    isOpen: false,
    action: '',
    title: '',
    description: '',
    confirmText: '',
    isDestructive: false,
  });

  const hasSelection = selectedFiles.length > 0;
  // const isMultipleSelection = selectedFiles.length > 1;

  const handleAction = async (action: string) => {
    if (!hasSelection) return;

    switch (action) {
      case 'copy':
        await handleCopy();
        break;
      case 'move':
        await handleMove();
        break;
      case 'delete':
        showConfirmDialog(
          'delete',
          'Confirm Secure Delete',
          `Are you sure you want to securely delete ${selectedFiles.length} file(s)? This action cannot be undone.`,
          'Delete Files',
          true
        );
        break;
      case 'open':
        await handleOpen();
        break;
      case 'details':
        await handleDetails();
        break;
      case 'compare':
        await handleCompare();
        break;
      case 'open-with':
        await handleOpenWith();
        break;
      case 'save-log':
        await handleSaveLog();
        break;
      default:
        onAction(action, selectedFiles);
    }
  };

  const showConfirmDialog = (
    action: string,
    title: string,
    description: string,
    confirmText: string,
    isDestructive: boolean = false
  ) => {
    setConfirmDialog({
      isOpen: true,
      action,
      title,
      description,
      confirmText,
      isDestructive,
    });
  };

  const handleConfirm = () => {
    onAction(confirmDialog.action, selectedFiles);
    setConfirmDialog(prev => ({ ...prev, isOpen: false }));
  };

  const handleCopy = async () => {
    try {
      const destination = await invoke<string | null>('select_directory');
      if (destination) {
        onAction('copy', selectedFiles);
        // TODO: Implement actual copy operation
        console.log(`Copying ${selectedFiles.length} files to ${destination}`);
      }
    } catch (err) {
      console.error('Copy operation failed:', err);
    }
  };

  const handleMove = async () => {
    try {
      const destination = await invoke<string | null>('select_directory');
      if (destination) {
        showConfirmDialog(
          'move',
          'Confirm Move',
          `Move ${selectedFiles.length} file(s) to the selected location?`,
          'Move Files'
        );
      }
    } catch (err) {
      console.error('Move operation failed:', err);
    }
  };

  const handleOpen = async () => {
    if (selectedFiles.length === 1) {
      try {
        // TODO: Implement file opening with default application
        console.log(`Opening file: ${selectedFiles[0]}`);
        onAction('open', selectedFiles);
      } catch (err) {
        console.error('Open operation failed:', err);
      }
    }
  };

  const handleDetails = async () => {
    // TODO: Show file details dialog
    console.log(`Showing details for ${selectedFiles.length} file(s)`);
    onAction('details', selectedFiles);
  };

  const handleCompare = async () => {
    if (selectedFiles.length === 2) {
      // TODO: Implement file comparison
      console.log(`Comparing files: ${selectedFiles[0]} and ${selectedFiles[1]}`);
      onAction('compare', selectedFiles);
    }
  };

  const handleOpenWith = async () => {
    if (selectedFiles.length === 1) {
      // TODO: Show application chooser
      console.log(`Open with chooser for: ${selectedFiles[0]}`);
      onAction('open-with', selectedFiles);
    }
  };

  const handleSaveLog = async () => {
    try {
      // TODO: Implement save log functionality
      console.log('Saving scan log...');
      onAction('save-log', selectedFiles);
    } catch (err) {
      console.error('Save log failed:', err);
    }
  };

  return (
    <>
      <div className={`file-actions-panel ${disabled ? 'disabled' : ''}`}>
        <div className="actions-header">
          <h3 className="actions-title">
            File Actions
            {hasSelection && (
              <span className="selection-badge">
                {selectedFiles.length} selected
              </span>
            )}
          </h3>
        </div>

        <div className="actions-grid">
          {/* Primary Actions */}
          <button
            className="action-button primary"
            onClick={() => handleAction('copy')}
            disabled={!hasSelection || disabled}
            title="Copy selected files to another location"
          >
            <Copy className="w-4 h-4" />
            Copy
          </button>

          <button
            className="action-button primary"
            onClick={() => handleAction('move')}
            disabled={!hasSelection || disabled}
            title="Move selected files to another location"
          >
            <Move className="w-4 h-4" />
            Move
          </button>

          <button
            className="action-button destructive"
            onClick={() => handleAction('delete')}
            disabled={!hasSelection || disabled}
            title="Securely delete selected files"
          >
            <Trash2 className="w-4 h-4" />
            Secure Delete
          </button>

          <button
            className="action-button secondary"
            onClick={() => handleAction('open')}
            disabled={selectedFiles.length !== 1 || disabled}
            title="Open file with default application"
          >
            <ExternalLink className="w-4 h-4" />
            Open
          </button>

          <button
            className="action-button secondary"
            onClick={() => handleAction('details')}
            disabled={!hasSelection || disabled}
            title="Show file details and properties"
          >
            <Info className="w-4 h-4" />
            Details
          </button>

          <button
            className="action-button secondary"
            onClick={() => handleAction('compare')}
            disabled={selectedFiles.length !== 2 || disabled}
            title="Compare two selected files"
          >
            <GitCompare className="w-4 h-4" />
            Compare
          </button>

          {/* More Actions Dropdown */}
          <DropdownMenu.Root>
            <DropdownMenu.Trigger asChild>
              <button
                className="action-button secondary"
                disabled={!hasSelection || disabled}
                title="More actions"
              >
                <MoreHorizontal className="w-4 h-4" />
                More
              </button>
            </DropdownMenu.Trigger>

            <DropdownMenu.Portal>
              <DropdownMenu.Content className="dropdown-content">
                <DropdownMenu.Item 
                  className="dropdown-item"
                  onSelect={() => handleAction('open-with')}
                  disabled={selectedFiles.length !== 1}
                >
                  <FolderOpen className="w-4 h-4" />
                  Open With...
                </DropdownMenu.Item>

                <DropdownMenu.Item 
                  className="dropdown-item"
                  onSelect={() => handleAction('save-log')}
                >
                  <Download className="w-4 h-4" />
                  Save Scan Log
                </DropdownMenu.Item>

                <DropdownMenu.Separator className="dropdown-separator" />

                <DropdownMenu.Item 
                  className="dropdown-item"
                  onSelect={() => handleAction('reveal')}
                  disabled={selectedFiles.length !== 1}
                >
                  <FolderOpen className="w-4 h-4" />
                  Reveal in Explorer
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Portal>
          </DropdownMenu.Root>
        </div>

        {!hasSelection && (
          <div className="no-selection-message">
            <AlertTriangle className="w-5 h-5 text-gray-400" />
            <p>Select files to enable actions</p>
          </div>
        )}
      </div>

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
        onConfirm={handleConfirm}
        title={confirmDialog.title}
        description={confirmDialog.description}
        confirmText={confirmDialog.confirmText}
        isDestructive={confirmDialog.isDestructive}
      />
    </>
  );
}
