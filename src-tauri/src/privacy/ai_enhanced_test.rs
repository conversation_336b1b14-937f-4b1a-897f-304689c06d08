/// AI-Enhanced Detection System Validation Tests
/// 
/// These tests validate the AI-powered enhancements to our pattern recognition system,
/// ensuring improved accuracy and context understanding.

use std::collections::HashMap;
use crate::privacy::ai_enhanced_detector::{AIEnhancedDetector, AIEnhancedConfig};
use crate::privacy::context_aware_detector::DocumentContext;
use crate::privacy::comprehensive_error_reporting::GLOBAL_ERROR_REPORTER;

#[cfg(test)]
mod ai_enhanced_tests {
    use super::*;

    /// Test AI-enhanced document type classification
    #[tokio::test]
    async fn test_ai_document_classification() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let mut detector = AIEnhancedDetector::new();
        
        let test_cases = vec![
            (
                "Employee ID: 12345, SSN: ***********, Department: Engineering, Salary: $75,000",
                "_employment",
                "Employment document with SSN should be classified correctly"
            ),
            (
                "Account Number: **********, Balance: $5,000, Transaction History: ...",
                "_financial",
                "Financial document should be classified correctly"
            ),
            (
                "Patient ID: P12345, Diagnosis: Hypertension, SSN: ***********",
                "_medical",
                "Medical document should be classified correctly"
            ),
            (
                "Thank you for calling customer service. Your reference number is ***********.",
                "_customer_service",
                "Customer service document should be classified correctly"
            ),
        ];
        
        for (text, _expected_type, description) in test_cases {
            println!("🧪 Testing AI classification: {}", description);
            
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: None, // Let AI determine this
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            let result = detector.detect_with_ai_enhancement(text, &context).await.unwrap();
            
            println!("   📊 AI predicted document type: {} (confidence: {:.2})", 
                    result.ai_analysis.predicted_document_type, 
                    result.ai_analysis.prediction_confidence);
            
            // Verify AI classification is reasonable
            assert!(result.ai_analysis.prediction_confidence > 0.0, 
                   "AI should provide some confidence in classification");
            
            // Check if AI analysis contains semantic indicators
            assert!(!result.ai_analysis.semantic_indicators.is_empty() || text.len() < 10, 
                   "AI should extract semantic indicators for substantial text");
            
            println!("   ✅ AI classification test passed");
        }
    }
    
    /// Test AI-enhanced confidence scoring
    #[tokio::test]
    async fn test_ai_confidence_enhancement() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let mut detector = AIEnhancedDetector::new();
        
        // Test case: Employment context should boost SSN confidence
        let employment_text = "Employee Information: John Doe, SSN: ***********, Department: IT";
        let employment_context = DocumentContext {
            full_text: employment_text.to_string(),
            document_type: Some("employment".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };
        
        let employment_result = detector.detect_with_ai_enhancement(employment_text, &employment_context).await.unwrap();
        
        // Test case: Customer service context should reduce SSN confidence
        let service_text = "Thank you for calling. Your reference number is ***********.";
        let service_context = DocumentContext {
            full_text: service_text.to_string(),
            document_type: Some("customer_service".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };
        
        let service_result = detector.detect_with_ai_enhancement(service_text, &service_context).await.unwrap();
        
        println!("📊 AI Enhancement Results:");
        println!("   Employment context findings: {}", employment_result.traditional_findings.len());
        println!("   Customer service context findings: {}", service_result.traditional_findings.len());
        println!("   Employment AI risk score: {:.2}", employment_result.ai_analysis.ai_risk_score);
        println!("   Customer service AI risk score: {:.2}", service_result.ai_analysis.ai_risk_score);
        
        // Verify AI enhancement is working
        assert!(employment_result.ai_analysis.prediction_confidence >= 0.0, 
               "Employment context should have valid AI confidence");
        assert!(service_result.ai_analysis.prediction_confidence >= 0.0, 
               "Customer service context should have valid AI confidence");
        
        // Check enhanced confidences
        if !employment_result.enhanced_confidences.is_empty() {
            println!("   Employment enhanced confidences: {:?}", employment_result.enhanced_confidences);
        }
        
        if !service_result.enhanced_confidences.is_empty() {
            println!("   Customer service enhanced confidences: {:?}", service_result.enhanced_confidences);
        }
        
        println!("✅ AI confidence enhancement test passed");
    }
    
    /// Test AI risk assessment
    #[tokio::test]
    async fn test_ai_risk_assessment() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let mut detector = AIEnhancedDetector::new();
        
        let test_cases = vec![
            (
                "SSN: ***********, Credit Card: 4532-1234-5678-9012, Bank Account: *********",
                "High risk: multiple sensitive data types",
                0.5 // Expected minimum risk score
            ),
            (
                "Employee ID: E12345, Department: Engineering",
                "Low risk: no sensitive data",
                0.0 // Expected maximum risk score
            ),
            (
                "Patient SSN: ***********, Medical Record: M12345, Diagnosis: Confidential",
                "High risk: medical context with SSN",
                0.4 // Expected minimum risk score
            ),
        ];
        
        for (text, description, min_expected_risk) in test_cases {
            println!("🧪 Testing AI risk assessment: {}", description);
            
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: None,
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            let result = detector.detect_with_ai_enhancement(text, &context).await.unwrap();
            
            println!("   📊 AI Risk Assessment:");
            println!("      Overall risk score: {:.2}", result.ai_risk_assessment.overall_risk_score);
            println!("      Risk level: {}", result.ai_risk_assessment.risk_level);
            println!("      Risk factors: {}", result.ai_risk_assessment.risk_factors.len());
            println!("      Recommended actions: {}", result.ai_risk_assessment.recommended_actions.len());
            
            // Verify risk assessment is reasonable
            assert!(result.ai_risk_assessment.overall_risk_score >= min_expected_risk, 
                   "Risk score {:.2} should be at least {:.2} for: {}", 
                   result.ai_risk_assessment.overall_risk_score, min_expected_risk, description);
            
            assert!(result.ai_risk_assessment.overall_risk_score <= 1.0, 
                   "Risk score should not exceed 1.0");
            
            assert!(!result.ai_risk_assessment.risk_level.is_empty(), 
                   "Risk level should be provided");
            
            assert!(!result.ai_risk_assessment.recommended_actions.is_empty(), 
                   "Recommended actions should be provided");
            
            // Print risk factors for analysis
            for (i, factor) in result.ai_risk_assessment.risk_factors.iter().enumerate() {
                println!("      Factor {}: {} (contribution: {:.2}, confidence: {:.2})", 
                        i + 1, factor.factor, factor.contribution, factor.confidence);
            }
            
            println!("   ✅ Risk assessment test passed");
        }
    }
    
    /// Test AI performance metrics
    #[tokio::test]
    async fn test_ai_performance_metrics() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let mut detector = AIEnhancedDetector::new();
        
        // Test with various text sizes
        let medium_text = "Medium text with SSN: ***********. ".repeat(10);
        let long_text = "Long text with SSN: ***********. ".repeat(100);
        let test_texts = vec![
            ("Short text with SSN: ***********", "short"),
            (medium_text.as_str(), "medium"),
            (long_text.as_str(), "long"),
        ];
        
        for (text, size_description) in test_texts {
            println!("🧪 Testing AI performance with {} text ({} chars)", size_description, text.len());
            
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: Some("test".to_string()),
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            let result = detector.detect_with_ai_enhancement(text, &context).await.unwrap();
            
            println!("   📊 Performance Metrics:");
            println!("      Total processing time: {}ms", result.processing_metadata.total_processing_time_ms);
            println!("      Traditional detection time: {}ms", result.processing_metadata.traditional_detection_time_ms);
            println!("      AI analysis time: {}ms", result.processing_metadata.ai_analysis_time_ms);
            println!("      AI enhanced findings: {}", result.processing_metadata.ai_enhanced_findings);
            
            // Verify performance is reasonable
            assert!(result.processing_metadata.total_processing_time_ms < 10000, 
                   "Total processing should complete within 10 seconds");
            
            assert!(result.processing_metadata.traditional_detection_time_ms <= result.processing_metadata.total_processing_time_ms, 
                   "Traditional detection time should not exceed total time");
            
            assert!(result.processing_metadata.ai_analysis_time_ms <= result.processing_metadata.total_processing_time_ms, 
                   "AI analysis time should not exceed total time");
            
            // Check model versions are recorded
            assert!(!result.processing_metadata.model_versions.is_empty(), 
                   "Model versions should be recorded");
            
            println!("   ✅ Performance metrics test passed");
        }
    }
    
    /// Test AI integration with comprehensive error reporting
    #[tokio::test]
    async fn test_ai_error_integration() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let mut detector = AIEnhancedDetector::new();
        
        // Test with potentially problematic inputs
        let very_long_text = "very long text ".repeat(1000);
        let problematic_inputs = vec![
            ("", "empty_text"),
            ("x", "single_character"),
            (very_long_text.as_str(), "very_long_text"),
            ("Special chars: émojis 🔒 unicode ñ", "unicode_text"),
        ];
        
        for (text, description) in problematic_inputs {
            println!("🧪 Testing AI error handling: {}", description);
            
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: Some("test".to_string()),
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            // This should not panic or fail catastrophically
            let result = detector.detect_with_ai_enhancement(text, &context).await;
            
            match result {
                Ok(detection_result) => {
                    println!("   ✅ AI detection completed successfully");
                    println!("      Traditional findings: {}", detection_result.traditional_findings.len());
                    println!("      AI risk score: {:.2}", detection_result.ai_analysis.ai_risk_score);
                },
                Err(e) => {
                    println!("   ⚠️  AI detection error (handled gracefully): {}", e);
                    // Errors should be captured in the comprehensive error reporter
                }
            }
        }
        
        // Verify comprehensive error reporting captured any issues
        if let Ok(reporter) = GLOBAL_ERROR_REPORTER.lock() {
            let report = reporter.get_comprehensive_report();
            
            println!("📊 AI Error Integration Report:");
            println!("   Total Errors: {}", report.total_errors);
            println!("   Total Warnings: {}", report.total_warnings);
            println!("   Total Debug Messages: {}", report.total_debug_messages);
            println!("   Total Performance Metrics: {}", report.total_performance_metrics);
            
            // We expect debug messages for AI processing
            assert!(report.total_debug_messages > 0, 
                   "Expected debug messages from AI processing");
            
            // We expect performance metrics for AI operations
            assert!(report.total_performance_metrics > 0, 
                   "Expected performance metrics from AI operations");
            
            println!("✅ AI error integration test passed");
        }
    }
}
