use tauri_appprivacy_ai_lib::security::{Pat<PERSON>Matcher, SensitiveDataType, DetectionResult};

#[tokio::test]
async fn test_pattern_matcher_creation() {
    let matcher = PatternMatcher::new();
    assert!(matcher.is_ok());
}

#[tokio::test]
async fn test_pattern_matcher_email_detection() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::Email);

    let test_content = "Contact <NAME_EMAIL> for more information";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 1);
    assert_eq!(results[0].data_type, SensitiveDataType::Email);
    assert!(results[0].confidence > 0.5);
    assert_eq!(results[0].line_number, Some(1));
}

#[tokio::test]
async fn test_pattern_matcher_multiple_emails() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::Email);

    let test_content = "Contact: john@example.<NAME_EMAIL>";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 2);
    assert!(results.iter().all(|r| r.data_type == SensitiveDataType::Email));
}

#[tokio::test]
async fn test_pattern_matcher_credit_card_detection() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::CreditCard);

    // Valid Visa test number
    let test_content = "Credit card: 4111 1111 1111 1111";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 1);
    assert_eq!(results[0].data_type, SensitiveDataType::CreditCard);
    assert!(results[0].confidence > 0.8); // Should be high confidence due to Luhn validation
}

#[tokio::test]
async fn test_pattern_matcher_ssn_detection() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::SocialSecurityNumber);

    let test_content = "SSN: ***********";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 1);
    assert_eq!(results[0].data_type, SensitiveDataType::SocialSecurityNumber);
    assert!(results[0].confidence > 0.6);
}

#[tokio::test]
async fn test_pattern_matcher_phone_number_detection() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::PhoneNumber);

    let test_content = "Call me at (************* or ************";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 2);
    assert!(results.iter().all(|r| r.data_type == SensitiveDataType::PhoneNumber));
}

#[tokio::test]
async fn test_pattern_matcher_crypto_seed_phrase() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::CryptoSeedPhrase);

    let test_content = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 1);
    assert_eq!(results[0].data_type, SensitiveDataType::CryptoSeedPhrase);
    assert!(results[0].confidence > 0.8); // 12-word phrase should have high confidence
}

#[tokio::test]
async fn test_pattern_matcher_no_false_positives() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::Email);
    matcher.add_enabled_type(SensitiveDataType::CreditCard);

    let test_content = "This is just normal text with no sensitive data.";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 0);
}

#[tokio::test]
async fn test_pattern_matcher_multiline_content() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::Email);
    matcher.add_enabled_type(SensitiveDataType::PhoneNumber);

    let test_content = "Line 1: Contact info\nLine 2: <EMAIL>\nLine 3: Phone: 555-1234";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 2);

    // Check line numbers are correct
    let email_result = results.iter().find(|r| r.data_type == SensitiveDataType::Email).unwrap();
    assert_eq!(email_result.line_number, Some(2));

    let phone_result = results.iter().find(|r| r.data_type == SensitiveDataType::PhoneNumber).unwrap();
    assert_eq!(phone_result.line_number, Some(3));
}

#[tokio::test]
async fn test_pattern_matcher_disabled_types() {
    let mut matcher = PatternMatcher::new().unwrap();
    // Don't enable any types

    let test_content = "Email: <EMAIL>, Phone: 555-1234";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 0);
}

#[tokio::test]
async fn test_pattern_matcher_confidence_scoring() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::CreditCard);

    // Valid credit card (should have high confidence)
    let valid_cc = "****************";
    let results_valid = matcher.scan_content(valid_cc, "test.txt");

    // Invalid credit card pattern (should have lower confidence)
    let invalid_cc = "1234567890123456";
    let results_invalid = matcher.scan_content(invalid_cc, "test.txt");

    if !results_valid.is_empty() && !results_invalid.is_empty() {
        assert!(results_valid[0].confidence > results_invalid[0].confidence);
    }
}

#[tokio::test]
async fn test_pattern_matcher_context_hints() {
    let mut matcher = PatternMatcher::new().unwrap();
    matcher.add_enabled_type(SensitiveDataType::Email);

    let test_content = "Please contact our support <NAME_EMAIL>";
    let results = matcher.scan_content(test_content, "test.txt");

    assert_eq!(results.len(), 1);
    assert!(results[0].context_hint.is_some());
    assert!(results[0].context_hint.as_ref().unwrap().contains("<EMAIL>"));
}
