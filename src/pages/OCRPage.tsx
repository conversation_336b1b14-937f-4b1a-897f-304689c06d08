import React, { useState } from 'react';
import OCRIntegration from '../components/OCRIntegration';

interface OCRPageProps {
  onBackToMain: () => void;
}

interface OCRResult {
  file_id: string;
  extracted_text: string;
  confidence: number;
  language: string;
  processing_time_ms: number;
  character_count: number;
  word_count: number;
  file_metadata?: {
    name: string;
    path: string;
    size: number;
    type: string;
    modified: string;
  };
}

const OCRPage: React.FC<OCRPageProps> = ({ onBackToMain }) => {
  const [ocrResults, setOcrResults] = useState<OCRResult[]>([]);

  const handleResultsChange = (results: OCRResult[]) => {
    setOcrResults(results);
  };

  return (
    <div className="simplified-main-screen">
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '20px',
        paddingBottom: '16px',
        borderBottom: '2px solid #e5e7eb'
      }}>
        <div>
          <h2 style={{ 
            color: '#111827', 
            margin: 0, 
            fontSize: '28px', 
            fontWeight: '700',
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}>
            📄 OCR & Document Processing
          </h2>
          <p style={{ 
            color: '#6b7280', 
            margin: '8px 0 0 0', 
            fontSize: '16px' 
          }}>
            Extract text from images and PDFs with 100+ language support
          </p>
        </div>
        
        {/* Results Summary */}
        {ocrResults.length > 0 && (
          <div style={{
            background: '#f0f9ff',
            border: '1px solid #bae6fd',
            borderRadius: '8px',
            padding: '12px 16px',
            textAlign: 'center'
          }}>
            <div style={{ 
              fontSize: '24px', 
              fontWeight: '700', 
              color: '#0369a1',
              marginBottom: '4px'
            }}>
              {ocrResults.length}
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: '#0369a1',
              fontWeight: '500'
            }}>
              Document{ocrResults.length !== 1 ? 's' : ''} Processed
            </div>
          </div>
        )}
      </div>

      {/* OCR Integration Component */}
      <OCRIntegration onResultsChange={handleResultsChange} />

      {/* Navigation */}
      <div style={{ 
        display: 'flex', 
        gap: '12px', 
        marginTop: '32px',
        paddingTop: '20px',
        borderTop: '1px solid #e5e7eb'
      }}>
        <button
          onClick={onBackToMain}
          style={{
            padding: '12px 24px',
            background: '#6b7280',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            transition: 'background-color 0.2s'
          }}
          onMouseOver={(e) => e.currentTarget.style.background = '#4b5563'}
          onMouseOut={(e) => e.currentTarget.style.background = '#6b7280'}
        >
          ← Back to Main
        </button>

        {/* Additional Navigation Options */}
        {ocrResults.length > 0 && (
          <>
            <button
              onClick={() => {
                const allText = ocrResults.map((result, index) => 
                  `=== Document ${index + 1} ===\n${result.extracted_text}\n`
                ).join('\n');
                
                navigator.clipboard.writeText(allText);
                alert(`📋 Copied text from ${ocrResults.length} document(s) to clipboard!`);
              }}
              style={{
                padding: '12px 24px',
                background: '#059669',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.background = '#047857'}
              onMouseOut={(e) => e.currentTarget.style.background = '#059669'}
            >
              📋 Copy All Text
            </button>

            <button
              onClick={() => {
                const exportData = {
                  export_timestamp: new Date().toISOString(),
                  total_documents: ocrResults.length,
                  results: ocrResults.map((result, index) => ({
                    document_number: index + 1,
                    file_name: result.file_metadata?.name || `document_${index + 1}`,
                    extracted_text: result.extracted_text,
                    character_count: result.character_count,
                    word_count: result.word_count,
                    language: result.language,
                    confidence: result.confidence,
                    processing_time_ms: result.processing_time_ms
                  }))
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
                  type: 'application/json' 
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `ocr-batch-export-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
                
                alert(`📊 Exported ${ocrResults.length} document(s) to JSON file!`);
              }}
              style={{
                padding: '12px 24px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.background = '#2563eb'}
              onMouseOut={(e) => e.currentTarget.style.background = '#3b82f6'}
            >
              📊 Export Batch
            </button>
          </>
        )}
      </div>

      {/* Footer Info */}
      <div style={{
        marginTop: '24px',
        padding: '16px',
        background: '#f9fafb',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        fontSize: '12px',
        color: '#6b7280',
        textAlign: 'center'
      }}>
        <div style={{ marginBottom: '8px' }}>
          <strong>🔧 OCR Engine Status:</strong> Ready • 
          <strong> 🌐 Languages:</strong> 100+ Supported • 
          <strong> 📁 Formats:</strong> JPG, PNG, BMP, TIFF, WebP, PDF
        </div>
        <div>
          <strong>🚀 Features:</strong> Real-time processing, Language auto-detection, 
          Privacy scanning, Batch export, High accuracy text extraction
        </div>
      </div>
    </div>
  );
};

export default OCRPage;
