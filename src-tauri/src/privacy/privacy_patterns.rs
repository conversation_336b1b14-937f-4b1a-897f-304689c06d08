use regex::Regex;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Privacy-specific pattern definitions and validation
/// 
/// This module contains comprehensive patterns for detecting various types
/// of privacy-sensitive data with validation algorithms.
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct PrivacyPatterns {
    /// Compiled regex patterns for different data types
    patterns: HashMap<String, Vec<Regex>>,
}

/// Pattern match result with validation
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PrivacyPatternMatch {
    /// Type of pattern matched
    pub pattern_type: String,
    /// Matched text
    pub matched_text: String,
    /// Confidence score (0.0-1.0)
    pub confidence: f32,
    /// Start position in text
    pub start_pos: usize,
    /// End position in text
    pub end_pos: usize,
    /// Whether the match passed validation
    pub is_valid: bool,
    /// Validation details
    pub validation_info: Option<String>,
}

impl PrivacyPatterns {
    /// Create a new privacy patterns instance with all patterns loaded
    pub fn new() -> Self {
        let mut patterns = HashMap::new();

        // Load all pattern definitions
        Self::load_ssn_patterns(&mut patterns);
        Self::load_credit_card_patterns(&mut patterns);
        Self::load_phone_patterns(&mut patterns);
        Self::load_email_patterns(&mut patterns);
        Self::load_drivers_license_patterns(&mut patterns);
        Self::load_passport_patterns(&mut patterns);
        Self::load_bank_account_patterns(&mut patterns);

        Self { patterns }
    }
    
    /// Find all privacy pattern matches in text
    pub fn find_matches(&self, text: &str) -> Vec<PrivacyPatternMatch> {
        let mut matches = Vec::new();
        
        for (pattern_type, regexes) in &self.patterns {
            for regex in regexes {
                for mat in regex.find_iter(text) {
                    let matched_text = mat.as_str().to_string();
                    let confidence = self.calculate_confidence(pattern_type, &matched_text);
                    
                    // Apply validation based on pattern type
                    let (is_valid, validation_info) = match pattern_type.as_str() {
                        "ssn" => {
                            let valid = Self::validate_ssn(&matched_text);
                            (valid, if valid { None } else { Some("Failed SSN validation".to_string()) })
                        }
                        "credit_card" => {
                            let valid = Self::validate_credit_card(&matched_text);
                            (valid, if valid { None } else { Some("Failed credit card validation".to_string()) })
                        }
                        "phone" => {
                            let valid = Self::validate_phone(&matched_text);
                            (valid, if valid { None } else { Some("Failed phone validation".to_string()) })
                        }
                        "email" => {
                            let valid = Self::validate_email(&matched_text);
                            (valid, if valid { None } else { Some("Failed email validation".to_string()) })
                        }
                        _ => (true, None)
                    };
                    
                    matches.push(PrivacyPatternMatch {
                        pattern_type: pattern_type.clone(),
                        matched_text,
                        confidence,
                        start_pos: mat.start(),
                        end_pos: mat.end(),
                        is_valid,
                        validation_info,
                    });
                }
            }
        }
        
        // Sort by position and filter by confidence
        matches.sort_by_key(|m| m.start_pos);
        matches.into_iter().filter(|m| m.confidence >= 0.5).collect()
    }
    
    /// Load Social Security Number patterns
    fn load_ssn_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let ssn_regexes = vec![
            // Standard format: ***********
            Regex::new(r"\b\d{3}-\d{2}-\d{4}\b").unwrap(),
            // No dashes: 123456789
            Regex::new(r"\b\d{9}\b").unwrap(),
            // With spaces: 123 45 6789
            Regex::new(r"\b\d{3}\s\d{2}\s\d{4}\b").unwrap(),
            // Mixed separators: 123.45.6789
            Regex::new(r"\b\d{3}\.\d{2}\.\d{4}\b").unwrap(),
        ];
        patterns.insert("ssn".to_string(), ssn_regexes);
    }
    
    /// Load credit card patterns
    fn load_credit_card_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let cc_regexes = vec![
            // Visa: 4xxx-xxxx-xxxx-xxxx
            Regex::new(r"\b4\d{3}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b").unwrap(),
            // MasterCard: 5xxx-xxxx-xxxx-xxxx
            Regex::new(r"\b5[1-5]\d{2}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b").unwrap(),
            // American Express: 3xxx-xxxxxx-xxxxx
            Regex::new(r"\b3[47]\d{2}[-\s]?\d{6}[-\s]?\d{5}\b").unwrap(),
            // Discover: 6xxx-xxxx-xxxx-xxxx
            Regex::new(r"\b6(?:011|5\d{2})[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b").unwrap(),
            // Generic 16-digit pattern
            Regex::new(r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b").unwrap(),
        ];
        patterns.insert("credit_card".to_string(), cc_regexes);
    }
    
    /// Load phone number patterns
    fn load_phone_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let phone_regexes = vec![
            // US format: (*************
            Regex::new(r"\(\d{3}\)\s?\d{3}-\d{4}").unwrap(),
            // US format: ************
            Regex::new(r"\b\d{3}-\d{3}-\d{4}\b").unwrap(),
            // US format: ************
            Regex::new(r"\b\d{3}\.\d{3}\.\d{4}\b").unwrap(),
            // US format: 1234567890
            Regex::new(r"\b\d{10}\b").unwrap(),
            // International: ******-456-7890
            Regex::new(r"\+\d{1,3}[-\s]?\d{3}[-\s]?\d{3}[-\s]?\d{4}").unwrap(),
        ];
        patterns.insert("phone".to_string(), phone_regexes);
    }
    
    /// Load email patterns
    fn load_email_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let email_regexes = vec![
            // Standard email pattern
            Regex::new(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b").unwrap(),
        ];
        patterns.insert("email".to_string(), email_regexes);
    }
    
    /// Load driver's license patterns (US states)
    fn load_drivers_license_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let dl_regexes = vec![
            // California: 1 letter + 7 digits
            Regex::new(r"\b[A-Z]\d{7}\b").unwrap(),
            // Texas: 8 digits
            Regex::new(r"\b\d{8}\b").unwrap(),
            // New York: 9 digits or 1 letter + 8 digits
            Regex::new(r"\b(?:\d{9}|[A-Z]\d{8})\b").unwrap(),
            // Florida: 1 letter + 12 digits
            Regex::new(r"\b[A-Z]\d{12}\b").unwrap(),
        ];
        patterns.insert("drivers_license".to_string(), dl_regexes);
    }
    
    /// Load passport patterns
    fn load_passport_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let passport_regexes = vec![
            // US Passport: 9 digits
            Regex::new(r"\b\d{9}\b").unwrap(),
            // US Passport Book: 2 letters + 7 digits
            Regex::new(r"\b[A-Z]{2}\d{7}\b").unwrap(),
        ];
        patterns.insert("passport".to_string(), passport_regexes);
    }
    
    /// Load bank account patterns
    fn load_bank_account_patterns(patterns: &mut HashMap<String, Vec<Regex>>) {
        let bank_regexes = vec![
            // US bank account: 8-17 digits
            Regex::new(r"\b\d{8,17}\b").unwrap(),
            // IBAN format
            Regex::new(r"\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}([A-Z0-9]?){0,16}\b").unwrap(),
        ];
        patterns.insert("bank_account".to_string(), bank_regexes);
    }
    
    /// Calculate confidence score for a pattern match
    fn calculate_confidence(&self, pattern_type: &str, matched_text: &str) -> f32 {
        match pattern_type {
            "ssn" => Self::calculate_ssn_confidence(matched_text),
            "credit_card" => Self::calculate_credit_card_confidence(matched_text),
            "phone" => Self::calculate_phone_confidence(matched_text),
            "email" => Self::calculate_email_confidence(matched_text),
            _ => 0.7, // Default confidence
        }
    }
    
    /// Calculate SSN confidence based on format and validation
    fn calculate_ssn_confidence(text: &str) -> f32 {
        let clean_text = text.replace(['-', ' ', '.'], "");
        if clean_text.len() != 9 {
            return 0.3;
        }
        
        // Check for obvious invalid patterns
        if clean_text.chars().all(|c| c == clean_text.chars().next().unwrap()) {
            return 0.1; // All same digit
        }
        
        if Self::validate_ssn(text) {
            0.9
        } else {
            0.6
        }
    }
    
    /// Calculate credit card confidence using Luhn algorithm
    fn calculate_credit_card_confidence(text: &str) -> f32 {
        if Self::validate_credit_card(text) {
            0.95
        } else {
            0.4
        }
    }
    
    /// Calculate phone confidence based on format
    fn calculate_phone_confidence(text: &str) -> f32 {
        if text.contains('(') && text.contains(')') {
            0.9 // Formatted phone number
        } else if text.contains('-') || text.contains('.') {
            0.8 // Separated format
        } else {
            0.7 // Plain digits
        }
    }
    
    /// Calculate email confidence
    fn calculate_email_confidence(text: &str) -> f32 {
        if text.contains('@') && text.contains('.') {
            0.9
        } else {
            0.5
        }
    }
    
    /// Validate Social Security Number
    fn validate_ssn(text: &str) -> bool {
        let clean = text.replace(['-', ' ', '.'], "");
        if clean.len() != 9 || !clean.chars().all(|c| c.is_ascii_digit()) {
            return false;
        }
        
        // Check for invalid SSN patterns
        let area = &clean[0..3];
        let group = &clean[3..5];
        let serial = &clean[5..9];
        
        // Invalid area codes
        if area == "000" || area == "666" || area.starts_with('9') {
            return false;
        }
        
        // Invalid group or serial
        if group == "00" || serial == "0000" {
            return false;
        }
        
        true
    }
    
    /// Validate credit card using Luhn algorithm
    fn validate_credit_card(text: &str) -> bool {
        let clean = text.replace(['-', ' ', '.'], "");
        if clean.len() < 13 || clean.len() > 19 || !clean.chars().all(|c| c.is_ascii_digit()) {
            return false;
        }
        
        // Luhn algorithm
        let mut sum = 0;
        let mut alternate = false;
        
        for c in clean.chars().rev() {
            let mut digit = c.to_digit(10).unwrap() as i32;
            
            if alternate {
                digit *= 2;
                if digit > 9 {
                    digit = (digit % 10) + 1;
                }
            }
            
            sum += digit;
            alternate = !alternate;
        }
        
        sum % 10 == 0
    }
    
    /// Validate phone number format
    fn validate_phone(_text: &str) -> bool {
        // Basic validation - could be enhanced
        true
    }
    
    /// Validate email format
    fn validate_email(text: &str) -> bool {
        text.contains('@') && text.contains('.') && text.len() > 5
    }
}

impl Default for PrivacyPatterns {
    fn default() -> Self {
        Self::new()
    }
}
