/// Scan Analytics Implementation
/// 
/// This module provides detailed analytics processing for scan results,
/// including pattern recognition, trend analysis, and statistical computations
/// for the PrivacyAI enterprise analytics dashboard.

use super::{
    AnalyticsDataPoint, AnalyticsMetrics, PerformanceMetrics, RiskSummary, ComplianceStatus,
    TimePeriod, PerformanceTrend, RiskLevel, RiskTrend, RiskCategory, ComplianceLevel,
    PerformanceBottleneck
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};

/// Scan analytics processor for generating insights from scan data
pub struct ScanAnalytics {
    /// Historical data for trend analysis
    historical_data: Vec<AnalyticsDataPoint>,
    
    /// Performance baseline for comparison
    performance_baseline: PerformanceBaseline,
    
    /// Risk assessment thresholds
    risk_thresholds: RiskThresholds,
}

/// Performance baseline for comparison and trend analysis
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceBaseline {
    /// Baseline scan time per profile (ms)
    pub baseline_scan_times: HashMap<String, f64>,
    
    /// Baseline memory usage per profile (MB)
    pub baseline_memory_usage: HashMap<String, f64>,
    
    /// Baseline throughput per profile (files/min)
    pub baseline_throughput: HashMap<String, f64>,
    
    /// Baseline error rates per profile (%)
    pub baseline_error_rates: HashMap<String, f64>,
    
    /// When the baseline was established
    pub baseline_date: DateTime<Utc>,
}

impl Default for PerformanceBaseline {
    fn default() -> Self {
        let mut baseline_scan_times = HashMap::new();
        baseline_scan_times.insert("quick_text_scan".to_string(), 150.0);
        baseline_scan_times.insert("financial_audit".to_string(), 300.0);
        baseline_scan_times.insert("identity_documents".to_string(), 600.0);
        baseline_scan_times.insert("cryptocurrency".to_string(), 250.0);
        baseline_scan_times.insert("file_integrity".to_string(), 100.0);
        baseline_scan_times.insert("comprehensive".to_string(), 800.0);
        
        let mut baseline_memory_usage = HashMap::new();
        baseline_memory_usage.insert("quick_text_scan".to_string(), 30.0);
        baseline_memory_usage.insert("financial_audit".to_string(), 50.0);
        baseline_memory_usage.insert("identity_documents".to_string(), 80.0);
        baseline_memory_usage.insert("cryptocurrency".to_string(), 40.0);
        baseline_memory_usage.insert("file_integrity".to_string(), 25.0);
        baseline_memory_usage.insert("comprehensive".to_string(), 100.0);
        
        let mut baseline_throughput = HashMap::new();
        baseline_throughput.insert("quick_text_scan".to_string(), 400.0);
        baseline_throughput.insert("financial_audit".to_string(), 200.0);
        baseline_throughput.insert("identity_documents".to_string(), 100.0);
        baseline_throughput.insert("cryptocurrency".to_string(), 240.0);
        baseline_throughput.insert("file_integrity".to_string(), 600.0);
        baseline_throughput.insert("comprehensive".to_string(), 75.0);
        
        let mut baseline_error_rates = HashMap::new();
        baseline_error_rates.insert("quick_text_scan".to_string(), 1.5);
        baseline_error_rates.insert("financial_audit".to_string(), 2.0);
        baseline_error_rates.insert("identity_documents".to_string(), 3.0);
        baseline_error_rates.insert("cryptocurrency".to_string(), 1.8);
        baseline_error_rates.insert("file_integrity".to_string(), 0.5);
        baseline_error_rates.insert("comprehensive".to_string(), 2.5);
        
        Self {
            baseline_scan_times,
            baseline_memory_usage,
            baseline_throughput,
            baseline_error_rates,
            baseline_date: Utc::now(),
        }
    }
}

/// Risk assessment thresholds for categorizing findings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskThresholds {
    /// Critical risk threshold (findings per file)
    pub critical_threshold: f64,
    
    /// High risk threshold (findings per file)
    pub high_threshold: f64,
    
    /// Medium risk threshold (findings per file)
    pub medium_threshold: f64,
    
    /// Risk score thresholds (0.0-1.0)
    pub risk_score_thresholds: RiskScoreThresholds,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskScoreThresholds {
    pub critical: f64,
    pub high: f64,
    pub medium: f64,
    pub low: f64,
}

impl Default for RiskThresholds {
    fn default() -> Self {
        Self {
            critical_threshold: 5.0,
            high_threshold: 3.0,
            medium_threshold: 1.0,
            risk_score_thresholds: RiskScoreThresholds {
                critical: 0.9,
                high: 0.7,
                medium: 0.4,
                low: 0.2,
            },
        }
    }
}

impl ScanAnalytics {
    /// Create a new scan analytics processor
    pub fn new() -> Self {
        Self {
            historical_data: Vec::new(),
            performance_baseline: PerformanceBaseline::default(),
            risk_thresholds: RiskThresholds::default(),
        }
    }
    
    /// Process scan data and generate comprehensive analytics
    pub fn process_analytics(
        &mut self,
        data_points: &[AnalyticsDataPoint],
        time_period: TimePeriod,
    ) -> AnalyticsMetrics {
        // Update historical data
        self.update_historical_data(data_points);
        
        // Calculate basic metrics
        let basic_metrics = self.calculate_basic_metrics(data_points, &time_period);
        
        // Calculate performance metrics with trend analysis
        let performance_metrics = self.calculate_performance_metrics(data_points);
        
        // Calculate risk assessment
        let risk_summary = self.calculate_risk_summary(data_points);
        
        // Calculate compliance status
        let compliance_status = self.calculate_compliance_status(data_points);
        
        AnalyticsMetrics {
            time_period,
            total_scans: basic_metrics.total_scans,
            total_files: basic_metrics.total_files,
            total_processing_time_ms: basic_metrics.total_processing_time_ms,
            avg_processing_time_ms: basic_metrics.avg_processing_time_ms,
            total_findings: basic_metrics.total_findings,
            findings_by_severity: basic_metrics.findings_by_severity,
            findings_by_type: basic_metrics.findings_by_type,
            file_types_distribution: basic_metrics.file_types_distribution,
            profile_usage_stats: basic_metrics.profile_usage_stats,
            performance_metrics,
            risk_summary,
            compliance_status,
        }
    }
    
    /// Update historical data for trend analysis
    fn update_historical_data(&mut self, new_data: &[AnalyticsDataPoint]) {
        self.historical_data.extend_from_slice(new_data);
        
        // Keep only last 90 days of historical data for performance
        let cutoff = Utc::now() - Duration::days(90);
        self.historical_data.retain(|dp| dp.timestamp >= cutoff);
    }
    
    /// Calculate basic metrics from data points
    fn calculate_basic_metrics(&self, data: &[AnalyticsDataPoint], _time_period: &TimePeriod) -> BasicMetrics {
        let total_scans = data.len() as u64;
        let total_files = data.len() as u64; // Each data point represents one file
        let total_processing_time_ms = data.iter().map(|d| d.processing_time_ms).sum();
        let avg_processing_time_ms = if total_files > 0 {
            total_processing_time_ms as f64 / total_files as f64
        } else {
            0.0
        };
        
        let total_findings = data.iter().map(|d| d.findings_count as u64).sum();
        
        // Aggregate findings by severity
        let mut findings_by_severity = HashMap::new();
        for data_point in data {
            for (severity, count) in &data_point.findings_by_severity {
                *findings_by_severity.entry(severity.clone()).or_insert(0) += *count as u64;
            }
        }
        
        // Aggregate findings by type
        let mut findings_by_type = HashMap::new();
        for data_point in data {
            for (finding_type, count) in &data_point.findings_by_type {
                *findings_by_type.entry(finding_type.clone()).or_insert(0) += *count as u64;
            }
        }
        
        // Calculate file types distribution
        let mut file_types_distribution = HashMap::new();
        for data_point in data {
            *file_types_distribution.entry(data_point.file_type.clone()).or_insert(0) += 1;
        }
        
        // Calculate profile usage statistics
        let mut profile_usage_stats = HashMap::new();
        for data_point in data {
            *profile_usage_stats.entry(data_point.scan_profile.clone()).or_insert(0) += 1;
        }
        
        BasicMetrics {
            total_scans,
            total_files,
            total_processing_time_ms,
            avg_processing_time_ms,
            total_findings,
            findings_by_severity,
            findings_by_type,
            file_types_distribution,
            profile_usage_stats,
        }
    }
    
    /// Calculate performance metrics with trend analysis
    fn calculate_performance_metrics(&self, data: &[AnalyticsDataPoint]) -> PerformanceMetrics {
        if data.is_empty() {
            return self.get_empty_performance_metrics();
        }
        
        let avg_scan_time_ms = data.iter().map(|d| d.processing_time_ms as f64).sum::<f64>() / data.len() as f64;
        let peak_memory_usage_mb = data.iter().map(|d| d.memory_usage_mb).max().unwrap_or(0);
        let avg_memory_usage_mb = data.iter().map(|d| d.memory_usage_mb as f64).sum::<f64>() / data.len() as f64;
        
        // Calculate throughput (files per minute)
        let total_time_minutes = data.iter().map(|d| d.processing_time_ms as f64).sum::<f64>() / 60000.0;
        let throughput_files_per_minute = if total_time_minutes > 0.0 {
            data.len() as f64 / total_time_minutes
        } else {
            0.0
        };
        
        // Calculate error rate
        let error_count = data.iter().filter(|d| !d.scan_success).count();
        let error_rate_percent = if data.len() > 0 {
            (error_count as f64 / data.len() as f64) * 100.0
        } else {
            0.0
        };
        
        // Analyze performance trend
        let performance_trend = self.analyze_performance_trend(data);
        
        // Identify bottlenecks
        let bottlenecks = self.identify_performance_bottlenecks(data);
        
        PerformanceMetrics {
            avg_scan_time_ms,
            peak_memory_usage_mb,
            avg_memory_usage_mb,
            throughput_files_per_minute,
            cache_hit_rate_percent: 85.0, // Placeholder - would be calculated from cache metrics
            error_rate_percent,
            performance_trend,
            bottlenecks,
        }
    }
    
    /// Analyze performance trend compared to baseline and historical data
    fn analyze_performance_trend(&self, current_data: &[AnalyticsDataPoint]) -> PerformanceTrend {
        if current_data.is_empty() || self.historical_data.len() < 10 {
            return PerformanceTrend::Stable { variance_percent: 0.0 };
        }
        
        // Calculate current average performance
        let current_avg = current_data.iter().map(|d| d.processing_time_ms as f64).sum::<f64>() / current_data.len() as f64;
        
        // Calculate historical average (last 30 days excluding current period)
        let cutoff = Utc::now() - Duration::days(30);
        let historical_data: Vec<_> = self.historical_data.iter()
            .filter(|d| d.timestamp >= cutoff)
            .filter(|d| !current_data.iter().any(|cd| cd.scan_session_id == d.scan_session_id))
            .collect();
        
        if historical_data.is_empty() {
            return PerformanceTrend::Stable { variance_percent: 0.0 };
        }
        
        let historical_avg = historical_data.iter().map(|d| d.processing_time_ms as f64).sum::<f64>() / historical_data.len() as f64;
        
        // Calculate percentage change
        let change_percent = ((current_avg - historical_avg) / historical_avg) * 100.0;
        
        match change_percent {
            x if x < -5.0 => PerformanceTrend::Improving { improvement_percent: -x },
            x if x > 5.0 => PerformanceTrend::Degrading { degradation_percent: x },
            x => PerformanceTrend::Stable { variance_percent: x.abs() },
        }
    }
    
    /// Identify performance bottlenecks in the scanning process
    fn identify_performance_bottlenecks(&self, data: &[AnalyticsDataPoint]) -> Vec<PerformanceBottleneck> {
        let mut bottlenecks = Vec::new();
        
        // Analyze by scan profile to identify slow profiles
        let mut profile_performance = HashMap::new();
        for data_point in data {
            let entry = profile_performance.entry(data_point.scan_profile.clone()).or_insert(Vec::new());
            entry.push(data_point.processing_time_ms as f64);
        }
        
        for (profile, times) in profile_performance {
            let avg_time = times.iter().sum::<f64>() / times.len() as f64;
            let baseline_time = self.performance_baseline.baseline_scan_times.get(&profile).unwrap_or(&800.0);
            
            if avg_time > baseline_time * 1.2 {
                let impact_score = ((avg_time - baseline_time) / baseline_time).min(1.0) as f32;
                bottlenecks.push(PerformanceBottleneck {
                    component: format!("Scan Profile: {}", profile),
                    impact_score,
                    description: format!("Profile {} is running {}ms slower than baseline", profile, avg_time - baseline_time),
                    recommendation: "Consider optimizing detection algorithms or reducing enabled features".to_string(),
                });
            }
        }
        
        // Analyze memory usage bottlenecks
        let avg_memory = data.iter().map(|d| d.memory_usage_mb as f64).sum::<f64>() / data.len() as f64;
        if avg_memory > 150.0 {
            bottlenecks.push(PerformanceBottleneck {
                component: "Memory Usage".to_string(),
                impact_score: ((avg_memory - 100.0) / 100.0).min(1.0) as f32,
                description: format!("Average memory usage is {}MB, above optimal range", avg_memory),
                recommendation: "Consider reducing cache size or optimizing memory allocation".to_string(),
            });
        }
        
        bottlenecks
    }
    
    /// Calculate risk summary from scan findings
    fn calculate_risk_summary(&self, data: &[AnalyticsDataPoint]) -> RiskSummary {
        let mut critical_findings = 0u64;
        let mut high_risk_findings = 0u64;
        let mut medium_risk_findings = 0u64;
        let mut low_risk_findings = 0u64;
        
        // Count findings by severity
        for data_point in data {
            for (severity, count) in &data_point.findings_by_severity {
                match severity.as_str() {
                    "Critical" => critical_findings += *count as u64,
                    "High" => high_risk_findings += *count as u64,
                    "Medium" => medium_risk_findings += *count as u64,
                    "Low" => low_risk_findings += *count as u64,
                    _ => {}
                }
            }
        }
        
        // Determine overall risk level
        let overall_risk_level = if critical_findings > 0 {
            RiskLevel::Critical
        } else if high_risk_findings > 10 {
            RiskLevel::High
        } else if high_risk_findings > 0 || medium_risk_findings > 20 {
            RiskLevel::Medium
        } else if medium_risk_findings > 0 || low_risk_findings > 50 {
            RiskLevel::Low
        } else {
            RiskLevel::Minimal
        };
        
        // Analyze risk trend
        let risk_trend = self.analyze_risk_trend(data);
        
        // Identify top risk categories
        let top_risk_categories = self.identify_top_risk_categories(data);
        
        // Identify files requiring urgent attention
        let urgent_files = data.iter()
            .filter(|d| d.risk_score as f64 > self.risk_thresholds.risk_score_thresholds.critical)
            .map(|d| d.file_path.clone())
            .collect();
        
        RiskSummary {
            overall_risk_level,
            critical_findings,
            high_risk_findings,
            medium_risk_findings,
            low_risk_findings,
            risk_trend,
            top_risk_categories,
            urgent_files,
        }
    }
    
    /// Analyze risk trend over time
    fn analyze_risk_trend(&self, current_data: &[AnalyticsDataPoint]) -> RiskTrend {
        // Simplified trend analysis - would be more sophisticated in production
        let current_risk_score = current_data.iter().map(|d| d.risk_score as f64).sum::<f64>() / current_data.len().max(1) as f64;
        
        // Compare with historical average
        let historical_avg = if !self.historical_data.is_empty() {
            self.historical_data.iter().map(|d| d.risk_score as f64).sum::<f64>() / self.historical_data.len() as f64
        } else {
            current_risk_score
        };
        
        let change_percent = if historical_avg > 0.0 {
            ((current_risk_score - historical_avg) / historical_avg) * 100.0
        } else {
            0.0
        };
        
        match change_percent {
            x if x > 10.0 => RiskTrend::Increasing { rate_percent: x },
            x if x < -10.0 => RiskTrend::Decreasing { rate_percent: -x },
            x => RiskTrend::Stable { variance_percent: x.abs() },
        }
    }
    
    /// Identify top risk categories from findings
    fn identify_top_risk_categories(&self, data: &[AnalyticsDataPoint]) -> Vec<RiskCategory> {
        let mut category_stats = HashMap::new();
        
        for data_point in data {
            for (finding_type, count) in &data_point.findings_by_type {
                let entry = category_stats.entry(finding_type.clone()).or_insert((0u64, Vec::new()));
                entry.0 += *count as u64;
                entry.1.push(data_point.risk_score);
            }
        }
        
        let mut categories: Vec<_> = category_stats.into_iter()
            .map(|(category, (count, risk_scores))| {
                let avg_risk = risk_scores.iter().sum::<f32>() / risk_scores.len().max(1) as f32;
                RiskCategory {
                    category,
                    finding_count: count,
                    impact_score: avg_risk,
                    trend: RiskTrend::Stable { variance_percent: 0.0 }, // Simplified
                }
            })
            .collect();
        
        // Sort by impact score and take top 5
        categories.sort_by(|a, b| b.impact_score.partial_cmp(&a.impact_score).unwrap());
        categories.truncate(5);
        
        categories
    }
    
    /// Calculate compliance status based on findings
    fn calculate_compliance_status(&self, data: &[AnalyticsDataPoint]) -> ComplianceStatus {
        // Simplified compliance calculation - would be more sophisticated in production
        let total_findings = data.iter().map(|d| d.findings_count).sum::<u32>();
        let critical_findings = data.iter()
            .map(|d| *d.findings_by_severity.get("Critical").unwrap_or(&0))
            .sum::<u32>();
        
        let gdpr_status = if critical_findings > 0 {
            ComplianceLevel::NonCompliant { violations_count: critical_findings }
        } else if total_findings > 10 {
            ComplianceLevel::PartiallyCompliant { issues_count: total_findings }
        } else {
            ComplianceLevel::Compliant
        };
        
        ComplianceStatus {
            gdpr_status: gdpr_status.clone(),
            hipaa_status: gdpr_status.clone(),
            pci_dss_status: gdpr_status,
            sox_status: ComplianceLevel::NotApplicable,
            custom_compliance: HashMap::new(),
            violations: Vec::new(),
            last_audit_date: Some(Utc::now()),
            next_audit_date: Some(Utc::now() + Duration::days(30)),
        }
    }
    
    /// Get empty performance metrics for when no data is available
    fn get_empty_performance_metrics(&self) -> PerformanceMetrics {
        PerformanceMetrics {
            avg_scan_time_ms: 0.0,
            peak_memory_usage_mb: 0,
            avg_memory_usage_mb: 0.0,
            throughput_files_per_minute: 0.0,
            cache_hit_rate_percent: 0.0,
            error_rate_percent: 0.0,
            performance_trend: PerformanceTrend::Stable { variance_percent: 0.0 },
            bottlenecks: Vec::new(),
        }
    }
}

/// Basic metrics calculation result
struct BasicMetrics {
    total_scans: u64,
    total_files: u64,
    total_processing_time_ms: u64,
    avg_processing_time_ms: f64,
    total_findings: u64,
    findings_by_severity: HashMap<String, u64>,
    findings_by_type: HashMap<String, u64>,
    file_types_distribution: HashMap<String, u64>,
    profile_usage_stats: HashMap<String, u64>,
}

impl Default for ScanAnalytics {
    fn default() -> Self {
        Self::new()
    }
}
