.secure-operations {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    color: #2c3e50;
    border: 1px solid #e5e7eb;
}

.secure-operations-header {
    text-align: center;
    margin-bottom: 30px;
}

.secure-operations-header h2 {
    margin: 0 0 10px 0;
    font-size: 2.2em;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.secure-operations-header p {
    margin: 0;
    font-size: 1.1em;
    opacity: 0.9;
    line-height: 1.4;
}

.operation-selector {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.operation-selector label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.operation-selector label:hover {
    transform: translateY(-2px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.operation-selector input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #4CAF50;
}

.file-drop-zone {
    border: 3px dashed #cbd5e1;
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8fafc;
    margin-bottom: 20px;
}

.file-drop-zone:hover {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: translateY(-2px);
}

.drop-zone-content p {
    font-size: 1.3em;
    margin: 0 0 15px 0;
    font-weight: 500;
}

.selected-files {
    background: #f1f5f9;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid #e2e8f0;
}

.selected-files ul {
    list-style: none;
    padding: 0;
    margin: 10px 0 0 0;
}

.selected-files li {
    padding: 5px 0;
    border-bottom: 1px solid #e2e8f0;
}

.selected-files li:last-child {
    border-bottom: none;
}

.password-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
}

.password-input {
    margin-bottom: 15px;
}

.password-input:last-child {
    margin-bottom: 0;
}

.password-input label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.password-input input {
    width: 100%;
    padding: 12px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 1em;
    transition: all 0.3s ease;
}

.password-input input::placeholder {
    color: #9ca3af;
}

.password-input input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
}

.config-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
}

.config-section h3 {
    margin: 0 0 15px 0;
    font-size: 1.3em;
    font-weight: 600;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.config-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 0.95em;
}

.config-item input,
.config-item select {
    width: 100%;
    padding: 10px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 1em;
    transition: all 0.3s ease;
}

.config-item input:focus,
.config-item select:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
}

.config-item select option {
    background: white;
    color: #374151;
}

.progress-section {
    margin-bottom: 20px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.process-button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.2em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.process-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.process-button:disabled {
    background: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.error-message {
    background: rgba(244, 67, 54, 0.2);
    border: 2px solid rgba(244, 67, 54, 0.5);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.error-message p {
    margin: 0;
    font-weight: 500;
}

.results-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.results-section h3 {
    margin: 0 0 15px 0;
    font-size: 1.3em;
    font-weight: 600;
}

.scan-stats,
.archive-stats,
.deletion-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-weight: 500;
    opacity: 0.9;
}

.stat-value {
    font-weight: 600;
    font-size: 1.1em;
}

.failed-deletions {
    margin-top: 20px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: 6px;
    padding: 15px;
}

.failed-deletions h4 {
    margin: 0 0 10px 0;
    color: #ff6b6b;
}

.failed-deletions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.failed-deletions li {
    padding: 5px 0;
    border-bottom: 1px solid rgba(244, 67, 54, 0.2);
    font-size: 0.9em;
}

.failed-deletions li:last-child {
    border-bottom: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .secure-operations {
        margin: 10px;
        padding: 15px;
    }
    
    .operation-selector {
        flex-direction: column;
        gap: 15px;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
    
    .scan-stats,
    .archive-stats,
    .deletion-stats {
        grid-template-columns: 1fr;
    }
}
