import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { X, FileText, File, AlertCircle, Download, ExternalLink } from 'lucide-react';

interface FilePreviewInfo {
  file_path: string;
  file_name: string;
  file_size: number;
  file_type: string;
  is_text_file: boolean;
  is_pdf_file: boolean;
  can_preview: boolean;
}

interface FilePreviewContent {
  file_info: FilePreviewInfo;
  content_type: string;
  content: string;
  is_truncated: boolean;
  total_size: number;
  preview_size: number;
}

interface FilePreviewProps {
  filePath: string;
  fileName: string;
  onClose: () => void;
}

const FilePreview: React.FC<FilePreviewProps> = ({ filePath, fileName, onClose }) => {
  const [previewInfo, setPreviewInfo] = useState<FilePreviewInfo | null>(null);
  const [previewContent, setPreviewContent] = useState<FilePreviewContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPreview();
  }, [filePath]);

  const loadPreview = async () => {
    try {
      setLoading(true);
      setError(null);

      // First get file info
      const info = await invoke<FilePreviewInfo>('get_file_preview_info', {
        filePath: filePath
      });
      
      setPreviewInfo(info);

      if (!info.can_preview) {
        setError(`File type '${info.file_type}' is not supported for preview`);
        setLoading(false);
        return;
      }

      // Then get content if it's previewable
      const content = await invoke<FilePreviewContent>('get_file_preview_content', {
        filePath: filePath,
        maxSize: 1024 * 1024 // 1MB limit
      });

      setPreviewContent(content);
    } catch (err) {
      console.error('Failed to load file preview:', err);
      setError(err as string);
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderTextPreview = (content: FilePreviewContent) => {
    return (
      <div className="flex-1 overflow-auto">
        <div className="bg-gray-50 border rounded-lg p-4 font-mono text-sm">
          <pre className="whitespace-pre-wrap break-words text-black">{content.content}</pre>
        </div>
        {content.is_truncated && (
          <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
            <AlertCircle className="inline w-4 h-4 mr-1" />
            File truncated. Showing {formatFileSize(content.preview_size)} of {formatFileSize(content.total_size)}
          </div>
        )}
      </div>
    );
  };

  const renderPDFPreview = (content: FilePreviewContent) => {
    return (
      <div className="flex-1 overflow-auto">
        <div className="bg-gray-50 border rounded-lg p-4 text-center">
          <File className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600 mb-4">PDF Preview</p>
          <p className="text-sm text-gray-500 mb-4">
            PDF files require external viewer for full preview
          </p>
          <div className="space-y-2">
            <button
              onClick={() => {
                // Open with system default PDF viewer
                invoke('tauri', {
                  cmd: 'shell',
                  data: {
                    open: content.file_info.file_path
                  }
                }).catch(console.error);
              }}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Open with System Viewer
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-3">
            <FileText className="w-5 h-5 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold truncate">{fileName}</h2>
              {previewInfo && (
                <p className="text-sm text-gray-500">
                  {previewInfo.file_type.toUpperCase()} • {formatFileSize(previewInfo.file_size)}
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-4 overflow-hidden flex flex-col">
          {loading && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading preview...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-400" />
                <p className="text-red-600 mb-2">Preview Error</p>
                <p className="text-sm text-gray-500">{error}</p>
              </div>
            </div>
          )}

          {!loading && !error && previewContent && (
            <>
              {previewContent.file_info.is_text_file && renderTextPreview(previewContent)}
              {previewContent.file_info.is_pdf_file && renderPDFPreview(previewContent)}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {previewInfo && (
                <>
                  File: {previewInfo.file_path}
                </>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:bg-gray-200 rounded"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilePreview;
