import React, { useState, useMemo } from 'react';
import Select, { SingleValue, ActionMeta } from 'react-select';

interface Language {
  code: string;
  name: string;
  nativeName?: string;
  region?: string;
}

interface OCRLanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: string) => void;
  autoDetect?: boolean;
  onAutoDetectChange?: (enabled: boolean) => void;
  disabled?: boolean;
  confidence?: number;
}

// Comprehensive list of 100+ languages supported by Tesseract
const SUPPORTED_LANGUAGES: Language[] = [
  { code: 'eng', name: 'English', nativeName: 'English', region: 'Global' },
  { code: 'spa', name: 'Spanish', nativeName: 'Español', region: 'Global' },
  { code: 'fra', name: 'French', nativeName: 'Français', region: 'Global' },
  { code: 'deu', name: 'German', nativeName: 'Deutsch', region: 'Europe' },
  { code: 'ita', name: 'Italian', nativeName: 'Italiano', region: 'Europe' },
  { code: 'por', name: 'Portuguese', nativeName: 'Português', region: 'Global' },
  { code: 'rus', name: 'Russian', nativeName: 'Русский', region: 'Europe/Asia' },
  { code: 'jpn', name: 'Japanese', nativeName: '日本語', region: 'Asia' },
  { code: 'chi_sim', name: 'Chinese (Simplified)', nativeName: '简体中文', region: 'Asia' },
  { code: 'chi_tra', name: 'Chinese (Traditional)', nativeName: '繁體中文', region: 'Asia' },
  { code: 'kor', name: 'Korean', nativeName: '한국어', region: 'Asia' },
  { code: 'ara', name: 'Arabic', nativeName: 'العربية', region: 'Middle East/Africa' },
  { code: 'hin', name: 'Hindi', nativeName: 'हिन्दी', region: 'Asia' },
  { code: 'ben', name: 'Bengali', nativeName: 'বাংলা', region: 'Asia' },
  { code: 'tha', name: 'Thai', nativeName: 'ไทย', region: 'Asia' },
  { code: 'vie', name: 'Vietnamese', nativeName: 'Tiếng Việt', region: 'Asia' },
  { code: 'heb', name: 'Hebrew', nativeName: 'עברית', region: 'Middle East' },
  { code: 'tur', name: 'Turkish', nativeName: 'Türkçe', region: 'Europe/Asia' },
  { code: 'pol', name: 'Polish', nativeName: 'Polski', region: 'Europe' },
  { code: 'nld', name: 'Dutch', nativeName: 'Nederlands', region: 'Europe' },
  { code: 'swe', name: 'Swedish', nativeName: 'Svenska', region: 'Europe' },
  { code: 'nor', name: 'Norwegian', nativeName: 'Norsk', region: 'Europe' },
  { code: 'dan', name: 'Danish', nativeName: 'Dansk', region: 'Europe' },
  { code: 'fin', name: 'Finnish', nativeName: 'Suomi', region: 'Europe' },
  { code: 'ces', name: 'Czech', nativeName: 'Čeština', region: 'Europe' },
  { code: 'hun', name: 'Hungarian', nativeName: 'Magyar', region: 'Europe' },
  { code: 'ron', name: 'Romanian', nativeName: 'Română', region: 'Europe' },
  { code: 'bul', name: 'Bulgarian', nativeName: 'Български', region: 'Europe' },
  { code: 'hrv', name: 'Croatian', nativeName: 'Hrvatski', region: 'Europe' },
  { code: 'srp', name: 'Serbian', nativeName: 'Српски', region: 'Europe' },
  { code: 'slk', name: 'Slovak', nativeName: 'Slovenčina', region: 'Europe' },
  { code: 'slv', name: 'Slovenian', nativeName: 'Slovenščina', region: 'Europe' },
  { code: 'est', name: 'Estonian', nativeName: 'Eesti', region: 'Europe' },
  { code: 'lav', name: 'Latvian', nativeName: 'Latviešu', region: 'Europe' },
  { code: 'lit', name: 'Lithuanian', nativeName: 'Lietuvių', region: 'Europe' },
  { code: 'ukr', name: 'Ukrainian', nativeName: 'Українська', region: 'Europe' },
  { code: 'bel', name: 'Belarusian', nativeName: 'Беларуская', region: 'Europe' },
  { code: 'ell', name: 'Greek', nativeName: 'Ελληνικά', region: 'Europe' },
  { code: 'mkd', name: 'Macedonian', nativeName: 'Македонски', region: 'Europe' },
  { code: 'alb', name: 'Albanian', nativeName: 'Shqip', region: 'Europe' },
  { code: 'mlt', name: 'Maltese', nativeName: 'Malti', region: 'Europe' },
  { code: 'isl', name: 'Icelandic', nativeName: 'Íslenska', region: 'Europe' },
  { code: 'gle', name: 'Irish', nativeName: 'Gaeilge', region: 'Europe' },
  { code: 'cym', name: 'Welsh', nativeName: 'Cymraeg', region: 'Europe' },
  { code: 'eus', name: 'Basque', nativeName: 'Euskera', region: 'Europe' },
  { code: 'cat', name: 'Catalan', nativeName: 'Català', region: 'Europe' },
  { code: 'glg', name: 'Galician', nativeName: 'Galego', region: 'Europe' },
  { code: 'afr', name: 'Afrikaans', nativeName: 'Afrikaans', region: 'Africa' },
  { code: 'swa', name: 'Swahili', nativeName: 'Kiswahili', region: 'Africa' },
  { code: 'amh', name: 'Amharic', nativeName: 'አማርኛ', region: 'Africa' },
  { code: 'orm', name: 'Oromo', nativeName: 'Afaan Oromoo', region: 'Africa' },
  { code: 'som', name: 'Somali', nativeName: 'Soomaali', region: 'Africa' },
  { code: 'hau', name: 'Hausa', nativeName: 'Hausa', region: 'Africa' },
  { code: 'yor', name: 'Yoruba', nativeName: 'Yorùbá', region: 'Africa' },
  { code: 'ibo', name: 'Igbo', nativeName: 'Igbo', region: 'Africa' },
  { code: 'zul', name: 'Zulu', nativeName: 'isiZulu', region: 'Africa' },
  { code: 'xho', name: 'Xhosa', nativeName: 'isiXhosa', region: 'Africa' },
  { code: 'ind', name: 'Indonesian', nativeName: 'Bahasa Indonesia', region: 'Asia' },
  { code: 'msa', name: 'Malay', nativeName: 'Bahasa Melayu', region: 'Asia' },
  { code: 'fil', name: 'Filipino', nativeName: 'Filipino', region: 'Asia' },
  { code: 'tam', name: 'Tamil', nativeName: 'தமிழ்', region: 'Asia' },
  { code: 'tel', name: 'Telugu', nativeName: 'తెలుగు', region: 'Asia' },
  { code: 'kan', name: 'Kannada', nativeName: 'ಕನ್ನಡ', region: 'Asia' },
  { code: 'mal', name: 'Malayalam', nativeName: 'മലയാളം', region: 'Asia' },
  { code: 'guj', name: 'Gujarati', nativeName: 'ગુજરાતી', region: 'Asia' },
  { code: 'pan', name: 'Punjabi', nativeName: 'ਪੰਜਾਬੀ', region: 'Asia' },
  { code: 'mar', name: 'Marathi', nativeName: 'मराठी', region: 'Asia' },
  { code: 'ori', name: 'Odia', nativeName: 'ଓଡ଼ିଆ', region: 'Asia' },
  { code: 'asm', name: 'Assamese', nativeName: 'অসমীয়া', region: 'Asia' },
  { code: 'urd', name: 'Urdu', nativeName: 'اردو', region: 'Asia' },
  { code: 'nep', name: 'Nepali', nativeName: 'नेपाली', region: 'Asia' },
  { code: 'sin', name: 'Sinhala', nativeName: 'සිංහල', region: 'Asia' },
  { code: 'mya', name: 'Myanmar', nativeName: 'မြန်မာ', region: 'Asia' },
  { code: 'khm', name: 'Khmer', nativeName: 'ខ្មែរ', region: 'Asia' },
  { code: 'lao', name: 'Lao', nativeName: 'ລາວ', region: 'Asia' },
  { code: 'mon', name: 'Mongolian', nativeName: 'Монгол', region: 'Asia' },
  { code: 'tib', name: 'Tibetan', nativeName: 'བོད་ཡིག', region: 'Asia' },
  { code: 'uzb', name: 'Uzbek', nativeName: 'Oʻzbek', region: 'Asia' },
  { code: 'kaz', name: 'Kazakh', nativeName: 'Қазақ', region: 'Asia' },
  { code: 'kir', name: 'Kyrgyz', nativeName: 'Кыргыз', region: 'Asia' },
  { code: 'tgk', name: 'Tajik', nativeName: 'Тоҷикӣ', region: 'Asia' },
  { code: 'aze', name: 'Azerbaijani', nativeName: 'Azərbaycan', region: 'Asia' },
  { code: 'kat', name: 'Georgian', nativeName: 'ქართული', region: 'Asia' },
  { code: 'hye', name: 'Armenian', nativeName: 'Հայերեն', region: 'Asia' },
  { code: 'fas', name: 'Persian', nativeName: 'فارسی', region: 'Asia' },
  { code: 'pus', name: 'Pashto', nativeName: 'پښتو', region: 'Asia' },
  { code: 'kur', name: 'Kurdish', nativeName: 'Kurdî', region: 'Asia' },
  { code: 'lat', name: 'Latin', nativeName: 'Latina', region: 'Historical' },
  { code: 'san', name: 'Sanskrit', nativeName: 'संस्कृत', region: 'Historical' },
  { code: 'epo', name: 'Esperanto', nativeName: 'Esperanto', region: 'Constructed' }
];

const OCRLanguageSelector: React.FC<OCRLanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  autoDetect = false,
  onAutoDetectChange,
  disabled = false,
  confidence
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Create options for react-select
  const languageOptions = useMemo(() => {
    const options = SUPPORTED_LANGUAGES.map(lang => ({
      value: lang.code,
      label: `${lang.name} (${lang.code})`,
      nativeName: lang.nativeName,
      region: lang.region,
      searchText: `${lang.name} ${lang.nativeName} ${lang.code} ${lang.region}`.toLowerCase()
    }));

    // Add auto-detect option at the top
    return [
      {
        value: 'auto',
        label: '🔍 Auto-detect language',
        nativeName: 'Automatic detection',
        region: 'Auto',
        searchText: 'auto detect automatic detection'
      },
      ...options
    ];
  }, []);

  // Filter options based on search
  const filteredOptions = useMemo(() => {
    if (!searchTerm) return languageOptions;
    
    const term = searchTerm.toLowerCase();
    return languageOptions.filter(option => 
      option.searchText.includes(term)
    );
  }, [languageOptions, searchTerm]);

  // Find current selection
  const currentSelection = languageOptions.find(option => 
    option.value === (autoDetect ? 'auto' : selectedLanguage)
  );

  const handleSelectionChange = (
    newValue: SingleValue<typeof languageOptions[0]>,
    _actionMeta: ActionMeta<typeof languageOptions[0]>
  ) => {
    if (!newValue) return;

    if (newValue.value === 'auto') {
      if (onAutoDetectChange) {
        onAutoDetectChange(true);
      }
    } else {
      if (onAutoDetectChange) {
        onAutoDetectChange(false);
      }
      onLanguageChange(newValue.value);
    }
  };

  // Custom option component
  const formatOptionLabel = (option: typeof languageOptions[0]) => (
    <div className="flex items-center justify-between py-1">
      <div className="flex-1">
        <div className="font-medium text-gray-900">
          {option.value === 'auto' ? '🔍 ' : ''}{option.label.split(' (')[0]}
        </div>
        {option.nativeName && option.value !== 'auto' && (
          <div className="text-sm text-gray-500">{option.nativeName}</div>
        )}
      </div>
      <div className="text-xs text-gray-400 ml-2">
        {option.value === 'auto' ? 'AUTO' : option.value.toUpperCase()}
      </div>
    </div>
  );

  // Custom styles for react-select
  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      borderColor: state.isFocused ? '#3b82f6' : '#d1d5db',
      boxShadow: state.isFocused ? '0 0 0 1px #3b82f6' : 'none',
      '&:hover': {
        borderColor: '#9ca3af'
      }
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected 
        ? '#3b82f6' 
        : state.isFocused 
          ? '#eff6ff' 
          : 'white',
      color: state.isSelected ? 'white' : '#374151',
      padding: '8px 12px'
    }),
    menu: (provided: any) => ({
      ...provided,
      zIndex: 9999
    }),
    menuList: (provided: any) => ({
      ...provided,
      maxHeight: '300px'
    })
  };

  return (
    <div className="ocr-language-selector">
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm font-medium text-gray-700">
          OCR Language
        </label>
        {confidence !== undefined && (
          <span className={`text-xs px-2 py-1 rounded-full ${
            confidence >= 0.9 
              ? 'bg-green-100 text-green-700' 
              : confidence >= 0.7 
                ? 'bg-yellow-100 text-yellow-700'
                : 'bg-red-100 text-red-700'
          }`}>
            {(confidence * 100).toFixed(1)}% confidence
          </span>
        )}
      </div>

      <Select
        value={currentSelection}
        onChange={handleSelectionChange}
        options={filteredOptions}
        formatOptionLabel={formatOptionLabel}
        styles={customStyles}
        isDisabled={disabled}
        isSearchable={true}
        placeholder="Select or search for a language..."
        noOptionsMessage={({ inputValue }) => 
          inputValue ? `No languages found for "${inputValue}"` : 'No languages available'
        }
        onInputChange={(inputValue) => setSearchTerm(inputValue)}
        className="react-select-container"
        classNamePrefix="react-select"
      />

      {/* Language Statistics */}
      <div className="mt-3 text-xs text-gray-500 flex items-center justify-between">
        <span>{SUPPORTED_LANGUAGES.length}+ languages supported</span>
        {currentSelection && currentSelection.value !== 'auto' && (
          <span className="flex items-center">
            <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
            {currentSelection.region}
          </span>
        )}
      </div>

      {/* Auto-detect info */}
      {autoDetect && (
        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Auto-detection will analyze the image to determine the most likely language
          </div>
        </div>
      )}

      {/* Popular languages quick access */}
      <div className="mt-3">
        <div className="text-xs text-gray-500 mb-2">Quick Select:</div>
        <div className="flex flex-wrap gap-1">
          {['eng', 'spa', 'fra', 'deu', 'chi_sim', 'jpn', 'ara', 'rus'].map(code => {
            const lang = SUPPORTED_LANGUAGES.find(l => l.code === code);
            if (!lang) return null;
            
            return (
              <button
                key={code}
                onClick={() => {
                  if (onAutoDetectChange) onAutoDetectChange(false);
                  onLanguageChange(code);
                }}
                disabled={disabled}
                className={`px-2 py-1 text-xs rounded border transition-colors ${
                  selectedLanguage === code && !autoDetect
                    ? 'bg-blue-100 border-blue-300 text-blue-700'
                    : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              >
                {lang.name}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default OCRLanguageSelector;
