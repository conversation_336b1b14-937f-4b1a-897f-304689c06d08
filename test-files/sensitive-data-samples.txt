# PrivacyAI Test File - Sensitive Data Samples
# This file contains various types of sensitive data for testing privacy detection

## Personal Information
Name: <PERSON>
Email: <EMAIL>
Phone: (*************
Address: 123 Main Street, Anytown, NY 12345

## Social Security Numbers
SSN: ***********
Social Security: ***********
SSN: 555 44 3333

## Credit Card Numbers
Visa: 4532-1234-5678-9012
MasterCard: ****************
American Express: ***************
Discover: ****************

## Banking Information
Account Number: *********012
Routing Number: *********
IBAN: GB29 NWBK 6016 1331 9268 19

## Cryptocurrency Addresses
Bitcoin: **********************************
Bitcoin: ******************************************
Ethereum: ******************************************
Litecoin: LTC1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4

## Cryptocurrency Seed Phrases
abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about
army van defense carry jealous true garbage claim echo media make crunch

## Domain Handles & Web3 Identities
AdaHandle: $johndoe
AdaHandle: $crypto_trader
Unstoppable Domain: johndoe.crypto
Unstoppable Domain: myname.nft
ENS Domain: vitalik.eth

## API Keys and Tokens
API Key: sk-*********0abcdef*********0abcdef
AWS Access Key: AKIAIOSFODNN7EXAMPLE
JWT Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c

## Passwords (for testing only)
Password: MySecretPassword123!
PIN: 1234
Security Code: 789

## Medical Information
Patient ID: P*********
Medical Record: MR-2023-001234
Insurance ID: INS987654321

## Government IDs
Driver License: D*********
Passport: P*********
Tax ID: 12-3456789

## Private Keys (test only - not real)
Private Key: 5KJvsngHeMpm884wtkJNzQGaCErckhHJBGFsvd3VyK5qMZXj3hS
Mnemonic: abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about

## Network Information
IP Address: *************
MAC Address: 00:1B:44:11:3A:B7
WiFi Password: MyWiFiPassword123

## Financial Data
Income: $75,000 annually
Net Worth: $250,000
Investment Account: INV-*********

## Sensitive Business Data
Employee ID: EMP001234
Customer ID: CUST567890
Contract Number: CNT-2023-001

## Test Patterns for Edge Cases
Almost SSN: 123-45-678 (missing digit)
Fake Credit Card: 1234-5678-9012-3456 (invalid)
Test Email: <EMAIL>
Debug Info: DEBUG_MODE=true, LOG_LEVEL=verbose

## International Data
UK National Insurance: *********
Canadian SIN: ***********
German Tax ID: *********01

## Cryptocurrency Extended
Cardano Address: addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh0*********abcdef
Solana Address: ********************************
Polygon Address: ******************************************
Binance Smart Chain: ******************************************

## World Mobile Token (WMT) Test Data
WMT Address: addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlhwmt123456
WMT Stake Pool: pool1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlhwmt

## More AdaHandles
$privacy_ai
$test_user
$crypto_enthusiast
$blockchain_dev

## More Unstoppable Domains
privacy.crypto
blockchain.nft
crypto.blockchain
defi.bitcoin
nft.wallet
web3.x
dao.888
trading.dao
staking.zil

This file contains various types of sensitive data for comprehensive testing of the PrivacyAI detection engine.
