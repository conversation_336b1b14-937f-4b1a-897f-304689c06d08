{"name": "tauri-appprivacy-ai", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-table": "^8.21.3", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "clsx": "^2.1.1", "lucide-react": "^0.526.0", "pdfjs-dist": "^5.4.54", "react": "^19.1.0", "react-arborist": "^3.4.3", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "recharts": "^3.1.0", "tesseract.js": "^6.0.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@vitest/coverage-v8": "^3.2.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}