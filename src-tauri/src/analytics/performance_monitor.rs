/// Performance Monitor Implementation
/// 
/// This module provides real-time performance monitoring capabilities for the
/// PrivacyAI scanning system, tracking metrics, identifying bottlenecks, and
/// providing optimization recommendations.

use super::{AnalyticsDataPoint, PerformanceMetrics, PerformanceTrend, PerformanceBottleneck};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::time::{Duration, Instant};
use chrono::{DateTime, Utc};

/// Real-time performance monitor for scanning operations
pub struct PerformanceMonitor {
    /// Recent performance samples for real-time analysis
    recent_samples: VecDeque<PerformanceSample>,
    
    /// Performance thresholds for alerting
    thresholds: PerformanceThresholds,
    
    /// Current performance state
    current_state: PerformanceState,
    
    /// Performance alerts
    active_alerts: Vec<PerformanceAlert>,
    
    /// Monitoring configuration
    config: MonitorConfig,
}

/// Individual performance sample
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSample {
    /// Timestamp of the sample
    pub timestamp: DateTime<Utc>,
    
    /// Scan profile used
    pub scan_profile: String,
    
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    
    /// Memory usage in MB
    pub memory_usage_mb: u64,
    
    /// CPU usage percentage
    pub cpu_usage_percent: f32,
    
    /// File size processed
    pub file_size_bytes: u64,
    
    /// Cache hit rate for this operation
    pub cache_hit_rate: f32,
    
    /// Number of concurrent operations
    pub concurrent_operations: u32,
    
    /// Success status
    pub success: bool,
    
    /// Error details if failed
    pub error_type: Option<String>,
}

/// Performance thresholds for monitoring and alerting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceThresholds {
    /// Maximum acceptable processing time per profile (ms)
    pub max_processing_times: HashMap<String, u64>,
    
    /// Maximum acceptable memory usage (MB)
    pub max_memory_usage_mb: u64,
    
    /// Maximum acceptable CPU usage (%)
    pub max_cpu_usage_percent: f32,
    
    /// Minimum acceptable cache hit rate (%)
    pub min_cache_hit_rate_percent: f32,
    
    /// Maximum acceptable error rate (%)
    pub max_error_rate_percent: f32,
    
    /// Performance degradation threshold (%)
    pub degradation_threshold_percent: f32,
}

impl Default for PerformanceThresholds {
    fn default() -> Self {
        let mut max_processing_times = HashMap::new();
        max_processing_times.insert("quick_text_scan".to_string(), 200);
        max_processing_times.insert("financial_audit".to_string(), 400);
        max_processing_times.insert("identity_documents".to_string(), 800);
        max_processing_times.insert("cryptocurrency".to_string(), 350);
        max_processing_times.insert("file_integrity".to_string(), 150);
        max_processing_times.insert("comprehensive".to_string(), 1000);
        
        Self {
            max_processing_times,
            max_memory_usage_mb: 200,
            max_cpu_usage_percent: 80.0,
            min_cache_hit_rate_percent: 70.0,
            max_error_rate_percent: 5.0,
            degradation_threshold_percent: 20.0,
        }
    }
}

/// Current performance state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceState {
    /// Current average processing time
    pub avg_processing_time_ms: f64,
    
    /// Current memory usage
    pub current_memory_usage_mb: u64,
    
    /// Current CPU usage
    pub current_cpu_usage_percent: f32,
    
    /// Current cache hit rate
    pub current_cache_hit_rate_percent: f32,
    
    /// Current error rate
    pub current_error_rate_percent: f32,
    
    /// Current throughput (operations per minute)
    pub current_throughput: f64,
    
    /// Performance health status
    pub health_status: PerformanceHealth,
    
    /// Last update timestamp
    pub last_update: DateTime<Utc>,
}

/// Performance health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PerformanceHealth {
    Excellent,
    Good,
    Fair,
    Poor,
    Critical,
}

/// Performance alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAlert {
    /// Alert ID
    pub id: String,
    
    /// Alert type
    pub alert_type: AlertType,
    
    /// Alert severity
    pub severity: AlertSeverity,
    
    /// Alert message
    pub message: String,
    
    /// Metric that triggered the alert
    pub metric: String,
    
    /// Current value
    pub current_value: f64,
    
    /// Threshold value
    pub threshold_value: f64,
    
    /// When the alert was triggered
    pub triggered_at: DateTime<Utc>,
    
    /// Recommended action
    pub recommendation: String,
}

/// Alert types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum AlertType {
    PerformanceDegradation,
    MemoryUsageHigh,
    CpuUsageHigh,
    ErrorRateHigh,
    CacheHitRateLow,
    ProcessingTimeHigh,
    ThroughputLow,
}

/// Alert severity levels
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum AlertSeverity {
    Info,
    Warning,
    Critical,
}

/// Monitor configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    /// Number of samples to keep for analysis
    pub sample_buffer_size: usize,
    
    /// Monitoring update frequency (seconds)
    pub update_frequency_seconds: u64,
    
    /// Enable real-time alerting
    pub enable_alerting: bool,
    
    /// Enable performance logging
    pub enable_logging: bool,
    
    /// Alert cooldown period (seconds)
    pub alert_cooldown_seconds: u64,
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            sample_buffer_size: 1000,
            update_frequency_seconds: 5,
            enable_alerting: true,
            enable_logging: true,
            alert_cooldown_seconds: 300, // 5 minutes
        }
    }
}

impl PerformanceMonitor {
    /// Create a new performance monitor
    pub fn new(config: MonitorConfig) -> Self {
        Self {
            recent_samples: VecDeque::with_capacity(config.sample_buffer_size),
            thresholds: PerformanceThresholds::default(),
            current_state: PerformanceState {
                avg_processing_time_ms: 0.0,
                current_memory_usage_mb: 0,
                current_cpu_usage_percent: 0.0,
                current_cache_hit_rate_percent: 0.0,
                current_error_rate_percent: 0.0,
                current_throughput: 0.0,
                health_status: PerformanceHealth::Good,
                last_update: Utc::now(),
            },
            active_alerts: Vec::new(),
            config,
        }
    }
    
    /// Record a new performance sample
    pub fn record_sample(&mut self, sample: PerformanceSample) {
        // Add sample to buffer
        self.recent_samples.push_back(sample);
        
        // Maintain buffer size
        if self.recent_samples.len() > self.config.sample_buffer_size {
            self.recent_samples.pop_front();
        }
        
        // Update current state
        self.update_current_state();
        
        // Check for alerts
        if self.config.enable_alerting {
            self.check_alerts();
        }
        
        // Log performance data
        if self.config.enable_logging {
            self.log_performance_data();
        }
    }
    
    /// Get current performance metrics
    pub fn get_current_metrics(&self) -> PerformanceMetrics {
        let samples: Vec<_> = self.recent_samples.iter().collect();
        
        if samples.is_empty() {
            return self.get_empty_metrics();
        }
        
        let avg_scan_time_ms = samples.iter()
            .map(|s| s.processing_time_ms as f64)
            .sum::<f64>() / samples.len() as f64;
        
        let peak_memory_usage_mb = samples.iter()
            .map(|s| s.memory_usage_mb)
            .max()
            .unwrap_or(0);
        
        let avg_memory_usage_mb = samples.iter()
            .map(|s| s.memory_usage_mb as f64)
            .sum::<f64>() / samples.len() as f64;
        
        let avg_cache_hit_rate = samples.iter()
            .map(|s| s.cache_hit_rate as f64)
            .sum::<f64>() / samples.len() as f64;
        
        let error_count = samples.iter().filter(|s| !s.success).count();
        let error_rate_percent = (error_count as f64 / samples.len() as f64) * 100.0;
        
        // Calculate throughput (operations per minute)
        let total_time_minutes = samples.iter()
            .map(|s| s.processing_time_ms as f64)
            .sum::<f64>() / 60000.0;
        
        let throughput_files_per_minute = if total_time_minutes > 0.0 {
            samples.len() as f64 / total_time_minutes
        } else {
            0.0
        };
        
        // Analyze performance trend
        let performance_trend = self.analyze_trend();
        
        // Identify current bottlenecks
        let bottlenecks = self.identify_bottlenecks();
        
        PerformanceMetrics {
            avg_scan_time_ms,
            peak_memory_usage_mb,
            avg_memory_usage_mb,
            throughput_files_per_minute,
            cache_hit_rate_percent: avg_cache_hit_rate,
            error_rate_percent,
            performance_trend,
            bottlenecks,
        }
    }
    
    /// Get active performance alerts
    pub fn get_active_alerts(&self) -> &[PerformanceAlert] {
        &self.active_alerts
    }
    
    /// Get current performance state
    pub fn get_current_state(&self) -> &PerformanceState {
        &self.current_state
    }
    
    /// Update performance thresholds
    pub fn update_thresholds(&mut self, thresholds: PerformanceThresholds) {
        self.thresholds = thresholds;
    }
    
    /// Clear resolved alerts
    pub fn clear_resolved_alerts(&mut self) {
        let cutoff = Utc::now() - chrono::Duration::seconds(self.config.alert_cooldown_seconds as i64);
        self.active_alerts.retain(|alert| alert.triggered_at > cutoff);
    }
    
    /// Update current performance state
    fn update_current_state(&mut self) {
        if self.recent_samples.is_empty() {
            return;
        }
        
        let recent_window = 20.min(self.recent_samples.len());
        let recent_samples: Vec<_> = self.recent_samples.iter().rev().take(recent_window).collect();
        
        self.current_state.avg_processing_time_ms = recent_samples.iter()
            .map(|s| s.processing_time_ms as f64)
            .sum::<f64>() / recent_samples.len() as f64;
        
        self.current_state.current_memory_usage_mb = recent_samples.iter()
            .map(|s| s.memory_usage_mb)
            .max()
            .unwrap_or(0);
        
        self.current_state.current_cpu_usage_percent = recent_samples.iter()
            .map(|s| s.cpu_usage_percent as f64)
            .sum::<f64>() as f32 / recent_samples.len() as f32;
        
        self.current_state.current_cache_hit_rate_percent = recent_samples.iter()
            .map(|s| s.cache_hit_rate as f64)
            .sum::<f64>() as f32 / recent_samples.len() as f32;
        
        let error_count = recent_samples.iter().filter(|s| !s.success).count();
        self.current_state.current_error_rate_percent = (error_count as f64 / recent_samples.len() as f64) as f32 * 100.0;
        
        // Calculate current throughput
        let total_time_minutes = recent_samples.iter()
            .map(|s| s.processing_time_ms as f64)
            .sum::<f64>() / 60000.0;
        
        self.current_state.current_throughput = if total_time_minutes > 0.0 {
            recent_samples.len() as f64 / total_time_minutes
        } else {
            0.0
        };
        
        // Update health status
        self.current_state.health_status = self.calculate_health_status();
        self.current_state.last_update = Utc::now();
    }
    
    /// Calculate overall performance health status
    fn calculate_health_status(&self) -> PerformanceHealth {
        let mut score = 100.0;
        
        // Check processing time
        if self.current_state.avg_processing_time_ms > 1000.0 {
            score -= 30.0;
        } else if self.current_state.avg_processing_time_ms > 800.0 {
            score -= 15.0;
        }
        
        // Check memory usage
        if self.current_state.current_memory_usage_mb > self.thresholds.max_memory_usage_mb {
            score -= 25.0;
        } else if self.current_state.current_memory_usage_mb > self.thresholds.max_memory_usage_mb * 80 / 100 {
            score -= 10.0;
        }
        
        // Check CPU usage
        if self.current_state.current_cpu_usage_percent > self.thresholds.max_cpu_usage_percent {
            score -= 20.0;
        } else if self.current_state.current_cpu_usage_percent > self.thresholds.max_cpu_usage_percent * 0.8 {
            score -= 10.0;
        }
        
        // Check error rate
        if self.current_state.current_error_rate_percent > self.thresholds.max_error_rate_percent {
            score -= 25.0;
        } else if self.current_state.current_error_rate_percent > self.thresholds.max_error_rate_percent * 0.5 {
            score -= 10.0;
        }
        
        // Check cache hit rate
        if self.current_state.current_cache_hit_rate_percent < self.thresholds.min_cache_hit_rate_percent {
            score -= 15.0;
        }
        
        match score {
            s if s >= 90.0 => PerformanceHealth::Excellent,
            s if s >= 75.0 => PerformanceHealth::Good,
            s if s >= 60.0 => PerformanceHealth::Fair,
            s if s >= 40.0 => PerformanceHealth::Poor,
            _ => PerformanceHealth::Critical,
        }
    }
    
    /// Check for performance alerts
    fn check_alerts(&mut self) {
        self.clear_resolved_alerts();
        
        // Check processing time alerts
        let mut alerts_to_create = Vec::new();
        for (profile, threshold) in &self.thresholds.max_processing_times {
            let profile_samples: Vec<_> = self.recent_samples.iter()
                .filter(|s| s.scan_profile == *profile)
                .collect();

            if !profile_samples.is_empty() {
                let avg_time = profile_samples.iter()
                    .map(|s| s.processing_time_ms as f64)
                    .sum::<f64>() / profile_samples.len() as f64;

                if avg_time > *threshold as f64 {
                    alerts_to_create.push((
                        AlertType::ProcessingTimeHigh,
                        AlertSeverity::Warning,
                        format!("Processing time for {} profile exceeds threshold", profile),
                        format!("{}_processing_time", profile),
                        avg_time,
                        *threshold as f64,
                        "Consider optimizing scan configuration or reducing enabled features".to_string(),
                    ));
                }
            }
        }

        // Create the alerts after the loop
        for (alert_type, severity, message, metric_name, current_value, threshold_value, recommendation) in alerts_to_create {
            self.create_alert(alert_type, severity, message, metric_name, current_value, threshold_value, recommendation);
        }
        
        // Check memory usage alert
        if self.current_state.current_memory_usage_mb > self.thresholds.max_memory_usage_mb {
            self.create_alert(
                AlertType::MemoryUsageHigh,
                AlertSeverity::Critical,
                "Memory usage exceeds threshold".to_string(),
                "memory_usage".to_string(),
                self.current_state.current_memory_usage_mb as f64,
                self.thresholds.max_memory_usage_mb as f64,
                "Reduce cache size or optimize memory allocation".to_string(),
            );
        }
        
        // Check error rate alert
        if self.current_state.current_error_rate_percent > self.thresholds.max_error_rate_percent {
            self.create_alert(
                AlertType::ErrorRateHigh,
                AlertSeverity::Warning,
                "Error rate exceeds threshold".to_string(),
                "error_rate".to_string(),
                self.current_state.current_error_rate_percent as f64,
                self.thresholds.max_error_rate_percent as f64,
                "Investigate scan failures and optimize file handling".to_string(),
            );
        }
    }
    
    /// Create a new performance alert
    fn create_alert(
        &mut self,
        alert_type: AlertType,
        severity: AlertSeverity,
        message: String,
        metric: String,
        current_value: f64,
        threshold_value: f64,
        recommendation: String,
    ) {
        // Check if similar alert already exists
        let alert_exists = self.active_alerts.iter().any(|alert| {
            alert.metric == metric && alert.alert_type as u8 == alert_type as u8
        });
        
        if !alert_exists {
            let alert = PerformanceAlert {
                id: format!("{}_{}", metric, Utc::now().timestamp()),
                alert_type,
                severity,
                message,
                metric,
                current_value,
                threshold_value,
                triggered_at: Utc::now(),
                recommendation,
            };
            
            self.active_alerts.push(alert);
        }
    }
    
    /// Analyze performance trend
    fn analyze_trend(&self) -> PerformanceTrend {
        if self.recent_samples.len() < 10 {
            return PerformanceTrend::Stable { variance_percent: 0.0 };
        }
        
        let half_point = self.recent_samples.len() / 2;
        let first_half: Vec<_> = self.recent_samples.iter().take(half_point).collect();
        let second_half: Vec<_> = self.recent_samples.iter().skip(half_point).collect();
        
        let first_avg = first_half.iter().map(|s| s.processing_time_ms as f64).sum::<f64>() / first_half.len() as f64;
        let second_avg = second_half.iter().map(|s| s.processing_time_ms as f64).sum::<f64>() / second_half.len() as f64;
        
        let change_percent = ((second_avg - first_avg) / first_avg) * 100.0;
        
        match change_percent {
            x if x < -5.0 => PerformanceTrend::Improving { improvement_percent: -x },
            x if x > 5.0 => PerformanceTrend::Degrading { degradation_percent: x },
            x => PerformanceTrend::Stable { variance_percent: x.abs() },
        }
    }
    
    /// Identify current performance bottlenecks
    fn identify_bottlenecks(&self) -> Vec<PerformanceBottleneck> {
        let mut bottlenecks = Vec::new();
        
        // Check for memory bottleneck
        if self.current_state.current_memory_usage_mb > self.thresholds.max_memory_usage_mb * 80 / 100 {
            bottlenecks.push(PerformanceBottleneck {
                component: "Memory Usage".to_string(),
                impact_score: (self.current_state.current_memory_usage_mb as f32 / self.thresholds.max_memory_usage_mb as f32).min(1.0),
                description: format!("Memory usage at {}MB approaching limit", self.current_state.current_memory_usage_mb),
                recommendation: "Consider reducing cache size or processing smaller batches".to_string(),
            });
        }
        
        // Check for cache bottleneck
        if self.current_state.current_cache_hit_rate_percent < self.thresholds.min_cache_hit_rate_percent {
            bottlenecks.push(PerformanceBottleneck {
                component: "Cache Performance".to_string(),
                impact_score: (1.0 - self.current_state.current_cache_hit_rate_percent / 100.0).min(1.0),
                description: format!("Cache hit rate at {:.1}% below optimal", self.current_state.current_cache_hit_rate_percent),
                recommendation: "Increase cache size or optimize caching strategy".to_string(),
            });
        }
        
        bottlenecks
    }
    
    /// Get empty metrics when no data is available
    fn get_empty_metrics(&self) -> PerformanceMetrics {
        PerformanceMetrics {
            avg_scan_time_ms: 0.0,
            peak_memory_usage_mb: 0,
            avg_memory_usage_mb: 0.0,
            throughput_files_per_minute: 0.0,
            cache_hit_rate_percent: 0.0,
            error_rate_percent: 0.0,
            performance_trend: PerformanceTrend::Stable { variance_percent: 0.0 },
            bottlenecks: Vec::new(),
        }
    }
    
    /// Log performance data for analysis
    fn log_performance_data(&self) {
        if let Some(latest_sample) = self.recent_samples.back() {
            println!(
                "Performance: profile={}, time={}ms, memory={}MB, cpu={:.1}%, cache_hit={:.1}%",
                latest_sample.scan_profile,
                latest_sample.processing_time_ms,
                latest_sample.memory_usage_mb,
                latest_sample.cpu_usage_percent,
                latest_sample.cache_hit_rate
            );
        }
    }
}
