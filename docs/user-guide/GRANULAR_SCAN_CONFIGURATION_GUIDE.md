# 🔧 **Granular Scan Configuration - User Guide**

**Version**: 1.0  
**Created**: July 27, 2025  
**Audience**: End Users, System Administrators  
**Scope**: Complete guide to optimizing scan performance through configuration

## 📋 **Overview**

The Granular Scan Configuration system allows you to fine-tune PrivacyAI's scanning behavior to match your specific needs, optimizing performance while maintaining the detection accuracy you require.

### **Why Use Granular Configuration?**
- **Faster Scanning**: Disable unnecessary detection types for up to 81% speed improvement
- **Resource Optimization**: Reduce memory usage and CPU load for better system performance
- **Targeted Detection**: Focus on specific data types relevant to your use case
- **Compliance Alignment**: Configure scans to match regulatory requirements (GDPR, HIPAA, PCI-DSS)

---

## 🚀 **Quick Start: Predefined Profiles**

### **Choosing the Right Profile**

#### **🏃 Quick Text Scan** - *Best for: Fast document processing*
- **Speed**: 81% faster than comprehensive scan
- **Throughput**: 400 files/minute
- **Detection**: Basic privacy patterns (SSN, credit cards, emails, phone numbers)
- **Processing**: Text-only, no OCR or AI analysis
- **Use Case**: Large document archives, log file analysis, quick privacy checks

#### **💰 Financial Audit** - *Best for: Financial compliance*
- **Speed**: 62% faster than comprehensive scan
- **Throughput**: 200 files/minute
- **Detection**: Financial data focus (credit cards, bank accounts, tax IDs, cryptocurrency)
- **Processing**: OCR enabled for financial documents, AI document classification
- **Use Case**: PCI-DSS compliance, financial data discovery, audit preparation

#### **🆔 Identity Document Search** - *Best for: Government ID detection*
- **Speed**: 25% faster than comprehensive scan
- **Throughput**: 100 files/minute
- **Detection**: Government IDs, passports, driver's licenses (US and international)
- **Processing**: High-accuracy OCR, AI visual detection, face detection
- **Use Case**: Identity verification, document classification, privacy impact assessments

#### **₿ Cryptocurrency Security** - *Best for: Crypto asset protection*
- **Speed**: 69% faster than comprehensive scan
- **Throughput**: 240 files/minute
- **Detection**: All cryptocurrency patterns (Bitcoin, Ethereum, Cardano, exchange credentials)
- **Processing**: Text analysis with selective OCR for crypto-related images
- **Use Case**: Cryptocurrency security audits, wallet discovery, exchange credential protection

#### **🔍 File Integrity Check** - *Best for: File corruption and duplicates*
- **Speed**: 87% faster than comprehensive scan
- **Throughput**: 600 files/minute
- **Detection**: File corruption, duplicate detection, metadata analysis
- **Processing**: Hash calculation, metadata extraction, no content analysis
- **Use Case**: System maintenance, storage optimization, backup verification

#### **🔬 Comprehensive Scan** - *Best for: Complete analysis*
- **Speed**: Baseline (800ms per file)
- **Throughput**: 75 files/minute
- **Detection**: All privacy patterns, security threats, file integrity issues
- **Processing**: Full OCR, AI analysis, complete metadata extraction
- **Use Case**: Security audits, compliance verification, complete privacy assessment

---

## ⚙️ **Custom Configuration Guide**

### **Detection Types Configuration**

#### **Privacy-Sensitive Data Detection**
Configure which types of personal information to detect:

```
✅ SSN Detection - Social Security Numbers
✅ Credit Card Detection - Payment card numbers with Luhn validation
✅ Phone Number Detection - US and international phone formats
✅ Email Detection - Email addresses and domains
⚠️ Address Detection - Physical addresses (slower processing)
⚠️ Bank Account Detection - Bank account and routing numbers
⚠️ Tax ID Detection - Tax identification numbers
⚠️ Medical Record Detection - Medical record and patient IDs
```

**Performance Impact**: Each enabled detection type adds 8-25ms processing time per file.

#### **Cryptocurrency Detection**
Select which cryptocurrency patterns to detect:

```
✅ Bitcoin Detection - BTC addresses and private keys
✅ Ethereum Detection - ETH addresses and private keys
✅ Cardano Detection - ADA addresses and AdaHandle formats
✅ World Mobile Token - WMT-specific patterns
⚠️ Unstoppable Domains - .crypto, .nft, .blockchain domains
⚠️ Exchange Credentials - API keys and trading credentials
⚠️ Seed Phrases - BIP39 mnemonic phrases
```

**Performance Impact**: Cryptocurrency detection is highly optimized, adding only 8-25ms per type.

#### **Government ID Detection**
Choose which government identification patterns to detect:

```
✅ US SSN - Social Security Numbers with area code validation
✅ US Driver's License - State-specific format validation
✅ US Passport - US passport number formats
⚠️ International Passport - Global passport number patterns
⚠️ European National ID - EU member state ID patterns
⚠️ Canadian SIN - Social Insurance Numbers
⚠️ UK National Insurance - National Insurance Numbers
⚠️ Australian TFN - Tax File Numbers
```

**Performance Impact**: Government ID detection adds 15-30ms per enabled type.

### **Processing Methods Configuration**

#### **OCR Text Extraction** ⚠️ *High Performance Impact*
Extract text from images and scanned documents:

```
📊 Performance Impact: +200-800ms per image
💾 Memory Usage: +30MB during processing
🎯 Accuracy: 94-98% for clear images
🌍 Languages: 100+ languages supported

Settings:
✅ Image OCR - Extract text from image files
✅ PDF OCR - Process scanned PDF documents
📝 Accuracy Level: 1-10 (higher = more accurate but slower)
📏 Max Image Size: 50MB default (configurable)
```

**When to Enable**: 
- ✅ Processing scanned documents or images
- ✅ Need to detect text in visual content
- ❌ Text-only document processing
- ❌ Performance is critical priority

#### **AI Visual Detection** ⚠️ *Medium Performance Impact*
Use AI models to classify and analyze visual content:

```
📊 Performance Impact: +80ms per image
💾 Memory Usage: +15MB for model loading
🎯 Accuracy: 91-96% depending on content type
📦 Model Size: 11.8MB (cached after first use)

Capabilities:
✅ Privacy Classification - Identify sensitive visual content
✅ Face Detection - Detect faces in images
✅ Text Detection - Identify text regions automatically
✅ Document Classification - Classify document types
🎚️ Confidence Threshold: 0.1-1.0 (higher = fewer false positives)
```

**When to Enable**:
- ✅ Processing images with potential privacy content
- ✅ Need automatic document classification
- ✅ Face detection for privacy compliance
- ❌ Text-only content processing

#### **Metadata Extraction** ✅ *Low Performance Impact*
Extract metadata from files:

```
📊 Performance Impact: +5-20ms per file
💾 Memory Usage: +5MB
🎯 Accuracy: 99% for supported formats

Capabilities:
✅ EXIF Data - Camera info, GPS coordinates, timestamps
✅ Document Metadata - Author, creation date, editing history
✅ File System Data - Permissions, ownership, modification times
⚠️ Hidden Metadata - Embedded comments, revision tracking
```

**When to Enable**: Almost always recommended due to minimal performance impact.

#### **Binary Analysis** ⚠️ *Variable Performance Impact*
Analyze binary files and archives:

```
📊 Performance Impact: +50-200ms per binary file
💾 Memory Usage: +10-30MB depending on file size
📏 File Size Limit: 500MB default

Capabilities:
✅ Executable Analysis - Version info, digital signatures
✅ Archive Analysis - Recursive extraction and scanning
✅ Database Analysis - SQLite, Access file content
⚠️ String Extraction - Extract embedded text patterns
```

**When to Enable**:
- ✅ Processing software archives or executables
- ✅ Database file analysis required
- ❌ Document-only environments

---

## 📊 **Performance Optimization Strategies**

### **Speed-Focused Configuration**
For maximum processing speed:

1. **Choose Quick Text Scan profile** as starting point
2. **Disable OCR processing** unless absolutely necessary
3. **Disable AI visual detection** for text-only content
4. **Enable only essential detection types** for your use case
5. **Use Fast performance mode** with reduced accuracy settings
6. **Enable parallel processing** with multiple worker threads

**Expected Results**: 300-500 files/minute throughput

### **Accuracy-Focused Configuration**
For maximum detection accuracy:

1. **Choose Comprehensive Scan profile** as starting point
2. **Enable all relevant detection types** for your data
3. **Use highest OCR accuracy settings** (level 9-10)
4. **Enable AI visual detection** with low confidence threshold
5. **Use Comprehensive performance mode** with extended timeouts
6. **Include all metadata extraction** options

**Expected Results**: 50-100 files/minute throughput, >95% accuracy

### **Balanced Configuration**
For optimal speed/accuracy balance:

1. **Choose profile closest to your use case** (Financial, Identity, etc.)
2. **Enable OCR with medium accuracy** (level 6-8)
3. **Use selective AI visual detection** based on content type
4. **Focus detection types** on compliance requirements
5. **Use Balanced performance mode** with standard timeouts
6. **Monitor performance metrics** and adjust as needed

**Expected Results**: 150-250 files/minute throughput, 90-95% accuracy

---

## 🎯 **Use Case Examples**

### **GDPR Compliance Scanning**
**Objective**: Identify personal data for GDPR compliance

**Recommended Configuration**:
```
Profile: Custom based on Financial Audit
Detection Types:
  ✅ Privacy Detection (all types enabled)
  ✅ Government ID Detection (EU focus)
  ❌ Cryptocurrency Detection
  ❌ File Integrity Detection

Processing Methods:
  ✅ OCR Processing (medium accuracy)
  ✅ AI Visual Detection (privacy classification)
  ✅ Metadata Extraction (full)
  ⚠️ Binary Analysis (archives only)

Performance: Balanced mode
Expected: 180 files/minute, 93% accuracy
```

### **Financial Institution Audit**
**Objective**: PCI-DSS compliance and financial data discovery

**Recommended Configuration**:
```
Profile: Financial Audit (with modifications)
Detection Types:
  ✅ Privacy Detection (financial focus)
  ✅ Cryptocurrency Detection (exchange credentials)
  ✅ Government ID Detection (SSN, tax IDs)
  ❌ File Integrity Detection

Processing Methods:
  ✅ OCR Processing (high accuracy)
  ✅ AI Visual Detection (document classification)
  ✅ Metadata Extraction (full)
  ✅ Binary Analysis (database files)

Performance: Comprehensive mode
Expected: 120 files/minute, 96% accuracy
```

### **Corporate IT Security Scan**
**Objective**: Identify security risks and cryptocurrency assets

**Recommended Configuration**:
```
Profile: Cryptocurrency Security (with additions)
Detection Types:
  ⚠️ Privacy Detection (basic patterns only)
  ✅ Cryptocurrency Detection (all types)
  ❌ Government ID Detection
  ✅ File Integrity Detection (corruption only)

Processing Methods:
  ⚠️ OCR Processing (low accuracy, crypto images only)
  ❌ AI Visual Detection
  ✅ Metadata Extraction (basic)
  ✅ Binary Analysis (executables and archives)

Performance: Fast mode
Expected: 280 files/minute, 88% accuracy
```

---

## 🔧 **Advanced Configuration Tips**

### **Memory Optimization**
- **Reduce cache size** for systems with limited RAM
- **Disable binary analysis** for large file environments
- **Use streaming processing** for very large files
- **Limit worker threads** on resource-constrained systems

### **Network Storage Optimization**
- **Enable aggressive caching** to reduce network I/O
- **Use batch processing** for multiple files
- **Optimize file reading** with larger buffer sizes
- **Consider local caching** for frequently accessed files

### **Multi-User Environment**
- **Create role-based profiles** for different user types
- **Implement configuration templates** for common use cases
- **Use centralized configuration management** for consistency
- **Monitor resource usage** across multiple concurrent scans

**The granular scan configuration system provides the flexibility to optimize PrivacyAI for any use case while maintaining the security and accuracy standards your organization requires.**
