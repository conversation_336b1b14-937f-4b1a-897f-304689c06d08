/**
 * Advanced Accessibility Hook
 * Provides comprehensive accessibility features and WCAG 2.1 AA+ compliance
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
    accessibilityManager, 
    AccessibilityPreferences, 
    KeyboardShortcut,
    announce,
    registerShortcut 
} from '../utils/accessibilityManager';

export interface AccessibilityState {
    preferences: AccessibilityPreferences;
    isScreenReaderActive: boolean;
    currentFocus: HTMLElement | null;
    announcements: string[];
    keyboardShortcuts: KeyboardShortcut[];
}

export interface FocusManagementOptions {
    trapFocus?: boolean;
    restoreFocus?: boolean;
    skipLinks?: boolean;
    autoFocus?: boolean;
}

export interface AriaLiveOptions {
    priority: 'polite' | 'assertive';
    atomic?: boolean;
    relevant?: 'additions' | 'removals' | 'text' | 'all';
}

export const useAccessibility = (options: FocusManagementOptions = {}) => {
    const [state, setState] = useState<AccessibilityState>({
        preferences: accessibilityManager.getPreferences(),
        isScreenReaderActive: false,
        currentFocus: null,
        announcements: [],
        keyboardShortcuts: accessibilityManager.getShortcuts(),
    });

    const focusHistoryRef = useRef<HTMLElement[]>([]);
    const trapContainerRef = useRef<HTMLElement | null>(null);
    const lastFocusRef = useRef<HTMLElement | null>(null);

    // Screen reader detection
    useEffect(() => {
        const detectScreenReader = () => {
            const isActive = state.preferences.screenReader || 
                           window.speechSynthesis?.getVoices().length > 0 ||
                           navigator.userAgent.includes('NVDA') ||
                           navigator.userAgent.includes('JAWS');
            
            setState(prev => ({ ...prev, isScreenReaderActive: isActive }));
        };

        detectScreenReader();
        
        // Listen for speech synthesis changes
        if (window.speechSynthesis) {
            window.speechSynthesis.addEventListener('voiceschanged', detectScreenReader);
            return () => window.speechSynthesis.removeEventListener('voiceschanged', detectScreenReader);
        }
    }, [state.preferences.screenReader]);

    // Focus management
    useEffect(() => {
        const handleFocusIn = (event: FocusEvent) => {
            const target = event.target as HTMLElement;
            if (target && target !== document.body) {
                setState(prev => ({ ...prev, currentFocus: target }));
                focusHistoryRef.current.push(target);
                
                // Limit focus history size
                if (focusHistoryRef.current.length > 50) {
                    focusHistoryRef.current = focusHistoryRef.current.slice(-25);
                }
            }
        };

        const handleFocusOut = (event: FocusEvent) => {
            const target = event.target as HTMLElement;
            lastFocusRef.current = target;
        };

        document.addEventListener('focusin', handleFocusIn);
        document.addEventListener('focusout', handleFocusOut);

        return () => {
            document.removeEventListener('focusin', handleFocusIn);
            document.removeEventListener('focusout', handleFocusOut);
        };
    }, []);

    // Keyboard navigation enhancement
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // Enhanced Tab navigation
            if (event.key === 'Tab') {
                handleTabNavigation(event);
            }
            
            // Escape key handling
            if (event.key === 'Escape') {
                handleEscapeKey(event);
            }
            
            // Arrow key navigation for custom components
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
                handleArrowNavigation(event);
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, []);

    // Tab navigation handler
    const handleTabNavigation = useCallback((event: KeyboardEvent) => {
        if (trapContainerRef.current && options.trapFocus) {
            const focusableElements = getFocusableElements(trapContainerRef.current);
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            
            if (event.shiftKey) {
                // Shift + Tab (backward)
                if (document.activeElement === firstElement) {
                    event.preventDefault();
                    lastElement?.focus();
                }
            } else {
                // Tab (forward)
                if (document.activeElement === lastElement) {
                    event.preventDefault();
                    firstElement?.focus();
                }
            }
        }
    }, [options.trapFocus]);

    // Escape key handler
    const handleEscapeKey = useCallback((event: KeyboardEvent) => {
        // Close modals, dropdowns, etc.
        const activeModal = document.querySelector('[role="dialog"][aria-modal="true"]');
        if (activeModal) {
            const closeButton = activeModal.querySelector('[aria-label*="close" i], [data-action="close"]') as HTMLElement;
            if (closeButton) {
                closeButton.click();
                event.preventDefault();
            }
        }
        
        // Restore focus if needed
        if (options.restoreFocus && lastFocusRef.current) {
            lastFocusRef.current.focus();
        }
    }, [options.restoreFocus]);

    // Arrow key navigation handler
    const handleArrowNavigation = useCallback((event: KeyboardEvent) => {
        const target = event.target as HTMLElement;
        const role = target.getAttribute('role');
        
        // Handle listbox, menu, tablist navigation
        if (['listbox', 'menu', 'tablist', 'radiogroup'].includes(role || '')) {
            event.preventDefault();
            navigateWithArrows(target, event.key);
        }
    }, []);

    // Get focusable elements
    const getFocusableElements = useCallback((container: HTMLElement): HTMLElement[] => {
        const focusableSelectors = [
            'a[href]',
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable="true"]',
            '[role="button"]:not([aria-disabled="true"])',
            '[role="link"]:not([aria-disabled="true"])',
            '[role="menuitem"]:not([aria-disabled="true"])',
            '[role="tab"]:not([aria-disabled="true"])'
        ].join(', ');
        
        return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[];
    }, []);

    // Navigate with arrow keys
    const navigateWithArrows = useCallback((container: HTMLElement, key: string) => {
        const items = getFocusableElements(container);
        const currentIndex = items.indexOf(document.activeElement as HTMLElement);
        
        let nextIndex = currentIndex;
        
        switch (key) {
            case 'ArrowDown':
            case 'ArrowRight':
                nextIndex = (currentIndex + 1) % items.length;
                break;
            case 'ArrowUp':
            case 'ArrowLeft':
                nextIndex = currentIndex <= 0 ? items.length - 1 : currentIndex - 1;
                break;
        }
        
        if (items[nextIndex]) {
            items[nextIndex].focus();
        }
    }, [getFocusableElements]);

    // Announce to screen readers
    const announceToScreenReader = useCallback((
        message: string, 
        options: AriaLiveOptions = { priority: 'polite' }
    ) => {
        announce(message, options.priority);
        
        setState(prev => ({
            ...prev,
            announcements: [...prev.announcements.slice(-9), message] // Keep last 10
        }));
    }, []);

    // Focus management utilities
    const focusFirst = useCallback((container?: HTMLElement) => {
        const target = container || document.body;
        const focusableElements = getFocusableElements(target);
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    }, [getFocusableElements]);

    const focusLast = useCallback((container?: HTMLElement) => {
        const target = container || document.body;
        const focusableElements = getFocusableElements(target);
        if (focusableElements.length > 0) {
            focusableElements[focusableElements.length - 1].focus();
        }
    }, [getFocusableElements]);

    const restoreFocus = useCallback(() => {
        if (lastFocusRef.current && document.contains(lastFocusRef.current)) {
            lastFocusRef.current.focus();
        }
    }, []);

    // Focus trap management
    const setFocusTrap = useCallback((container: HTMLElement | null) => {
        trapContainerRef.current = container;
        
        if (container && options.autoFocus) {
            // Auto-focus first element in trap
            setTimeout(() => focusFirst(container), 0);
        }
    }, [options.autoFocus, focusFirst]);

    // Register custom keyboard shortcut
    const addKeyboardShortcut = useCallback((shortcut: KeyboardShortcut) => {
        registerShortcut(shortcut);
        setState(prev => ({
            ...prev,
            keyboardShortcuts: [...prev.keyboardShortcuts, shortcut]
        }));
    }, []);

    // Update accessibility preferences
    const updatePreferences = useCallback((updates: Partial<AccessibilityPreferences>) => {
        accessibilityManager.updatePreferences(updates);
        setState(prev => ({
            ...prev,
            preferences: { ...prev.preferences, ...updates }
        }));
    }, []);

    // Generate ARIA attributes helper
    const generateAriaAttributes = useCallback((
        element: {
            label?: string;
            description?: string;
            expanded?: boolean;
            selected?: boolean;
            disabled?: boolean;
            required?: boolean;
            invalid?: boolean;
            level?: number;
            setSize?: number;
            posInSet?: number;
        }
    ) => {
        const attributes: Record<string, string | boolean | number> = {};
        
        if (element.label) attributes['aria-label'] = element.label;
        if (element.description) attributes['aria-describedby'] = `desc-${Date.now()}`;
        if (element.expanded !== undefined) attributes['aria-expanded'] = element.expanded;
        if (element.selected !== undefined) attributes['aria-selected'] = element.selected;
        if (element.disabled) attributes['aria-disabled'] = true;
        if (element.required) attributes['aria-required'] = true;
        if (element.invalid) attributes['aria-invalid'] = true;
        if (element.level) attributes['aria-level'] = element.level;
        if (element.setSize) attributes['aria-setsize'] = element.setSize;
        if (element.posInSet) attributes['aria-posinset'] = element.posInSet;
        
        return attributes;
    }, []);

    // Check if element is accessible
    const checkAccessibility = useCallback((element: HTMLElement) => {
        const issues: string[] = [];
        
        // Check for missing labels
        if (['input', 'select', 'textarea'].includes(element.tagName.toLowerCase())) {
            const hasLabel = element.getAttribute('aria-label') || 
                           element.getAttribute('aria-labelledby') ||
                           document.querySelector(`label[for="${element.id}"]`);
            if (!hasLabel) {
                issues.push('Missing accessible label');
            }
        }
        
        // Check for insufficient color contrast (simplified check)
        const style = getComputedStyle(element);
        const backgroundColor = style.backgroundColor;
        const color = style.color;
        
        if (backgroundColor !== 'rgba(0, 0, 0, 0)' && color) {
            // This is a simplified check - in practice, you'd use a proper contrast ratio calculation
            issues.push('Consider checking color contrast ratio');
        }
        
        // Check for minimum touch target size
        const rect = element.getBoundingClientRect();
        if (rect.width < 44 || rect.height < 44) {
            if (element.tagName.toLowerCase() === 'button' || element.getAttribute('role') === 'button') {
                issues.push('Touch target may be too small (minimum 44x44px recommended)');
            }
        }
        
        return {
            isAccessible: issues.length === 0,
            issues
        };
    }, []);

    return {
        // State
        ...state,
        
        // Focus management
        focusFirst,
        focusLast,
        restoreFocus,
        setFocusTrap,
        getFocusableElements,
        
        // Announcements
        announce: announceToScreenReader,
        
        // Keyboard shortcuts
        addKeyboardShortcut,
        
        // Preferences
        updatePreferences,
        
        // Utilities
        generateAriaAttributes,
        checkAccessibility,
        
        // Convenience flags
        isKeyboardUser: state.preferences.keyboardOnly,
        needsHighContrast: state.preferences.highContrast,
        needsReducedMotion: state.preferences.reducedMotion,
        needsLargeText: state.preferences.largeText,
    };
};

export default useAccessibility;
