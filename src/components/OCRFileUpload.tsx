import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';

interface OCRFile {
  id: string;
  file: File;
  preview?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
}

interface OCRFileUploadProps {
  onFilesSelected: (files: OCRFile[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
}

const OCRFileUpload: React.FC<OCRFileUploadProps> = ({
  onFilesSelected,
  maxFiles = 10,
  maxSize = 50 * 1024 * 1024 // 50MB default
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<OCRFile[]>([]);

  // Supported file types for OCR
  const acceptedFileTypes = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/bmp': ['.bmp'],
    'image/tiff': ['.tiff', '.tif'],
    'image/webp': ['.webp'],
    'application/pdf': ['.pdf']
  };

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(rejection => ({
        file: rejection.file.name,
        errors: rejection.errors.map((e: any) => e.message).join(', ')
      }));
      console.warn('Rejected files:', errors);
    }

    // Process accepted files
    const newFiles: OCRFile[] = acceptedFiles.map(file => {
      const fileId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Create preview for images
      let preview: string | undefined;
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file);
      }

      return {
        id: fileId,
        file,
        preview,
        status: 'pending',
        progress: 0
      };
    });

    setUploadedFiles(prev => [...prev, ...newFiles]);
    onFilesSelected(newFiles);
  }, [onFilesSelected]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxFiles,
    maxSize,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === fileId);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  };

  // updateFileStatus function removed - not used in current implementation

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string): string => {
    if (fileType.startsWith('image/')) return '🖼️';
    if (fileType === 'application/pdf') return '📄';
    return '📁';
  };

  const getStatusColor = (status: OCRFile['status']): string => {
    switch (status) {
      case 'pending': return '#6b7280';
      case 'processing': return '#3b82f6';
      case 'completed': return '#10b981';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusText = (status: OCRFile['status']): string => {
    switch (status) {
      case 'pending': return 'Ready to process';
      case 'processing': return 'Processing...';
      case 'completed': return 'Completed';
      case 'error': return 'Error occurred';
      default: return 'Unknown';
    }
  };

  return (
    <div className="ocr-file-upload">
      {/* Dropzone Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${isDragActive && !isDragReject ? 'border-blue-400 bg-blue-50' : ''}
          ${isDragReject ? 'border-red-400 bg-red-50' : ''}
          ${!isDragActive ? 'border-gray-300 hover:border-gray-400' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-4">
          <div className="text-6xl">📁</div>
          
          {isDragActive ? (
            isDragReject ? (
              <div>
                <p className="text-lg font-medium text-red-600">
                  Some files are not supported
                </p>
                <p className="text-sm text-red-500">
                  Please upload JPG, PNG, BMP, TIFF, WebP, or PDF files
                </p>
              </div>
            ) : (
              <div>
                <p className="text-lg font-medium text-blue-600">
                  Drop files here to upload
                </p>
              </div>
            )
          ) : (
            <div>
              <p className="text-lg font-medium text-gray-700">
                Drag & drop files here, or click to select
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Supports: JPG, PNG, BMP, TIFF, WebP, PDF
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Max {maxFiles} files, {formatFileSize(maxSize)} per file
              </p>
            </div>
          )}
        </div>
      </div>

      {/* File List */}
      {uploadedFiles.length > 0 && (
        <div className="mt-6 space-y-3">
          <h3 className="text-lg font-medium text-gray-900">
            Uploaded Files ({uploadedFiles.length})
          </h3>
          
          <div className="space-y-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border"
              >
                {/* File Icon/Preview */}
                <div className="flex-shrink-0">
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.file.name}
                      className="w-12 h-12 object-cover rounded"
                    />
                  ) : (
                    <div className="w-12 h-12 flex items-center justify-center text-2xl">
                      {getFileIcon(file.file.type)}
                    </div>
                  )}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {file.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.file.size)} • {file.file.type}
                  </p>
                  <p className="text-xs" style={{ color: getStatusColor(file.status) }}>
                    {getStatusText(file.status)}
                    {file.error && ` - ${file.error}`}
                  </p>
                </div>

                {/* Progress Indicator */}
                <div className="flex-shrink-0">
                  {file.status === 'processing' ? (
                    <div className="w-8 h-8">
                      <CircularProgressbar
                        value={file.progress}
                        text={`${file.progress}%`}
                        styles={buildStyles({
                          textSize: '24px',
                          pathColor: '#3b82f6',
                          textColor: '#3b82f6',
                          trailColor: '#e5e7eb'
                        })}
                      />
                    </div>
                  ) : (
                    <div className="w-8 h-8 flex items-center justify-center">
                      {file.status === 'completed' && <span className="text-green-500 text-xl">✓</span>}
                      {file.status === 'error' && <span className="text-red-500 text-xl">✗</span>}
                      {file.status === 'pending' && <span className="text-gray-400 text-xl">⏳</span>}
                    </div>
                  )}
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => removeFile(file.id)}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors"
                  title="Remove file"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Processing Status */}
      {uploadedFiles.some(file => file.status === 'processing') && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <span className="text-blue-700 font-medium">Processing files...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default OCRFileUpload;
