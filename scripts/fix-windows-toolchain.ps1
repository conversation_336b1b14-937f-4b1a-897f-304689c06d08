# Windows Toolchain Fix Script
Write-Host "=== PrivacyAI Windows Toolchain Fix ===" -ForegroundColor Green

# Set execution policy for script
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force

# Switch to MSVC toolchain
Write-Host "`n1. Switching to MSVC toolchain..." -ForegroundColor Yellow
rustup toolchain install stable-x86_64-pc-windows-msvc
rustup default stable-x86_64-pc-windows-msvc

# Verify toolchain switch
Write-Host "`n2. Verifying toolchain switch..." -ForegroundColor Yellow
rustup show

# Update components
Write-Host "`n3. Updating Rust components..." -ForegroundColor Yellow
rustup update
rustup component add clippy rustfmt

# Test basic compilation
Write-Host "`n4. Testing Rust compilation..." -ForegroundColor Yellow
Push-Location "src-tauri"
try {
    cargo check --verbose
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Rust compilation working" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Rust compilation failed" -ForegroundColor Red
        exit 1
    }
} finally {
    Pop-Location
}

Write-Host "`n=== Toolchain Fix Complete ===" -ForegroundColor Green