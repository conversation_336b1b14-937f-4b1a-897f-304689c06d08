/// AI-Enhanced Context-Aware Detector
/// 
/// This module integrates AI-powered context analysis with traditional pattern recognition
/// to achieve superior accuracy and reduced false positives.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use serde::{Serialize, Deserialize};
use chrono::Utc;

use crate::privacy::context_aware_detector::{ContextAwareDetector, DocumentContext, Finding};
use crate::privacy::ai_context_analyzer::{AIContextAnalyzer, AIContextAnalysis};
use crate::privacy::comprehensive_error_reporting::{
    DetectionError, PerformanceMetric, GLOBAL_ERROR_REPORTER
};
use crate::privacy::balanced_error_reporting::{
    BalancedErrorReporter, LogLevel, ErrorReportingConfig
};
use crate::{report_error, report_warning, report_debug};

/// Enhanced detection results with AI insights
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AIEnhancedDetectionResult {
    /// Traditional detection findings
    pub traditional_findings: Vec<Finding>,
    /// AI context analysis results
    pub ai_analysis: AIContextAnalysis,
    /// Enhanced confidence scores
    pub enhanced_confidences: HashMap<String, f32>,
    /// AI-driven risk assessment
    pub ai_risk_assessment: AIRiskAssessment,
    /// Processing metadata
    pub processing_metadata: ProcessingMetadata,
}

/// AI-driven risk assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIRiskAssessment {
    /// Overall risk score (0.0 - 1.0)
    pub overall_risk_score: f32,
    /// Risk level description
    pub risk_level: String,
    /// Contributing risk factors
    pub risk_factors: Vec<RiskFactor>,
    /// Recommended actions
    pub recommended_actions: Vec<String>,
}

/// Individual risk factor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    /// Factor name
    pub factor: String,
    /// Contribution to overall risk
    pub contribution: f32,
    /// Confidence in this factor
    pub confidence: f32,
    /// Description
    pub description: String,
}

/// Processing metadata for AI-enhanced detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingMetadata {
    /// Total processing time
    pub total_processing_time_ms: u64,
    /// Traditional detection time
    pub traditional_detection_time_ms: u64,
    /// AI analysis time
    pub ai_analysis_time_ms: u64,
    /// Number of findings enhanced by AI
    pub ai_enhanced_findings: usize,
    /// AI model versions used
    pub model_versions: HashMap<String, String>,
    /// Processing timestamp
    pub timestamp: chrono::DateTime<Utc>,
}

/// AI-Enhanced Context-Aware Detector
pub struct AIEnhancedDetector {
    /// Traditional context-aware detector
    traditional_detector: Arc<ContextAwareDetector>,
    /// AI context analyzer
    ai_analyzer: Arc<AIContextAnalyzer>,
    /// Configuration settings
    config: AIEnhancedConfig,
    /// Balanced error reporter
    balanced_reporter: BalancedErrorReporter,
}

/// Configuration for AI-enhanced detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIEnhancedConfig {
    /// Enable AI enhancement
    pub enable_ai_enhancement: bool,
    /// AI confidence threshold
    pub ai_confidence_threshold: f32,
    /// Traditional confidence threshold
    pub traditional_confidence_threshold: f32,
    /// Combined confidence weight (AI vs traditional)
    pub ai_weight: f32,
    /// Enable risk assessment
    pub enable_risk_assessment: bool,
    /// Maximum processing time for AI analysis
    pub max_ai_processing_time_ms: u64,
}

impl AIEnhancedDetector {
    /// Create a new AI-enhanced detector
    pub fn new() -> Self {
        report_debug!("Initializing AI-Enhanced Context-Aware Detector".to_string());
        
        let config = AIEnhancedConfig {
            enable_ai_enhancement: true,
            ai_confidence_threshold: 0.6,
            traditional_confidence_threshold: 0.7,
            ai_weight: 0.4, // 40% AI, 60% traditional
            enable_risk_assessment: true,
            max_ai_processing_time_ms: 5000,
        };
        
        Self {
            traditional_detector: Arc::new(ContextAwareDetector::new()),
            ai_analyzer: Arc::new(AIContextAnalyzer::new()),
            config,
            balanced_reporter: BalancedErrorReporter::new(ErrorReportingConfig::default()),
        }
    }
    
    /// Perform AI-enhanced detection
    pub async fn detect_with_ai_enhancement(
        &mut self,
        text: &str,
        document_context: &DocumentContext,
    ) -> Result<AIEnhancedDetectionResult, Box<dyn std::error::Error + Send + Sync>> {
        let start_time = Instant::now();

        // Start balanced reporting
        self.balanced_reporter.start_operation("ai_enhanced_detection", 1);
        self.balanced_reporter.log(LogLevel::Info,
            &format!("Starting AI-enhanced detection ({} chars)", text.len()),
            "ai_enhanced_detector");

        report_debug!(format!("Starting AI-enhanced detection on {} characters", text.len()));
        
        // Perform traditional detection
        let traditional_start = Instant::now();
        let traditional_findings = self.traditional_detector
            .detect_with_context(text, document_context)
            .await?;
        let traditional_time = traditional_start.elapsed().as_millis() as u64;
        
        // Log traditional detection results
        self.balanced_reporter.log_performance("traditional_detection", traditional_time, "context_aware_detector");
        self.balanced_reporter.log(LogLevel::Info,
            &format!("Traditional detection: {} findings", traditional_findings.len()),
            "context_aware_detector");

        report_debug!(format!("Traditional detection completed: {} findings in {}ms",
                             traditional_findings.len(), traditional_time));
        
        // Perform AI analysis if enabled
        let (ai_analysis, ai_time) = if self.config.enable_ai_enhancement {
            let ai_start = Instant::now();
            
            match self.ai_analyzer.analyze_context(text, document_context.document_type.as_deref()).await {
                Ok(analysis) => {
                    let ai_elapsed = ai_start.elapsed().as_millis() as u64;

                    // Log AI analysis results with aggregation
                    self.balanced_reporter.log_performance("ai_analysis", ai_elapsed, "ai_context_analyzer");
                    self.balanced_reporter.log(LogLevel::Info,
                        &format!("AI analysis: {} (confidence: {:.2})",
                                analysis.predicted_document_type, analysis.prediction_confidence),
                        "ai_context_analyzer");

                    // Log detailed scores in debug mode
                    if !analysis.document_type_scores.is_empty() {
                        self.balanced_reporter.log_aggregated(LogLevel::Debug,
                            "document_classification", &analysis.document_type_scores, "ai_classifier");
                    }

                    report_debug!(format!("AI analysis completed in {}ms", ai_elapsed));
                    (analysis, ai_elapsed)
                },
                Err(e) => {
                    report_error!(DetectionError::IntegrationError {
                        source_component: "AIEnhancedDetector".to_string(),
                        target_component: "AIContextAnalyzer".to_string(),
                        operation: "analyze_context".to_string(),
                        error_details: e.to_string(),
                        timestamp: Utc::now(),
                    });
                    
                    // Fallback to empty analysis
                    (AIContextAnalysis {
                        document_type_scores: HashMap::new(),
                        predicted_document_type: "unknown".to_string(),
                        prediction_confidence: 0.0,
                        semantic_indicators: Vec::new(),
                        ai_risk_score: 0.0,
                        analysis_metadata: crate::privacy::ai_context_analyzer::AnalysisMetadata {
                            timestamp: Utc::now(),
                            processing_time_ms: 0,
                            model_version: "fallback".to_string(),
                            input_length: text.len(),
                            features_extracted: 0,
                        },
                    }, 0)
                }
            }
        } else {
            report_debug!("AI enhancement disabled, using traditional detection only".to_string());
            (AIContextAnalysis {
                document_type_scores: HashMap::new(),
                predicted_document_type: document_context.document_type.clone().unwrap_or("unknown".to_string()),
                prediction_confidence: 0.0,
                semantic_indicators: Vec::new(),
                ai_risk_score: 0.0,
                analysis_metadata: crate::privacy::ai_context_analyzer::AnalysisMetadata {
                    timestamp: Utc::now(),
                    processing_time_ms: 0,
                    model_version: "disabled".to_string(),
                    input_length: text.len(),
                    features_extracted: 0,
                },
            }, 0)
        };
        
        // Enhance confidence scores using AI insights
        let enhanced_confidences = self.enhance_confidence_scores(&traditional_findings, &ai_analysis);
        
        // Perform AI-driven risk assessment
        let ai_risk_assessment = if self.config.enable_risk_assessment {
            self.assess_ai_risk(&traditional_findings, &ai_analysis).await?
        } else {
            AIRiskAssessment {
                overall_risk_score: 0.0,
                risk_level: "Not Assessed".to_string(),
                risk_factors: Vec::new(),
                recommended_actions: Vec::new(),
            }
        };
        
        let total_time = start_time.elapsed().as_millis() as u64;
        
        // Record performance metrics
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            reporter.record_performance(PerformanceMetric {
                operation: "ai_enhanced_detection".to_string(),
                duration_ms: total_time,
                memory_usage_mb: None,
                input_size: text.len(),
                output_size: traditional_findings.len(),
                timestamp: Utc::now(),
            });
        }
        
        let mut model_versions = HashMap::new();
        model_versions.insert("ai_context_analyzer".to_string(), ai_analysis.analysis_metadata.model_version.clone());
        model_versions.insert("traditional_detector".to_string(), "v1.0.0".to_string());
        
        let result = AIEnhancedDetectionResult {
            traditional_findings,
            ai_analysis,
            enhanced_confidences,
            ai_risk_assessment,
            processing_metadata: ProcessingMetadata {
                total_processing_time_ms: total_time,
                traditional_detection_time_ms: traditional_time,
                ai_analysis_time_ms: ai_time,
                ai_enhanced_findings: 0, // Will be calculated
                model_versions,
                timestamp: Utc::now(),
            },
        };
        
        // Final balanced reporting
        self.balanced_reporter.log_performance("total_detection", total_time, "ai_enhanced_detector");
        self.balanced_reporter.log(LogLevel::Info,
            &format!("Detection completed: {} findings, risk: {}, time: {}ms",
                    result.traditional_findings.len(),
                    result.ai_risk_assessment.risk_level,
                    total_time),
            "ai_enhanced_detector");

        // End operation and get aggregated report
        let _aggregated_report = self.balanced_reporter.end_operation();

        report_debug!(format!("AI-enhanced detection completed in {}ms (traditional: {}ms, AI: {}ms)",
                             total_time, traditional_time, ai_time));

        Ok(result)
    }
    
    /// Enhance confidence scores using AI insights
    fn enhance_confidence_scores(
        &self,
        findings: &[Finding],
        ai_analysis: &AIContextAnalysis,
    ) -> HashMap<String, f32> {
        let mut enhanced_confidences = HashMap::new();
        
        for finding in findings {
            let traditional_confidence = finding.confidence;
            
            // Get AI confidence boost based on semantic indicators
            let ai_boost = self.calculate_ai_confidence_boost(finding, ai_analysis);
            
            // Combine traditional and AI confidences
            let enhanced_confidence = (traditional_confidence * (1.0 - self.config.ai_weight)) + 
                                    (ai_boost * self.config.ai_weight);
            
            enhanced_confidences.insert(finding.content.clone(), enhanced_confidence.clamp(0.0, 1.0));
            
            report_debug!(format!("Enhanced confidence for '{}': {:.2} -> {:.2} (AI boost: {:.2})", 
                                 finding.content, traditional_confidence, enhanced_confidence, ai_boost));
        }
        
        enhanced_confidences
    }
    
    /// Calculate AI confidence boost for a finding
    fn calculate_ai_confidence_boost(
        &self,
        finding: &Finding,
        ai_analysis: &AIContextAnalysis,
    ) -> f32 {
        let mut boost: f32 = 0.5; // Base AI confidence
        
        // Boost based on document type prediction
        if ai_analysis.prediction_confidence > self.config.ai_confidence_threshold {
            match ai_analysis.predicted_document_type.as_str() {
                "employment" | "financial" | "medical" => boost += 0.3,
                "customer_service" => boost -= 0.4, // Reduce confidence for customer service
                _ => {}
            }
        }
        
        // Boost based on semantic indicators
        for indicator in &ai_analysis.semantic_indicators {
            if indicator.confidence > 0.7 {
                match indicator.indicator_type.as_str() {
                    "privacy_sensitive" => boost += 0.2,
                    "financial_context" => boost += 0.15,
                    "employment_context" => boost += 0.1,
                    "customer_service_context" => boost -= 0.3,
                    _ => {}
                }
            }
        }
        
        boost.clamp(0.0, 1.0)
    }
    
    /// Perform AI-driven risk assessment
    async fn assess_ai_risk(
        &self,
        findings: &[Finding],
        ai_analysis: &AIContextAnalysis,
    ) -> Result<AIRiskAssessment, Box<dyn std::error::Error + Send + Sync>> {
        let mut risk_factors = Vec::new();
        let mut overall_risk = ai_analysis.ai_risk_score;
        
        // Add risk factors based on findings
        if !findings.is_empty() {
            risk_factors.push(RiskFactor {
                factor: "sensitive_data_detected".to_string(),
                contribution: 0.4,
                confidence: 0.9,
                description: format!("{} sensitive data items detected", findings.len()),
            });
            overall_risk += 0.4;
        }
        
        // Add risk factors based on AI analysis
        if ai_analysis.prediction_confidence > 0.8 {
            match ai_analysis.predicted_document_type.as_str() {
                "financial" | "medical" => {
                    risk_factors.push(RiskFactor {
                        factor: "high_sensitivity_document".to_string(),
                        contribution: 0.3,
                        confidence: ai_analysis.prediction_confidence,
                        description: format!("Document classified as {}", ai_analysis.predicted_document_type),
                    });
                    overall_risk += 0.3;
                },
                _ => {}
            }
        }
        
        overall_risk = overall_risk.clamp(0.0, 1.0);
        
        // Determine risk level and recommendations
        let (risk_level, recommended_actions) = self.determine_risk_level_and_actions(overall_risk, &risk_factors);
        
        Ok(AIRiskAssessment {
            overall_risk_score: overall_risk,
            risk_level,
            risk_factors,
            recommended_actions,
        })
    }
    
    /// Determine risk level and recommended actions
    fn determine_risk_level_and_actions(&self, risk_score: f32, _risk_factors: &[RiskFactor]) -> (String, Vec<String>) {
        let risk_level = if risk_score >= 0.9 {
            "Critical"
        } else if risk_score >= 0.7 {
            "High"
        } else if risk_score >= 0.5 {
            "Medium"
        } else if risk_score >= 0.3 {
            "Low"
        } else {
            "Minimal"
        };
        
        let mut actions = Vec::new();
        
        match risk_level {
            "Critical" => {
                actions.push("Immediate review required".to_string());
                actions.push("Restrict document access".to_string());
                actions.push("Notify security team".to_string());
            },
            "High" => {
                actions.push("Review within 24 hours".to_string());
                actions.push("Apply additional security measures".to_string());
            },
            "Medium" => {
                actions.push("Review within 72 hours".to_string());
                actions.push("Monitor for additional risks".to_string());
            },
            "Low" => {
                actions.push("Routine monitoring sufficient".to_string());
            },
            _ => {
                actions.push("No immediate action required".to_string());
            }
        }
        
        (risk_level.to_string(), actions)
    }
}

impl Default for AIEnhancedDetector {
    fn default() -> Self {
        Self::new()
    }
}
