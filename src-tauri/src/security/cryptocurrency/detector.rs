/// Cryptocurrency detector implementation
/// 
/// This module provides the main cryptocurrency detection functionality,
/// combining pattern matching with validation to identify cryptocurrency-related
/// sensitive data with high accuracy and low false positive rates.

use regex::Regex;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use std::time::Instant;
use thiserror::Error;

use super::patterns::{CRYPTO_PATTERNS, CryptoPatternType, CryptoSeverity};
use super::validation::{CryptocurrencyValidator, CryptoValidationResult};

/// Cryptocurrency detection errors
#[derive(Debug, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum CryptocurrencyDetectionError {
    #[error("Regex compilation failed: {message}")]
    RegexError { message: String },
    
    #[error("Validation failed: {message}")]
    ValidationError { message: String },
    
    #[error("Processing timeout")]
    Timeout,
}

/// Types of cryptocurrency data
#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, <PERSON>q, Serialize, Deserialize)]
pub enum CryptocurrencyType {
    PrivateKey,
    Address,
    ExchangeCredential,
    Unknown,
}

/// Cryptocurrency finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CryptocurrencyFinding {
    pub finding_type: CryptocurrencyType,
    pub pattern_type: CryptoPatternType,
    pub matched_text: String,
    pub start_position: usize,
    pub end_position: usize,
    pub severity: CryptoSeverity,
    pub confidence: f32,
    pub validation_result: Option<CryptoValidationResult>,
    pub description: String,
}

/// Cryptocurrency detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CryptocurrencyDetectionResult {
    pub findings: Vec<CryptocurrencyFinding>,
    pub total_findings: usize,
    pub critical_findings: usize,
    pub high_findings: usize,
    pub processing_time_ms: u64,
    pub validation_time_ms: u64,
}

/// Main cryptocurrency detector
pub struct CryptocurrencyDetector {
    patterns: HashMap<CryptoPatternType, Regex>,
    validator: CryptocurrencyValidator,
    enable_validation: bool,
}

impl CryptocurrencyDetector {
    /// Create a new cryptocurrency detector
    pub fn new() -> Result<Self, CryptocurrencyDetectionError> {
        let mut patterns = HashMap::new();
        
        // Compile all regex patterns
        for pattern in CRYPTO_PATTERNS {
            match Regex::new(&pattern.regex) {
                Ok(regex) => {
                    patterns.insert(pattern.pattern_type.clone(), regex);
                }
                Err(e) => {
                    return Err(CryptocurrencyDetectionError::RegexError {
                        message: format!("Failed to compile pattern {:?}: {}", pattern.pattern_type, e),
                    });
                }
            }
        }
        
        Ok(Self {
            patterns,
            validator: CryptocurrencyValidator::new(),
            enable_validation: true,
        })
    }
    
    /// Create detector with validation disabled (for performance)
    pub fn new_fast() -> Result<Self, CryptocurrencyDetectionError> {
        let mut detector = Self::new()?;
        detector.enable_validation = false;
        Ok(detector)
    }
    
    /// Detect cryptocurrency data in text
    pub fn detect(&self, text: &str) -> CryptocurrencyDetectionResult {
        let start_time = Instant::now();
        let mut findings = Vec::new();
        
        // Search for all patterns
        for pattern in CRYPTO_PATTERNS {
            if let Some(regex) = self.patterns.get(&pattern.pattern_type) {
                for mat in regex.find_iter(text) {
                    let matched_text = mat.as_str().to_string();
                    let start_pos = mat.start();
                    let end_pos = mat.end();
                    
                    let finding = self.create_finding(
                        pattern,
                        matched_text,
                        start_pos,
                        end_pos,
                    );
                    
                    findings.push(finding);
                }
            }
        }
        
        let detection_time = start_time.elapsed().as_millis() as u64;
        
        // Perform validation if enabled
        let validation_start = Instant::now();
        if self.enable_validation {
            self.validate_findings(&mut findings);
        }
        let validation_time = validation_start.elapsed().as_millis() as u64;
        
        // Count findings by severity
        let critical_findings = findings.iter()
            .filter(|f| f.severity == CryptoSeverity::Critical)
            .count();
        let high_findings = findings.iter()
            .filter(|f| f.severity == CryptoSeverity::High)
            .count();
        
        CryptocurrencyDetectionResult {
            total_findings: findings.len(),
            critical_findings,
            high_findings,
            findings,
            processing_time_ms: detection_time,
            validation_time_ms: validation_time,
        }
    }
    
    /// Detect only private keys (high-priority scan)
    pub fn detect_private_keys(&self, text: &str) -> CryptocurrencyDetectionResult {
        let start_time = Instant::now();
        let mut findings = Vec::new();
        
        // Only scan for private key patterns
        let private_key_types = [
            CryptoPatternType::BitcoinWIF,
            CryptoPatternType::BitcoinWIFCompressed,
            CryptoPatternType::EthereumPrivateKey,
            CryptoPatternType::RawPrivateKey,
            CryptoPatternType::Base58PrivateKey,
        ];
        
        for pattern in CRYPTO_PATTERNS {
            if private_key_types.contains(&pattern.pattern_type) {
                if let Some(regex) = self.patterns.get(&pattern.pattern_type) {
                    for mat in regex.find_iter(text) {
                        let matched_text = mat.as_str().to_string();
                        let start_pos = mat.start();
                        let end_pos = mat.end();
                        
                        let finding = self.create_finding(
                            pattern,
                            matched_text,
                            start_pos,
                            end_pos,
                        );
                        
                        findings.push(finding);
                    }
                }
            }
        }
        
        let detection_time = start_time.elapsed().as_millis() as u64;
        
        // Always validate private keys
        let validation_start = Instant::now();
        self.validate_findings(&mut findings);
        let validation_time = validation_start.elapsed().as_millis() as u64;
        
        CryptocurrencyDetectionResult {
            total_findings: findings.len(),
            critical_findings: findings.len(), // All private keys are critical
            high_findings: 0,
            findings,
            processing_time_ms: detection_time,
            validation_time_ms: validation_time,
        }
    }
    
    /// Create a cryptocurrency finding
    fn create_finding(
        &self,
        pattern: &super::patterns::CryptoPattern,
        matched_text: String,
        start_position: usize,
        end_position: usize,
    ) -> CryptocurrencyFinding {
        let finding_type = self.classify_finding_type(&pattern.pattern_type);
        
        CryptocurrencyFinding {
            finding_type,
            pattern_type: pattern.pattern_type.clone(),
            matched_text,
            start_position,
            end_position,
            severity: pattern.severity.clone(),
            confidence: 0.8, // Initial confidence, will be updated by validation
            validation_result: None,
            description: pattern.description.to_string(),
        }
    }
    
    /// Classify the type of cryptocurrency finding
    fn classify_finding_type(&self, pattern_type: &CryptoPatternType) -> CryptocurrencyType {
        match pattern_type {
            CryptoPatternType::BitcoinWIF |
            CryptoPatternType::BitcoinWIFCompressed |
            CryptoPatternType::EthereumPrivateKey |
            CryptoPatternType::RawPrivateKey |
            CryptoPatternType::Base58PrivateKey => CryptocurrencyType::PrivateKey,
            
            CryptoPatternType::BitcoinLegacy |
            CryptoPatternType::BitcoinSegWit |
            CryptoPatternType::BitcoinBech32 |
            CryptoPatternType::EthereumAddress |
            CryptoPatternType::MoneroAddress |
            CryptoPatternType::LitecoinAddress |
            CryptoPatternType::CardanoAddress => CryptocurrencyType::Address,
            
            CryptoPatternType::BinanceApiKey |
            CryptoPatternType::BinanceSecret |
            CryptoPatternType::CoinbaseApiKey |
            CryptoPatternType::CoinbaseSecret |
            CryptoPatternType::KrakenApiKey |
            CryptoPatternType::KrakenSecret |
            CryptoPatternType::BitfinexApiKey |
            CryptoPatternType::GenericApiKey |
            CryptoPatternType::GenericApiSecret => CryptocurrencyType::ExchangeCredential,

            CryptoPatternType::WorldMobileToken |
            CryptoPatternType::AdaHandle |
            CryptoPatternType::UnstoppableDomains => CryptocurrencyType::Address,
        }
    }
    
    /// Validate findings to reduce false positives
    fn validate_findings(&self, findings: &mut Vec<CryptocurrencyFinding>) {
        for finding in findings.iter_mut() {
            // Get the pattern to check if validation is required
            if let Some(pattern) = CRYPTO_PATTERNS.iter()
                .find(|p| p.pattern_type == finding.pattern_type) {
                
                if pattern.requires_validation {
                    let validation_result = self.validator.validate(
                        &finding.matched_text,
                        finding.pattern_type.clone(),
                    );
                    
                    // Update confidence based on validation
                    if validation_result.is_valid {
                        finding.confidence = validation_result.confidence;
                    } else {
                        finding.confidence = 0.2; // Low confidence for invalid matches
                    }
                    
                    finding.validation_result = Some(validation_result);
                }
            }
        }
        
        // Sort findings by confidence (highest first)
        findings.sort_by(|a, b| b.confidence.partial_cmp(&a.confidence).unwrap_or(std::cmp::Ordering::Equal));
    }
    
    /// Get detection statistics
    pub fn get_pattern_count(&self) -> usize {
        self.patterns.len()
    }
    
    /// Check if validation is enabled
    pub fn is_validation_enabled(&self) -> bool {
        self.enable_validation
    }
    
    /// Enable or disable validation
    pub fn set_validation_enabled(&mut self, enabled: bool) {
        self.enable_validation = enabled;
    }
}

impl Default for CryptocurrencyDetector {
    fn default() -> Self {
        Self::new().expect("Failed to create default cryptocurrency detector")
    }
}
