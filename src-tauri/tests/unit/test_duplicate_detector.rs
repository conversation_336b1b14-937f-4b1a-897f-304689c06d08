use tauri_appprivacy_ai_lib::core::{DuplicateDetector, DuplicateDetectionOptions, DuplicateDetectionResult};
use tempfile::TempDir;
use std::fs;

#[tokio::test]
async fn test_duplicate_detector_creation() {
    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options);
    assert!(detector.is_ok());
}

#[tokio::test]
async fn test_duplicate_detector_custom_options() {
    let options = DuplicateDetectionOptions {
        hash_algorithm: "sha256".to_string(),
        min_file_size: 1024,
        max_file_size: 100 * 1024 * 1024,
        include_hidden_files: false,
        file_extensions: vec!["txt".to_string(), "jpg".to_string()],
        use_perceptual_hash: true,
    };

    let detector = DuplicateDetector::new(options);
    assert!(detector.is_ok());
}

#[tokio::test]
async fn test_duplicate_detection_empty_directory() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 0);
    assert_eq!(result.total_duplicates, 0);
    assert_eq!(result.total_files_scanned, 0);
    assert!(result.processing_time_ms > 0);
}

#[tokio::test]
async fn test_duplicate_detection_single_file() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    fs::write(temp_path.join("test.txt"), "test content").unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 0);
    assert_eq!(result.total_duplicates, 0);
    assert_eq!(result.total_files_scanned, 1);
}

#[tokio::test]
async fn test_duplicate_detection_identical_files() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    let content = "This is identical content for testing duplicates";
    fs::write(temp_path.join("file1.txt"), content).unwrap();
    fs::write(temp_path.join("file2.txt"), content).unwrap();
    fs::write(temp_path.join("file3.txt"), content).unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 1);
    assert_eq!(result.total_duplicates, 3);
    assert_eq!(result.total_files_scanned, 3);

    let group = &result.duplicate_groups[0];
    assert_eq!(group.files.len(), 3);
    assert!(group.total_size > 0);
}

#[tokio::test]
async fn test_duplicate_detection_different_files() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    fs::write(temp_path.join("file1.txt"), "Content 1").unwrap();
    fs::write(temp_path.join("file2.txt"), "Content 2").unwrap();
    fs::write(temp_path.join("file3.txt"), "Content 3").unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 0);
    assert_eq!(result.total_duplicates, 0);
    assert_eq!(result.total_files_scanned, 3);
}

#[tokio::test]
async fn test_duplicate_detection_mixed_scenario() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create some duplicate files
    let duplicate_content = "Duplicate content";
    fs::write(temp_path.join("dup1.txt"), duplicate_content).unwrap();
    fs::write(temp_path.join("dup2.txt"), duplicate_content).unwrap();

    // Create some unique files
    fs::write(temp_path.join("unique1.txt"), "Unique content 1").unwrap();
    fs::write(temp_path.join("unique2.txt"), "Unique content 2").unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 1);
    assert_eq!(result.total_duplicates, 2);
    assert_eq!(result.total_files_scanned, 4);
}

#[tokio::test]
async fn test_duplicate_detection_file_size_filtering() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create files of different sizes
    fs::write(temp_path.join("small.txt"), "small").unwrap(); // 5 bytes
    fs::write(temp_path.join("large.txt"), "a".repeat(2000)).unwrap(); // 2000 bytes

    let mut options = DuplicateDetectionOptions::default();
    options.min_file_size = 100; // Only files >= 100 bytes
    options.max_file_size = 1500; // Only files <= 1500 bytes

    let detector = DuplicateDetector::new(options).unwrap();
    let result = detector.detect_duplicates(temp_path).unwrap();

    // Should only scan the large file (but it's > 1500 bytes, so actually none)
    // Or adjust the test based on actual implementation
    assert!(result.total_files_scanned <= 2);
}

#[tokio::test]
async fn test_duplicate_detection_file_extension_filtering() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    fs::write(temp_path.join("file1.txt"), "content").unwrap();
    fs::write(temp_path.join("file2.jpg"), "content").unwrap();
    fs::write(temp_path.join("file3.pdf"), "content").unwrap();

    let mut options = DuplicateDetectionOptions::default();
    options.file_extensions = vec!["txt".to_string(), "jpg".to_string()];

    let detector = DuplicateDetector::new(options).unwrap();
    let result = detector.detect_duplicates(temp_path).unwrap();

    // Should only scan .txt and .jpg files
    assert_eq!(result.total_files_scanned, 2);
}

#[tokio::test]
async fn test_duplicate_detection_hidden_files() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    fs::write(temp_path.join("visible.txt"), "content").unwrap();
    fs::write(temp_path.join(".hidden.txt"), "content").unwrap();

    // Test with hidden files excluded (default)
    let mut options = DuplicateDetectionOptions::default();
    options.include_hidden_files = false;

    let detector = DuplicateDetector::new(options).unwrap();
    let result = detector.detect_duplicates(temp_path).unwrap();

    // Should only find the visible file
    assert_eq!(result.total_files_scanned, 1);

    // Test with hidden files included
    let mut options = DuplicateDetectionOptions::default();
    options.include_hidden_files = true;

    let detector = DuplicateDetector::new(options).unwrap();
    let result = detector.detect_duplicates(temp_path).unwrap();

    // Should find both files and detect them as duplicates
    assert_eq!(result.total_files_scanned, 2);
    assert_eq!(result.total_duplicates, 2);
}

#[tokio::test]
async fn test_duplicate_detection_subdirectories() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create subdirectories
    let subdir1 = temp_path.join("subdir1");
    let subdir2 = temp_path.join("subdir2");
    fs::create_dir(&subdir1).unwrap();
    fs::create_dir(&subdir2).unwrap();

    let content = "duplicate content across directories";
    fs::write(temp_path.join("root.txt"), content).unwrap();
    fs::write(subdir1.join("sub1.txt"), content).unwrap();
    fs::write(subdir2.join("sub2.txt"), content).unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 1);
    assert_eq!(result.total_duplicates, 3);
    assert_eq!(result.total_files_scanned, 3);
}

#[tokio::test]
async fn test_duplicate_detection_performance_tracking() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create several files for performance testing
    for i in 0..10 {
        fs::write(temp_path.join(format!("file_{}.txt", i)), format!("content {}", i)).unwrap();
    }

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let start_time = std::time::Instant::now();
    let result = detector.detect_duplicates(temp_path).unwrap();
    let actual_time = start_time.elapsed().as_millis() as u64;

    assert!(result.processing_time_ms > 0);
    assert!(result.processing_time_ms <= actual_time + 100); // Allow some margin
    assert_eq!(result.total_files_scanned, 10);
}

#[tokio::test]
async fn test_duplicate_detection_hash_algorithm() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    let content = "test content for hash algorithm";
    fs::write(temp_path.join("file1.txt"), content).unwrap();
    fs::write(temp_path.join("file2.txt"), content).unwrap();

    // Test with different hash algorithms
    let algorithms = vec!["md5", "sha1", "sha256"];

    for algorithm in algorithms {
        let mut options = DuplicateDetectionOptions::default();
        options.hash_algorithm = algorithm.to_string();

        let detector = DuplicateDetector::new(options).unwrap();
        let result = detector.detect_duplicates(temp_path).unwrap();

        assert_eq!(result.duplicate_groups.len(), 1);
        assert_eq!(result.total_duplicates, 2);
    }
}

#[tokio::test]
async fn test_duplicate_detection_perceptual_hash() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create fake image files (in real implementation, these would be actual images)
    fs::write(temp_path.join("image1.jpg"), "fake image data").unwrap();
    fs::write(temp_path.join("image2.jpg"), "fake image data").unwrap();

    let mut options = DuplicateDetectionOptions::default();
    options.use_perceptual_hash = true;
    options.file_extensions = vec!["jpg".to_string()];

    let detector = DuplicateDetector::new(options).unwrap();
    let result = detector.detect_duplicates(temp_path).unwrap();

    // Should detect duplicates using perceptual hashing
    assert_eq!(result.total_files_scanned, 2);
}

#[tokio::test]
async fn test_duplicate_detection_error_handling() {
    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    // Test with non-existent directory
    let result = detector.detect_duplicates("nonexistent_directory");
    assert!(result.is_err());
}

#[tokio::test]
async fn test_duplicate_detection_large_files() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    // Create large files (simulate with smaller content for testing)
    let large_content = "x".repeat(10000);
    fs::write(temp_path.join("large1.txt"), &large_content).unwrap();
    fs::write(temp_path.join("large2.txt"), &large_content).unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    assert_eq!(result.duplicate_groups.len(), 1);
    assert_eq!(result.total_duplicates, 2);

    let group = &result.duplicate_groups[0];
    assert!(group.total_size >= 20000); // 2 files * 10000 bytes each
}

#[tokio::test]
async fn test_duplicate_detection_result_structure() {
    let temp_dir = TempDir::new().unwrap();
    let temp_path = temp_dir.path();

    let content = "test content";
    fs::write(temp_path.join("file1.txt"), content).unwrap();
    fs::write(temp_path.join("file2.txt"), content).unwrap();

    let options = DuplicateDetectionOptions::default();
    let detector = DuplicateDetector::new(options).unwrap();

    let result = detector.detect_duplicates(temp_path).unwrap();

    // Verify result structure
    assert_eq!(result.duplicate_groups.len(), 1);

    let group = &result.duplicate_groups[0];
    assert_eq!(group.files.len(), 2);
    assert!(!group.hash.is_empty());
    assert!(group.total_size > 0);

    for file_info in &group.files {
        assert!(!file_info.path.is_empty());
        assert!(file_info.size > 0);
        assert!(file_info.modified_time > 0);
    }
}
