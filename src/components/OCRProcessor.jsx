/**
 * OCR Processor Component
 * 
 * Provides a complete OCR interface with drag-and-drop file upload,
 * real-time processing status, and integration with the AI-enhanced
 * privacy detection system.
 */

import React, { useState, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { tesseractEngine, OCRUtils } from '../lib/ocr/tesseract-integration.js';

const OCRProcessor = () => {
    const [isProcessing, setIsProcessing] = useState(false);
    const [ocrResults, setOcrResults] = useState(null);
    const [error, setError] = useState(null);
    const [dragActive, setDragActive] = useState(false);
    const [processingProgress, setProcessingProgress] = useState(0);
    const [performanceStats, setPerformanceStats] = useState(null);
    const fileInputRef = useRef(null);

    // Handle file selection
    const handleFileSelect = useCallback(async (files) => {
        if (!files || files.length === 0) return;

        const file = files[0];
        
        try {
            // Validate file
            OCRUtils.validateImageFile(file);
            
            setError(null);
            setIsProcessing(true);
            setProcessingProgress(10);

            // Convert file to base64
            const base64Data = await OCRUtils.fileToBase64(file);
            const imageFormat = OCRUtils.getImageFormat(file);
            
            setProcessingProgress(30);

            // Process with OCR engine
            const result = await tesseractEngine.processOCR(base64Data, {
                format: imageFormat,
                language: 'eng',
                preprocess: true,
                confidence_threshold: 0.6
            });

            setProcessingProgress(80);

            if (result.success) {
                setOcrResults(result);
                
                // Get performance stats
                const stats = tesseractEngine.getPerformanceStats();
                setPerformanceStats(stats);

                // If text was extracted, run privacy detection
                if (result.text && result.text.trim().length > 0) {
                    await runPrivacyDetection(result.text);
                }
            } else {
                setError(result.error || 'OCR processing failed');
            }

            setProcessingProgress(100);
            
        } catch (err) {
            console.error('OCR processing error:', err);
            setError(err.message);
        } finally {
            setIsProcessing(false);
            setTimeout(() => setProcessingProgress(0), 1000);
        }
    }, []);

    // Run privacy detection on extracted text
    const runPrivacyDetection = async (text) => {
        try {
            console.log('🔍 Running privacy detection on extracted text...');
            
            // This would integrate with the existing privacy detection system
            // For now, we'll just log the text length
            console.log(`📝 Extracted text: ${text.length} characters`);
            
            // TODO: Integrate with AI-enhanced privacy detection
            // const privacyResults = await invoke('scan_text_for_privacy', { text });
            
        } catch (err) {
            console.warn('Privacy detection failed:', err);
        }
    };

    // Handle drag and drop
    const handleDrag = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            setDragActive(true);
        } else if (e.type === "dragleave") {
            setDragActive(false);
        }
    }, []);

    const handleDrop = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
        
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files);
        }
    }, [handleFileSelect]);

    // Handle file input change
    const handleInputChange = useCallback((e) => {
        if (e.target.files && e.target.files[0]) {
            handleFileSelect(e.target.files);
        }
    }, [handleFileSelect]);

    // Run performance tests
    const runPerformanceTests = async () => {
        try {
            setIsProcessing(true);
            console.log('🧪 Running OCR performance tests...');
            
            const testResults = await invoke('run_ocr_performance_tests');
            
            console.log('📊 Performance test results:', testResults);
            alert(`Performance Tests Completed!\n\nTests: ${testResults.total_tests}\nPassed: ${testResults.passed_tests}\nAverage Time: ${testResults.average_processing_time_ms}ms\nAccuracy: ${(testResults.average_accuracy * 100).toFixed(1)}%`);
            
        } catch (err) {
            console.error('Performance tests failed:', err);
            setError('Performance tests failed: ' + err);
        } finally {
            setIsProcessing(false);
        }
    };

    return (
        <div className="ocr-processor">
            <div className="ocr-header">
                <h2>📖 OCR Text Extraction</h2>
                <p>Upload an image to extract text using AI-powered OCR</p>
            </div>

            {/* File Upload Area */}
            <div 
                className={`upload-area ${dragActive ? 'drag-active' : ''} ${isProcessing ? 'processing' : ''}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
            >
                <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleInputChange}
                    style={{ display: 'none' }}
                />
                
                {isProcessing ? (
                    <div className="processing-indicator">
                        <div className="spinner"></div>
                        <p>Processing image...</p>
                        {processingProgress > 0 && (
                            <div className="progress-bar">
                                <div 
                                    className="progress-fill" 
                                    style={{ width: `${processingProgress}%` }}
                                ></div>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="upload-prompt">
                        <div className="upload-icon">📁</div>
                        <p>Drop an image here or click to select</p>
                        <small>Supports: JPG, PNG, WebP, BMP, TIFF (max 10MB)</small>
                    </div>
                )}
            </div>

            {/* Error Display */}
            {error && (
                <div className="error-message">
                    <span className="error-icon">❌</span>
                    <span>{error}</span>
                </div>
            )}

            {/* OCR Results */}
            {ocrResults && (
                <div className="ocr-results">
                    <h3>📝 Extracted Text</h3>
                    
                    <div className="results-stats">
                        <div className="stat">
                            <span className="stat-label">Confidence:</span>
                            <span className="stat-value">{(ocrResults.confidence * 100).toFixed(1)}%</span>
                        </div>
                        <div className="stat">
                            <span className="stat-label">Words:</span>
                            <span className="stat-value">{ocrResults.word_count}</span>
                        </div>
                        <div className="stat">
                            <span className="stat-label">Processing Time:</span>
                            <span className="stat-value">{ocrResults.processing_time_ms}ms</span>
                        </div>
                        <div className="stat">
                            <span className="stat-label">Language:</span>
                            <span className="stat-value">{ocrResults.detected_language}</span>
                        </div>
                    </div>

                    <div className="extracted-text">
                        <textarea 
                            value={ocrResults.text} 
                            readOnly 
                            rows={10}
                            placeholder="Extracted text will appear here..."
                        />
                    </div>

                    {ocrResults.preprocessing_applied && ocrResults.preprocessing_applied.length > 0 && (
                        <div className="preprocessing-info">
                            <h4>🔧 Image Preprocessing Applied:</h4>
                            <ul>
                                {ocrResults.preprocessing_applied.map((filter, index) => (
                                    <li key={index}>{filter}</li>
                                ))}
                            </ul>
                        </div>
                    )}

                    {ocrResults.performance_metrics && (
                        <div className="performance-metrics">
                            <h4>⚡ Performance Metrics:</h4>
                            <div className="metrics-grid">
                                <div className="metric">
                                    <span>Preprocessing:</span>
                                    <span>{ocrResults.performance_metrics.preprocessing_time_ms}ms</span>
                                </div>
                                <div className="metric">
                                    <span>OCR Processing:</span>
                                    <span>{ocrResults.performance_metrics.ocr_time_ms}ms</span>
                                </div>
                                <div className="metric">
                                    <span>Image Size:</span>
                                    <span>{(ocrResults.performance_metrics.image_size_bytes / 1024).toFixed(1)}KB</span>
                                </div>
                                <div className="metric">
                                    <span>Memory Usage:</span>
                                    <span>{(ocrResults.performance_metrics.memory_usage_bytes / 1024 / 1024).toFixed(1)}MB</span>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Performance Stats */}
            {performanceStats && (
                <div className="performance-stats">
                    <h3>📊 Engine Performance</h3>
                    <div className="stats-grid">
                        <div className="stat-item">
                            <span>Total Processed:</span>
                            <span>{performanceStats.totalProcessed}</span>
                        </div>
                        <div className="stat-item">
                            <span>Average Time:</span>
                            <span>{performanceStats.averageTime.toFixed(0)}ms</span>
                        </div>
                        <div className="stat-item">
                            <span>Success Rate:</span>
                            <span>{(performanceStats.successRate * 100).toFixed(1)}%</span>
                        </div>
                        <div className="stat-item">
                            <span>Initialized:</span>
                            <span>{performanceStats.isInitialized ? '✅' : '❌'}</span>
                        </div>
                    </div>
                </div>
            )}

            {/* Action Buttons */}
            <div className="action-buttons">
                <button 
                    onClick={runPerformanceTests}
                    disabled={isProcessing}
                    className="test-button"
                >
                    🧪 Run Performance Tests
                </button>
                
                <button 
                    onClick={() => {
                        setOcrResults(null);
                        setError(null);
                        setPerformanceStats(null);
                    }}
                    className="clear-button"
                >
                    🗑️ Clear Results
                </button>
            </div>
        </div>
    );
};

export default OCRProcessor;
