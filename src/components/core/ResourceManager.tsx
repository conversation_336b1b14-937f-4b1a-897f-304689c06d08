/**
 * Resource Manager Component
 * Advanced resource management and memory optimization
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { MemoryStick, Cpu, HardDrive, Zap, AlertTriangle, CheckCircle, TrendingUp, Trash2, Activity, Settings } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import './ResourceManager.css';

interface ResourceMetrics {
    memory_usage_mb: number;
    memory_limit_mb: number;
    cpu_usage_percent: number;
    disk_usage_mb: number;
    active_processes: number;
    resource_pools: ResourcePool[];
    gc_stats: GarbageCollectionStats;
    // Auto-scaling metrics
    thread_pool_size: number;
    optimal_thread_count: number;
    scaling_events: ScalingEvent[];
    workload_prediction: WorkloadPrediction;
}

interface ScalingEvent {
    timestamp: number;
    event_type: 'scale_up' | 'scale_down' | 'threshold_adjust';
    resource_type: 'memory' | 'cpu' | 'threads';
    old_value: number;
    new_value: number;
    trigger_reason: string;
}

interface WorkloadPrediction {
    predicted_memory_usage_mb: number;
    predicted_cpu_usage_percent: number;
    confidence_score: number;
    time_horizon_minutes: number;
}

interface ResourcePool {
    name: string;
    type: string;
    active_count: number;
    max_size: number;
    hit_rate: number;
    memory_usage_mb: number;
}

interface GarbageCollectionStats {
    collections: number;
    freed_memory_mb: number;
    last_collection: number;
    avg_collection_time_ms: number;
}

interface MemoryPressureAlert {
    level: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    recommendation: string;
    threshold: number;
}

interface AutoScalingConfig {
    enabled: boolean;
    memory_scaling: MemoryScalingConfig;
    cpu_scaling: CpuScalingConfig;
    thread_scaling: ThreadScalingConfig;
    prediction_enabled: boolean;
}

interface MemoryScalingConfig {
    scale_up_threshold: number;    // 80%
    scale_down_threshold: number;  // 50%
    aggressive_cleanup_threshold: number; // 90%
    cooldown_minutes: number;      // 5 minutes
    max_cache_size_mb: number;     // 500MB
    min_cache_size_mb: number;     // 50MB
}

interface CpuScalingConfig {
    scale_up_threshold: number;    // 85%
    scale_down_threshold: number;  // 40%
    target_utilization: number;    // 70%
    cooldown_minutes: number;      // 3 minutes
}

interface ThreadScalingConfig {
    min_threads: number;           // 2
    max_threads: number;           // CPU cores * 2
    scale_up_threshold: number;    // 80% CPU
    scale_down_threshold: number;  // 30% CPU
    cooldown_minutes: number;      // 2 minutes
}

const ResourceManager: React.FC = () => {
    const [metrics, setMetrics] = useState<ResourceMetrics | null>(null);
    const [alerts, setAlerts] = useState<MemoryPressureAlert[]>([]);
    const [isMonitoring, setIsMonitoring] = useState(false);
    const [autoCleanup, setAutoCleanup] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [autoScalingConfig, setAutoScalingConfig] = useState<AutoScalingConfig>({
        enabled: true,
        memory_scaling: {
            scale_up_threshold: 80,
            scale_down_threshold: 50,
            aggressive_cleanup_threshold: 90,
            cooldown_minutes: 5,
            max_cache_size_mb: 500,
            min_cache_size_mb: 50,
        },
        cpu_scaling: {
            scale_up_threshold: 85,
            scale_down_threshold: 40,
            target_utilization: 70,
            cooldown_minutes: 3,
        },
        thread_scaling: {
            min_threads: 2,
            max_threads: navigator.hardwareConcurrency * 2 || 8,
            scale_up_threshold: 80,
            scale_down_threshold: 30,
            cooldown_minutes: 2,
        },
        prediction_enabled: true,
    });
    const [showScalingConfig, setShowScalingConfig] = useState(false);
    const intervalRef = useRef<number | null>(null);

    // Fetch resource metrics
    const fetchMetrics = useCallback(async () => {
        try {
            const resourceMetrics = await invoke<ResourceMetrics>('get_resource_metrics');
            setMetrics(resourceMetrics);
            
            // Check for memory pressure alerts
            checkMemoryPressure(resourceMetrics);
        } catch (error) {
            console.error('Failed to fetch resource metrics:', error);
        }
    }, []);

    // Check for memory pressure and generate alerts
    const checkMemoryPressure = useCallback((metrics: ResourceMetrics) => {
        const newAlerts: MemoryPressureAlert[] = [];
        const memoryUsagePercent = (metrics.memory_usage_mb / metrics.memory_limit_mb) * 100;

        if (memoryUsagePercent >= 90) {
            newAlerts.push({
                level: 'critical',
                message: 'Critical memory usage detected',
                recommendation: 'Immediate cleanup required - consider closing applications',
                threshold: 90
            });
        } else if (memoryUsagePercent >= 80) {
            newAlerts.push({
                level: 'high',
                message: 'High memory usage detected',
                recommendation: 'Run garbage collection and clear caches',
                threshold: 80
            });
        } else if (memoryUsagePercent >= 70) {
            newAlerts.push({
                level: 'medium',
                message: 'Moderate memory usage',
                recommendation: 'Monitor closely and prepare for cleanup',
                threshold: 70
            });
        }

        // Check CPU usage
        if (metrics.cpu_usage_percent >= 85) {
            newAlerts.push({
                level: 'high',
                message: 'High CPU usage detected',
                recommendation: 'Reduce concurrent operations',
                threshold: 85
            });
        }

        setAlerts(newAlerts);
    }, []);

    // Force garbage collection
    const forceGarbageCollection = useCallback(async () => {
        try {
            setIsLoading(true);
            await invoke('force_garbage_collection');
            await fetchMetrics();
        } catch (error) {
            console.error('Failed to force garbage collection:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchMetrics]);

    // Clear resource pools
    // Resource pool clearing function (currently unused but may be needed for future features)
    // const clearResourcePools = useCallback(async () => {
    //     try {
    //         setIsLoading(true);
    //         await invoke('clear_resource_pools');
    //         await fetchMetrics();
    //     } catch (error) {
    //         console.error('Failed to clear resource pools:', error);
    //     } finally {
    //         setIsLoading(false);
    //     }
    // }, [fetchMetrics]);

    // Optimize memory usage
    const optimizeMemory = useCallback(async () => {
        try {
            setIsLoading(true);
            await invoke('optimize_memory_usage');
            await fetchMetrics();
        } catch (error) {
            console.error('Failed to optimize memory:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchMetrics]);

    // Auto-scaling functions
    const enableAutoScaling = useCallback(async () => {
        try {
            setIsLoading(true);
            await invoke('enable_auto_scaling', { config: autoScalingConfig });
            await fetchMetrics();
        } catch (error) {
            console.error('Failed to enable auto-scaling:', error);
        } finally {
            setIsLoading(false);
        }
    }, [autoScalingConfig, fetchMetrics]);

    const disableAutoScaling = useCallback(async () => {
        try {
            setIsLoading(true);
            await invoke('disable_auto_scaling');
            await fetchMetrics();
        } catch (error) {
            console.error('Failed to disable auto-scaling:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchMetrics]);

    const adjustThreadPool = useCallback(async (newSize: number) => {
        try {
            setIsLoading(true);
            await invoke('adjust_thread_pool_size', { size: newSize });
            await fetchMetrics();
        } catch (error) {
            console.error('Failed to adjust thread pool:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchMetrics]);

    const updateScalingConfig = useCallback(async (newConfig: Partial<AutoScalingConfig>) => {
        const updatedConfig = { ...autoScalingConfig, ...newConfig };
        setAutoScalingConfig(updatedConfig);

        if (autoScalingConfig.enabled) {
            try {
                await invoke('update_scaling_config', { config: updatedConfig });
            } catch (error) {
                console.error('Failed to update scaling config:', error);
            }
        }
    }, [autoScalingConfig]);

    // Start/stop monitoring
    const toggleMonitoring = useCallback(() => {
        if (isMonitoring) {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
            setIsMonitoring(false);
        } else {
            fetchMetrics();
            intervalRef.current = setInterval(fetchMetrics, 3000);
            setIsMonitoring(true);
        }
    }, [isMonitoring, fetchMetrics]);

    // Auto-cleanup and auto-scaling effect
    useEffect(() => {
        if (!metrics) return;

        const memoryUsagePercent = (metrics.memory_usage_mb / metrics.memory_limit_mb) * 100;
        const cpuUsagePercent = metrics.cpu_usage_percent;

        // Auto-cleanup (legacy behavior)
        if (autoCleanup && memoryUsagePercent >= 85) {
            forceGarbageCollection();
        }

        // Auto-scaling logic
        if (autoScalingConfig.enabled) {
            // Memory scaling
            if (memoryUsagePercent >= autoScalingConfig.memory_scaling.aggressive_cleanup_threshold) {
                // Emergency cleanup
                forceGarbageCollection();
            } else if (memoryUsagePercent >= autoScalingConfig.memory_scaling.scale_up_threshold) {
                // Trigger memory optimization
                optimizeMemory();
            }

            // Thread scaling based on CPU usage
            if (cpuUsagePercent >= autoScalingConfig.cpu_scaling.scale_up_threshold &&
                metrics.thread_pool_size < autoScalingConfig.thread_scaling.max_threads) {
                const newSize = Math.min(
                    metrics.thread_pool_size + 1,
                    autoScalingConfig.thread_scaling.max_threads
                );
                adjustThreadPool(newSize);
            } else if (cpuUsagePercent <= autoScalingConfig.cpu_scaling.scale_down_threshold &&
                       metrics.thread_pool_size > autoScalingConfig.thread_scaling.min_threads) {
                const newSize = Math.max(
                    metrics.thread_pool_size - 1,
                    autoScalingConfig.thread_scaling.min_threads
                );
                adjustThreadPool(newSize);
            }
        }
    }, [autoCleanup, metrics, autoScalingConfig, forceGarbageCollection, optimizeMemory, adjustThreadPool]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
        };
    }, []);

    // Format bytes to human readable
    const formatBytes = (bytes: number): string => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    // Get memory usage color
    const getMemoryUsageColor = (usagePercent: number): string => {
        if (usagePercent >= 90) return '#ef4444'; // red
        if (usagePercent >= 80) return '#f59e0b'; // amber
        if (usagePercent >= 70) return '#eab308'; // yellow
        return '#10b981'; // green
    };

    // Get alert icon
    const getAlertIcon = (level: string) => {
        switch (level) {
            case 'critical':
                return <AlertTriangle className="alert-icon critical" />;
            case 'high':
                return <AlertTriangle className="alert-icon high" />;
            case 'medium':
                return <AlertTriangle className="alert-icon medium" />;
            default:
                return <CheckCircle className="alert-icon low" />;
        }
    };

    const memoryUsagePercent = metrics ? (metrics.memory_usage_mb / metrics.memory_limit_mb) * 100 : 0;

    return (
        <div className="resource-manager">
            <div className="resource-header">
                <div className="header-content">
                    <MemoryStick className="header-icon" />
                    <div>
                        <h3>Resource Management</h3>
                        <p>Advanced memory optimization and resource monitoring</p>
                    </div>
                </div>
                
                <div className="resource-controls">
                    <button
                        className={`monitor-toggle ${isMonitoring ? 'active' : ''}`}
                        onClick={toggleMonitoring}
                        disabled={isLoading}
                        aria-label={isMonitoring ? 'Stop monitoring' : 'Start monitoring'}
                    >
                        {isMonitoring ? 'Stop' : 'Start'} Monitoring
                    </button>
                    
                    <button
                        className="gc-button"
                        onClick={forceGarbageCollection}
                        disabled={isLoading}
                        aria-label="Force garbage collection"
                    >
                        <Trash2 size={16} />
                        GC
                    </button>
                    
                    <button
                        className="optimize-button"
                        onClick={optimizeMemory}
                        disabled={isLoading}
                        aria-label="Optimize memory"
                    >
                        <Zap size={16} />
                        Optimize
                    </button>

                    <button
                        className={`autoscaling-toggle ${autoScalingConfig.enabled ? 'active' : ''}`}
                        onClick={() => autoScalingConfig.enabled ? disableAutoScaling() : enableAutoScaling()}
                        disabled={isLoading}
                        aria-label={autoScalingConfig.enabled ? 'Disable auto-scaling' : 'Enable auto-scaling'}
                    >
                        <TrendingUp size={16} />
                        {autoScalingConfig.enabled ? 'Auto-scaling ON' : 'Auto-scaling OFF'}
                    </button>

                    <button
                        className="config-button"
                        onClick={() => setShowScalingConfig(!showScalingConfig)}
                        aria-label="Toggle scaling configuration"
                    >
                        <Settings size={16} />
                        Config
                    </button>
                </div>
            </div>

            {/* Memory Pressure Alerts */}
            {alerts.length > 0 && (
                <div className="alerts-section">
                    <h4>Resource Alerts</h4>
                    <div className="alerts-list">
                        {alerts.map((alert, index) => (
                            <div key={index} className={`alert alert-${alert.level}`}>
                                {getAlertIcon(alert.level)}
                                <div className="alert-content">
                                    <div className="alert-message">{alert.message}</div>
                                    <div className="alert-recommendation">{alert.recommendation}</div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Auto-scaling Configuration */}
            {showScalingConfig && (
                <div className="scaling-config">
                    <h4>Auto-scaling Configuration</h4>
                    <div className="config-grid">
                        <div className="config-section">
                            <h5>Memory Scaling</h5>
                            <div className="config-item">
                                <label>Scale Up Threshold (%)</label>
                                <input
                                    type="number"
                                    value={autoScalingConfig.memory_scaling.scale_up_threshold}
                                    onChange={(e) => updateScalingConfig({
                                        memory_scaling: {
                                            ...autoScalingConfig.memory_scaling,
                                            scale_up_threshold: parseInt(e.target.value)
                                        }
                                    })}
                                    min="50"
                                    max="95"
                                />
                            </div>
                            <div className="config-item">
                                <label>Scale Down Threshold (%)</label>
                                <input
                                    type="number"
                                    value={autoScalingConfig.memory_scaling.scale_down_threshold}
                                    onChange={(e) => updateScalingConfig({
                                        memory_scaling: {
                                            ...autoScalingConfig.memory_scaling,
                                            scale_down_threshold: parseInt(e.target.value)
                                        }
                                    })}
                                    min="20"
                                    max="80"
                                />
                            </div>
                        </div>

                        <div className="config-section">
                            <h5>CPU Scaling</h5>
                            <div className="config-item">
                                <label>Scale Up Threshold (%)</label>
                                <input
                                    type="number"
                                    value={autoScalingConfig.cpu_scaling.scale_up_threshold}
                                    onChange={(e) => updateScalingConfig({
                                        cpu_scaling: {
                                            ...autoScalingConfig.cpu_scaling,
                                            scale_up_threshold: parseInt(e.target.value)
                                        }
                                    })}
                                    min="60"
                                    max="95"
                                />
                            </div>
                            <div className="config-item">
                                <label>Target Utilization (%)</label>
                                <input
                                    type="number"
                                    value={autoScalingConfig.cpu_scaling.target_utilization}
                                    onChange={(e) => updateScalingConfig({
                                        cpu_scaling: {
                                            ...autoScalingConfig.cpu_scaling,
                                            target_utilization: parseInt(e.target.value)
                                        }
                                    })}
                                    min="50"
                                    max="90"
                                />
                            </div>
                        </div>

                        <div className="config-section">
                            <h5>Thread Scaling</h5>
                            <div className="config-item">
                                <label>Min Threads</label>
                                <input
                                    type="number"
                                    value={autoScalingConfig.thread_scaling.min_threads}
                                    onChange={(e) => updateScalingConfig({
                                        thread_scaling: {
                                            ...autoScalingConfig.thread_scaling,
                                            min_threads: parseInt(e.target.value)
                                        }
                                    })}
                                    min="1"
                                    max="8"
                                />
                            </div>
                            <div className="config-item">
                                <label>Max Threads</label>
                                <input
                                    type="number"
                                    value={autoScalingConfig.thread_scaling.max_threads}
                                    onChange={(e) => updateScalingConfig({
                                        thread_scaling: {
                                            ...autoScalingConfig.thread_scaling,
                                            max_threads: parseInt(e.target.value)
                                        }
                                    })}
                                    min="2"
                                    max="32"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Resource Metrics */}
            {metrics && (
                <div className="resource-metrics">
                    <div className="metrics-grid">
                        <div className="metric-card">
                            <div className="metric-header">
                                <MemoryStick size={20} />
                                <span>Memory Usage</span>
                            </div>
                            <div className="metric-value">
                                {formatBytes(metrics.memory_usage_mb * 1024 * 1024)}
                            </div>
                            <div className="metric-detail">
                                {memoryUsagePercent.toFixed(1)}% of {formatBytes(metrics.memory_limit_mb * 1024 * 1024)}
                            </div>
                            <div
                                className="progress-bar"
                                style={{
                                    background: `linear-gradient(to right, ${getMemoryUsageColor(memoryUsagePercent)} ${memoryUsagePercent}%, #e5e7eb ${memoryUsagePercent}%)`
                                }}
                            />
                        </div>
                        
                        <div className="metric-card">
                            <div className="metric-header">
                                <Cpu size={20} />
                                <span>CPU Usage</span>
                            </div>
                            <div className="metric-value">{metrics.cpu_usage_percent.toFixed(1)}%</div>
                            <div className="metric-detail">{metrics.active_processes} active processes</div>
                        </div>
                        
                        <div className="metric-card">
                            <div className="metric-header">
                                <HardDrive size={20} />
                                <span>Disk Usage</span>
                            </div>
                            <div className="metric-value">{formatBytes(metrics.disk_usage_mb * 1024 * 1024)}</div>
                            <div className="metric-detail">Application data</div>
                        </div>
                        
                        <div className="metric-card">
                            <div className="metric-header">
                                <TrendingUp size={20} />
                                <span>GC Stats</span>
                            </div>
                            <div className="metric-value">{metrics.gc_stats.collections}</div>
                            <div className="metric-detail">
                                {formatBytes(metrics.gc_stats.freed_memory_mb * 1024 * 1024)} freed
                            </div>
                        </div>

                        {/* Auto-scaling specific metrics */}
                        <div className="metric-card">
                            <div className="metric-header">
                                <Activity size={20} />
                                <span>Thread Pool</span>
                            </div>
                            <div className="metric-value">{metrics.thread_pool_size || 'N/A'}</div>
                            <div className="metric-detail">
                                Optimal: {metrics.optimal_thread_count || 'Calculating...'}
                            </div>
                        </div>

                        {autoScalingConfig.prediction_enabled && metrics.workload_prediction && (
                            <div className="metric-card">
                                <div className="metric-header">
                                    <TrendingUp size={20} />
                                    <span>Prediction</span>
                                </div>
                                <div className="metric-value">
                                    {metrics.workload_prediction.confidence_score.toFixed(0)}%
                                </div>
                                <div className="metric-detail">
                                    {metrics.workload_prediction.time_horizon_minutes}min horizon
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Scaling Events History */}
            {metrics?.scaling_events && metrics.scaling_events.length > 0 && (
                <div className="scaling-events">
                    <h4>Recent Scaling Events</h4>
                    <div className="events-list">
                        {metrics.scaling_events.slice(-5).map((event, index) => (
                            <div key={index} className={`event-item event-${event.event_type}`}>
                                <div className="event-time">
                                    {new Date(event.timestamp * 1000).toLocaleTimeString()}
                                </div>
                                <div className="event-description">
                                    <strong>{event.resource_type}</strong> {event.event_type.replace('_', ' ')}:
                                    {event.old_value} → {event.new_value}
                                </div>
                                <div className="event-reason">{event.trigger_reason}</div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Auto-cleanup toggle */}
            <div className="auto-cleanup">
                <label>
                    <input
                        type="checkbox"
                        checked={autoCleanup}
                        onChange={(e) => setAutoCleanup(e.target.checked)}
                    />
                    Enable automatic memory cleanup at 85% usage
                </label>
            </div>
        </div>
    );
};

export default ResourceManager;
