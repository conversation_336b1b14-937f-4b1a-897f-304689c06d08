# Test Document - Social Security Numbers

This document contains test Social Security Numbers for privacy detection validation.

**IMPORTANT**: These are FAKE SSNs for testing purposes only.

## Test SSN Patterns

### Valid Format SSNs (Fake)
- <PERSON>: ***********
- <PERSON>: ***********
- <PERSON>: ***********
- <PERSON>: ***********

### Various Formats
- Without dashes: 123456789
- With spaces: 123 45 6789
- Mixed format: 123-45 6789
- Parentheses: (123) 45-6789

### Edge Cases
- Leading zeros: ***********
- All same digits: ***********
- Sequential: ***********

## Additional Personal Information

### Names and SSNs
- <PERSON>, SSN: ***********
- <PERSON>, Social Security: ***********
- <PERSON>, SS#: ***********

### In Sentences
The employee's social security number is *********** and should be kept confidential.
Please verify SSN *********** before processing the application.
Contact information includes SSN: *********** for verification purposes.

### Mixed Content
This document also contains:
- Phone numbers: (*************
- Email addresses: <EMAIL>
- Addresses: 123 Main St, Anytown, ST 12345

## Privacy Detection Test Cases

1. **Direct SSN**: ***********
2. **SSN in context**: "The SSN *********** belongs to the applicant"
3. **Multiple SSNs**: ***********, ***********, ***********
4. **SSN with label**: Social Security Number: ***********
5. **Abbreviated**: SS# ***********

## Expected Detection Results

This document should trigger:
- HIGH RISK classification
- Multiple SSN pattern matches
- Privacy data type: SOCIAL_SECURITY_NUMBER
- Confidence score: >95%
- AI enhancement should identify context and relationships

## Test Validation

The privacy detection system should:
1. Identify all SSN patterns (15+ instances)
2. Classify document as HIGH RISK
3. Provide accurate confidence scores
4. Generate appropriate privacy findings
5. Suggest secure handling recommendations
