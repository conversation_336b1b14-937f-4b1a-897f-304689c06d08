/// Unified Document Type Detection System
/// 
/// Combines template matching, ML classification, and layout analysis
/// for comprehensive document type detection with ensemble predictions.

use std::collections::HashMap;
use std::path::Path;
use serde::{Serialize, Deserialize};
use image::DynamicImage;
use crate::privacy::document_template_matcher::{
    DocumentTemplateMatcher, TemplateMatchResult, DocumentType, LayoutFeatures
};
use crate::privacy::document_classifier_ml::{
    DocumentClassifierML, MLClassificationResult, MLModelConfig
};
use crate::privacy::balanced_error_reporting::{BalancedErrorReporter, LogLevel, ErrorReportingConfig};
// Using balanced error reporter instead of macros

/// Unified document detection result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedDetectionResult {
    /// Final predicted document type
    pub predicted_type: DocumentType,
    /// Overall confidence score (0.0-1.0)
    pub confidence: f64,
    /// Template matching results
    pub template_result: TemplateMatchResult,
    /// ML classification results
    pub ml_result: MLClassificationResult,
    /// Ensemble prediction details
    pub ensemble_details: EnsembleDetails,
    /// Processing time breakdown
    pub processing_breakdown: ProcessingBreakdown,
    /// Detection metadata
    pub metadata: DetectionMetadata,
}

/// Ensemble prediction details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnsembleDetails {
    /// Template matching weight in final decision
    pub template_weight: f64,
    /// ML classification weight in final decision
    pub ml_weight: f64,
    /// Layout analysis weight in final decision
    pub layout_weight: f64,
    /// Consensus score between methods
    pub consensus_score: f64,
    /// Individual method agreements
    pub method_agreements: HashMap<String, bool>,
}

/// Processing time breakdown
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingBreakdown {
    /// Template matching time (ms)
    pub template_time_ms: u64,
    /// ML classification time (ms)
    pub ml_time_ms: u64,
    /// Layout analysis time (ms)
    pub layout_time_ms: u64,
    /// Ensemble processing time (ms)
    pub ensemble_time_ms: u64,
    /// Total processing time (ms)
    pub total_time_ms: u64,
}

/// Detection metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionMetadata {
    /// Image dimensions
    pub image_dimensions: (u32, u32),
    /// Image file size (if available)
    pub image_size_bytes: Option<usize>,
    /// Text extraction available
    pub has_extracted_text: bool,
    /// Number of detected features
    pub feature_count: usize,
    /// Detection quality score
    pub quality_score: f64,
}

/// Unified detector configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedDetectorConfig {
    /// Enable template matching
    pub enable_template_matching: bool,
    /// Enable ML classification
    pub enable_ml_classification: bool,
    /// Ensemble method
    pub ensemble_method: EnsembleMethod,
    /// Minimum confidence threshold
    pub min_confidence: f64,
    /// ML model configuration
    pub ml_config: MLModelConfig,
    /// Template matching configuration
    pub template_config: TemplateMatchConfig,
}

/// Ensemble prediction methods
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EnsembleMethod {
    /// Weighted average of predictions
    WeightedAverage,
    /// Majority voting
    MajorityVoting,
    /// Confidence-based selection
    ConfidenceBased,
    /// Adaptive weighting based on document characteristics
    Adaptive,
}

/// Template matching configuration (simplified)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateMatchConfig {
    /// Minimum confidence threshold
    pub min_confidence: f64,
    /// Enable multi-scale matching
    pub multi_scale: bool,
}

impl Default for UnifiedDetectorConfig {
    fn default() -> Self {
        Self {
            enable_template_matching: true,
            enable_ml_classification: true,
            ensemble_method: EnsembleMethod::Adaptive,
            min_confidence: 0.7,
            ml_config: MLModelConfig::default(),
            template_config: TemplateMatchConfig {
                min_confidence: 0.6,
                multi_scale: true,
            },
        }
    }
}

/// Unified Document Type Detector
pub struct UnifiedDocumentDetector {
    /// Configuration
    config: UnifiedDetectorConfig,
    /// Template matcher
    template_matcher: DocumentTemplateMatcher,
    /// ML classifier
    ml_classifier: DocumentClassifierML,
    /// Balanced error reporter
    reporter: BalancedErrorReporter,
    /// Performance statistics
    performance_stats: HashMap<String, u64>,
}

impl UnifiedDocumentDetector {
    /// Create new unified detector
    pub fn new() -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        Self::with_config(UnifiedDetectorConfig::default())
    }
    
    /// Create new unified detector with configuration
    pub fn with_config(config: UnifiedDetectorConfig) -> Result<Self, Box<dyn std::error::Error + Send + Sync>> {
        let template_matcher = DocumentTemplateMatcher::new();
        let ml_classifier = DocumentClassifierML::new()?;
        let reporter = BalancedErrorReporter::new(ErrorReportingConfig::default());
        
        Ok(Self {
            config,
            template_matcher,
            ml_classifier,
            reporter,
            performance_stats: HashMap::new(),
        })
    }
    
    /// Initialize the detector (load models, templates, etc.)
    pub async fn initialize(&mut self, models_dir: &Path) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        self.reporter.start_operation("detector_initialization", 1);
        self.reporter.log(LogLevel::Info, "Initializing unified document detector", "unified_detector");
        
        let start_time = std::time::Instant::now();
        
        // Load templates
        let template_dir = models_dir.join("templates");
        let template_count = self.template_matcher.load_templates(&template_dir)?;
        self.reporter.log(LogLevel::Info, &format!("Loaded {} templates", template_count), "template_matcher");
        
        // Load ML model
        if self.config.enable_ml_classification {
            let model_path = models_dir.join("document_classifier.safetensors");
            self.ml_classifier.load_model(&model_path).await?;
            self.reporter.log(LogLevel::Info, "ML model loaded successfully", "ml_classifier");
        }
        
        let init_time = start_time.elapsed().as_millis() as u64;
        self.reporter.log_performance("initialization", init_time, "unified_detector");
        self.reporter.log(LogLevel::Info, &format!("Detector initialized in {}ms", init_time), "unified_detector");
        
        let _report = self.reporter.end_operation();
        
        Ok(())
    }
    
    /// Detect document type using unified approach
    pub async fn detect_document_type(
        &mut self,
        image: &DynamicImage,
        extracted_text: Option<&str>
    ) -> Result<UnifiedDetectionResult, Box<dyn std::error::Error + Send + Sync>> {
        
        self.reporter.start_operation("unified_detection", 1);
        let total_start = std::time::Instant::now();
        
        self.reporter.log(LogLevel::Info, 
            &format!("Starting unified document detection for {}x{} image", image.width(), image.height()), 
            "unified_detector");
        
        // Template matching
        let template_start = std::time::Instant::now();
        let template_result = if self.config.enable_template_matching {
            self.template_matcher.match_document(image).await?
        } else {
            self.create_empty_template_result()
        };
        let template_time = template_start.elapsed().as_millis() as u64;
        
        // ML classification
        let ml_start = std::time::Instant::now();
        let ml_result = if self.config.enable_ml_classification {
            self.ml_classifier.classify_document(image, &template_result.layout_features, extracted_text).await?
        } else {
            self.create_empty_ml_result()
        };
        let ml_time = ml_start.elapsed().as_millis() as u64;
        
        // Ensemble prediction
        let ensemble_start = std::time::Instant::now();
        let (predicted_type, confidence, ensemble_details) = self.perform_ensemble_prediction(
            &template_result, 
            &ml_result, 
            image
        )?;
        let ensemble_time = ensemble_start.elapsed().as_millis() as u64;
        
        let total_time = total_start.elapsed().as_millis() as u64;
        
        // Create processing breakdown
        let processing_breakdown = ProcessingBreakdown {
            template_time_ms: template_time,
            ml_time_ms: ml_time,
            layout_time_ms: 0, // Included in template time
            ensemble_time_ms: ensemble_time,
            total_time_ms: total_time,
        };
        
        // Create metadata
        let metadata = DetectionMetadata {
            image_dimensions: (image.width(), image.height()),
            image_size_bytes: None,
            has_extracted_text: extracted_text.is_some(),
            feature_count: template_result.layout_features.text_blocks + 
                          template_result.layout_features.tables + 
                          template_result.layout_features.form_fields,
            quality_score: self.calculate_quality_score(&template_result, &ml_result),
        };
        
        let result = UnifiedDetectionResult {
            predicted_type: predicted_type.clone(),
            confidence,
            template_result,
            ml_result,
            ensemble_details,
            processing_breakdown,
            metadata,
        };
        
        // Log performance
        self.reporter.log_performance("unified_detection", total_time, "unified_detector");
        self.reporter.log(LogLevel::Info, 
            &format!("Detection completed: {:?} (confidence: {:.2}, time: {}ms)", 
                    predicted_type, confidence, total_time), 
            "unified_detector");
        
        let _report = self.reporter.end_operation();
        
        Ok(result)
    }
    
    /// Perform ensemble prediction
    fn perform_ensemble_prediction(
        &self,
        template_result: &TemplateMatchResult,
        ml_result: &MLClassificationResult,
        image: &DynamicImage
    ) -> Result<(DocumentType, f64, EnsembleDetails), Box<dyn std::error::Error + Send + Sync>> {
        
        match self.config.ensemble_method {
            EnsembleMethod::WeightedAverage => {
                self.weighted_average_ensemble(template_result, ml_result)
            },
            EnsembleMethod::MajorityVoting => {
                self.majority_voting_ensemble(template_result, ml_result)
            },
            EnsembleMethod::ConfidenceBased => {
                self.confidence_based_ensemble(template_result, ml_result)
            },
            EnsembleMethod::Adaptive => {
                self.adaptive_ensemble(template_result, ml_result, image)
            },
        }
    }
    
    /// Weighted average ensemble
    fn weighted_average_ensemble(
        &self,
        template_result: &TemplateMatchResult,
        ml_result: &MLClassificationResult
    ) -> Result<(DocumentType, f64, EnsembleDetails), Box<dyn std::error::Error + Send + Sync>> {
        
        let template_weight = 0.4;
        let ml_weight = 0.6;
        
        // Combine scores for each document type
        let mut combined_scores = HashMap::new();
        
        // Get all possible document types
        let all_types = [
            DocumentType::GovernmentId,
            DocumentType::Financial,
            DocumentType::Medical,
            DocumentType::Legal,
            DocumentType::Employment,
            DocumentType::Educational,
            DocumentType::Insurance,
            DocumentType::Business,
            DocumentType::Personal,
            DocumentType::Unknown,
        ];
        
        for doc_type in &all_types {
            let template_score = if template_result.document_type == *doc_type {
                template_result.confidence
            } else {
                0.0
            };
            
            let ml_score = ml_result.type_scores.get(doc_type).copied().unwrap_or(0.0);
            
            let combined_score = template_score * template_weight + ml_score * ml_weight;
            combined_scores.insert(doc_type.clone(), combined_score);
        }
        
        // Find best prediction
        let (best_type, best_score) = combined_scores.iter()
            .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal))
            .map(|(t, &s)| (t.clone(), s))
            .unwrap_or((DocumentType::Unknown, 0.0));
        
        // Calculate consensus
        let consensus_score = if template_result.document_type == ml_result.predicted_type {
            0.9
        } else {
            0.3
        };
        
        let mut method_agreements = HashMap::new();
        method_agreements.insert("template_ml_agreement".to_string(), 
                               template_result.document_type == ml_result.predicted_type);
        
        let ensemble_details = EnsembleDetails {
            template_weight,
            ml_weight,
            layout_weight: 0.0,
            consensus_score,
            method_agreements,
        };
        
        Ok((best_type, best_score, ensemble_details))
    }
    
    /// Majority voting ensemble
    fn majority_voting_ensemble(
        &self,
        template_result: &TemplateMatchResult,
        ml_result: &MLClassificationResult
    ) -> Result<(DocumentType, f64, EnsembleDetails), Box<dyn std::error::Error + Send + Sync>> {
        
        // Simple majority vote between template and ML
        let (predicted_type, confidence) = if template_result.confidence > ml_result.confidence {
            (template_result.document_type.clone(), template_result.confidence)
        } else {
            (ml_result.predicted_type.clone(), ml_result.confidence)
        };
        
        let consensus_score = if template_result.document_type == ml_result.predicted_type {
            1.0
        } else {
            0.0
        };
        
        let mut method_agreements = HashMap::new();
        method_agreements.insert("template_ml_agreement".to_string(), 
                               template_result.document_type == ml_result.predicted_type);
        
        let ensemble_details = EnsembleDetails {
            template_weight: 0.5,
            ml_weight: 0.5,
            layout_weight: 0.0,
            consensus_score,
            method_agreements,
        };
        
        Ok((predicted_type, confidence, ensemble_details))
    }
    
    /// Confidence-based ensemble
    fn confidence_based_ensemble(
        &self,
        template_result: &TemplateMatchResult,
        ml_result: &MLClassificationResult
    ) -> Result<(DocumentType, f64, EnsembleDetails), Box<dyn std::error::Error + Send + Sync>> {
        
        // Select the method with highest confidence
        let (predicted_type, confidence, template_weight, ml_weight) = 
            if template_result.confidence > ml_result.confidence {
                (template_result.document_type.clone(), template_result.confidence, 1.0, 0.0)
            } else {
                (ml_result.predicted_type.clone(), ml_result.confidence, 0.0, 1.0)
            };
        
        let consensus_score = if template_result.document_type == ml_result.predicted_type {
            1.0
        } else {
            (template_result.confidence - ml_result.confidence).abs()
        };
        
        let mut method_agreements = HashMap::new();
        method_agreements.insert("template_ml_agreement".to_string(), 
                               template_result.document_type == ml_result.predicted_type);
        
        let ensemble_details = EnsembleDetails {
            template_weight,
            ml_weight,
            layout_weight: 0.0,
            consensus_score,
            method_agreements,
        };
        
        Ok((predicted_type, confidence, ensemble_details))
    }
    
    /// Adaptive ensemble based on document characteristics
    fn adaptive_ensemble(
        &self,
        template_result: &TemplateMatchResult,
        ml_result: &MLClassificationResult,
        image: &DynamicImage
    ) -> Result<(DocumentType, f64, EnsembleDetails), Box<dyn std::error::Error + Send + Sync>> {
        
        // Adapt weights based on document characteristics
        let layout = &template_result.layout_features;
        
        // Template matching works better for structured documents
        let template_weight = if layout.tables > 0 || layout.form_fields > 5 {
            0.7 // Favor template matching for structured documents
        } else if layout.text_density > 0.3 {
            0.3 // Favor ML for text-heavy documents
        } else {
            0.5 // Balanced for other documents
        };
        
        let ml_weight = 1.0 - template_weight;
        
        // Weighted combination
        let mut combined_scores = HashMap::new();
        let all_types = [
            DocumentType::GovernmentId, DocumentType::Financial, DocumentType::Medical,
            DocumentType::Legal, DocumentType::Employment, DocumentType::Educational,
            DocumentType::Insurance, DocumentType::Business, DocumentType::Personal,
            DocumentType::Unknown,
        ];
        
        for doc_type in &all_types {
            let template_score = if template_result.document_type == *doc_type {
                template_result.confidence
            } else {
                0.0
            };
            
            let ml_score = ml_result.type_scores.get(doc_type).copied().unwrap_or(0.0);
            let combined_score = template_score * template_weight + ml_score * ml_weight;
            combined_scores.insert(doc_type.clone(), combined_score);
        }
        
        let (best_type, best_score) = combined_scores.iter()
            .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal))
            .map(|(t, &s)| (t.clone(), s))
            .unwrap_or((DocumentType::Unknown, 0.0));
        
        let consensus_score = if template_result.document_type == ml_result.predicted_type {
            0.9
        } else {
            0.4
        };
        
        let mut method_agreements = HashMap::new();
        method_agreements.insert("template_ml_agreement".to_string(), 
                               template_result.document_type == ml_result.predicted_type);
        
        let ensemble_details = EnsembleDetails {
            template_weight,
            ml_weight,
            layout_weight: 0.0,
            consensus_score,
            method_agreements,
        };
        
        Ok((best_type, best_score, ensemble_details))
    }

    /// Create empty template result for when template matching is disabled
    fn create_empty_template_result(&self) -> TemplateMatchResult {
        use crate::privacy::document_template_matcher::{TemplateMatchResult, LayoutFeatures, HeaderInfo, FooterInfo};

        TemplateMatchResult {
            document_type: DocumentType::Unknown,
            confidence: 0.0,
            template_scores: HashMap::new(),
            layout_features: LayoutFeatures {
                text_blocks: 0,
                tables: 0,
                form_fields: 0,
                header_info: HeaderInfo {
                    present: false,
                    height_ratio: 0.0,
                    text_content: None,
                },
                footer_info: FooterInfo {
                    present: false,
                    height_ratio: 0.0,
                    text_content: None,
                },
                logos_detected: Vec::new(),
                text_density: 0.0,
                complexity_score: 0.0,
            },
            processing_time_ms: 0,
            match_details: Vec::new(),
        }
    }

    /// Create empty ML result for when ML classification is disabled
    fn create_empty_ml_result(&self) -> MLClassificationResult {
        use crate::privacy::document_classifier_ml::{MLClassificationResult, ModelInfo};

        MLClassificationResult {
            predicted_type: DocumentType::Unknown,
            confidence: 0.0,
            type_scores: HashMap::new(),
            feature_vector: Vec::new(),
            processing_time_ms: 0,
            model_info: ModelInfo {
                name: "Disabled".to_string(),
                version: "0.0.0".to_string(),
                accuracy: 0.0,
                input_dimensions: 0,
                output_classes: 0,
            },
        }
    }

    /// Calculate overall quality score
    fn calculate_quality_score(
        &self,
        template_result: &TemplateMatchResult,
        ml_result: &MLClassificationResult
    ) -> f64 {
        let template_quality = template_result.confidence;
        let ml_quality = ml_result.confidence;
        let consensus_bonus = if template_result.document_type == ml_result.predicted_type {
            0.2
        } else {
            0.0
        };

        let base_quality = (template_quality + ml_quality) / 2.0;
        (base_quality + consensus_bonus).min(1.0)
    }

    /// Get performance statistics
    pub fn get_performance_stats(&self) -> HashMap<String, u64> {
        let mut stats = self.performance_stats.clone();

        // Add template matcher stats
        let template_stats = self.template_matcher.get_performance_stats();
        for (key, value) in template_stats {
            stats.insert(format!("template_{}", key), value);
        }

        // Add ML classifier stats
        let ml_stats = self.ml_classifier.get_performance_stats();
        for (key, value) in ml_stats {
            stats.insert(format!("ml_{}", key), value);
        }

        stats
    }

    /// Update configuration
    pub fn update_config(&mut self, config: UnifiedDetectorConfig) {
        self.config = config;

        // Update sub-component configurations
        self.ml_classifier.update_config(self.config.ml_config.clone());
    }

    /// Get current configuration
    pub fn get_config(&self) -> &UnifiedDetectorConfig {
        &self.config
    }

    /// Check if detector is ready for use
    pub fn is_ready(&self) -> bool {
        // Check if required components are initialized
        true // Simplified check
    }

    /// Get supported document types
    pub fn get_supported_types(&self) -> Vec<DocumentType> {
        vec![
            DocumentType::GovernmentId,
            DocumentType::Financial,
            DocumentType::Medical,
            DocumentType::Legal,
            DocumentType::Employment,
            DocumentType::Educational,
            DocumentType::Insurance,
            DocumentType::Business,
            DocumentType::Personal,
            DocumentType::Unknown,
        ]
    }
}
