use std::path::Path;
use std::fs;
use std::io::Read;
use serde::{Deserialize, Serialize};
use crate::security::pattern_matcher::{PatternMatcher, DetectionResult, SensitiveDataType, SeverityLevel};

/// Configuration for sensitive data detection
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DetectionConfig {
    pub enabled: bool,
    pub max_file_size: usize,  // Maximum file size to scan (in bytes)
    pub enabled_types: Vec<SensitiveDataType>,
    pub excluded_extensions: Vec<String>,
    pub excluded_paths: Vec<String>,
    pub background_scan_enabled: bool,
    pub alert_threshold: SeverityLevel,
}

impl Default for DetectionConfig {
    fn default() -> Self {
        DetectionConfig {
            enabled: true,
            max_file_size: 10 * 1024 * 1024, // 10MB default limit
            enabled_types: vec![
                SensitiveDataType::Password,
                SensitiveDataType::CreditCard,
                SensitiveDataType::SocialSecurityNumber,
                SensitiveDataType::CryptoSeedPhrase,
                SensitiveDataType::<PERSON><PERSON><PERSON><PERSON>,
                SensitiveDataType::AuthToken,
            ],
            excluded_extensions: vec![
                ".exe".to_string(), ".dll".to_string(), ".bin".to_string(),
                ".jpg".to_string(), ".png".to_string(), ".gif".to_string(),
                ".mp4".to_string(), ".mp3".to_string(), ".avi".to_string(),
                ".zip".to_string(), ".rar".to_string(), ".7z".to_string(),
            ],
            excluded_paths: vec![
                "node_modules".to_string(),
                ".git".to_string(),
                "target".to_string(),
                "build".to_string(),
                "dist".to_string(),
            ],
            background_scan_enabled: false, // Opt-in for privacy
            alert_threshold: SeverityLevel::Medium,
        }
    }
}

/// File scan result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileScanResult {
    pub file_path: String,
    pub scan_time: chrono::DateTime<chrono::Utc>,
    pub detections: Vec<DetectionResult>,
    pub file_size: u64,
    pub scan_duration_ms: u64,
    pub error: Option<String>,
}

/// Batch scan result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchScanResult {
    pub total_files_scanned: usize,
    pub total_detections: usize,
    pub high_risk_files: usize,
    pub scan_duration_ms: u64,
    pub files: Vec<FileScanResult>,
}

/// Main sensitive data detector
#[derive(Debug)]
pub struct SensitiveDataDetector {
    pattern_matcher: PatternMatcher,
    config: DetectionConfig,
}

impl SensitiveDataDetector {
    /// Create a minimal detector without pattern matching
    pub fn minimal() -> Result<Self, Box<dyn std::error::Error>> {
        let pattern_matcher = PatternMatcher::minimal()?;
        let config = DetectionConfig::default();

        Ok(Self {
            pattern_matcher,
            config,
        })
    }

    /// Create a new detector with default configuration
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let pattern_matcher = PatternMatcher::new()?;
        let config = DetectionConfig::default();
        
        Ok(SensitiveDataDetector {
            pattern_matcher,
            config,
        })
    }
    
    /// Create a detector with custom configuration
    pub fn with_config(config: DetectionConfig) -> Result<Self, Box<dyn std::error::Error>> {
        let mut pattern_matcher = PatternMatcher::new()?;
        pattern_matcher.set_enabled_types(config.enabled_types.clone());
        
        Ok(SensitiveDataDetector {
            pattern_matcher,
            config,
        })
    }
    
    /// Update the detector configuration
    pub fn update_config(&mut self, config: DetectionConfig) {
        self.pattern_matcher.set_enabled_types(config.enabled_types.clone());
        self.config = config;
    }
    
    /// Scan a single file for sensitive data
    pub fn scan_file<P: AsRef<Path>>(&self, file_path: P) -> FileScanResult {
        let start_time = std::time::Instant::now();
        let file_path = file_path.as_ref();
        let file_path_str = file_path.to_string_lossy().to_string();
        
        // Check if scanning is enabled
        if !self.config.enabled {
            return FileScanResult {
                file_path: file_path_str,
                scan_time: chrono::Utc::now(),
                detections: vec![],
                file_size: 0,
                scan_duration_ms: start_time.elapsed().as_millis() as u64,
                error: Some("Scanning disabled".to_string()),
            };
        }
        
        // Check if file should be excluded
        if self.should_exclude_file(file_path) {
            return FileScanResult {
                file_path: file_path_str,
                scan_time: chrono::Utc::now(),
                detections: vec![],
                file_size: 0,
                scan_duration_ms: start_time.elapsed().as_millis() as u64,
                error: Some("File excluded from scanning".to_string()),
            };
        }
        
        // Get file metadata
        let metadata = match fs::metadata(file_path) {
            Ok(metadata) => metadata,
            Err(e) => {
                return FileScanResult {
                    file_path: file_path_str,
                    scan_time: chrono::Utc::now(),
                    detections: vec![],
                    file_size: 0,
                    scan_duration_ms: start_time.elapsed().as_millis() as u64,
                    error: Some(format!("Failed to read metadata: {e}")),
                };
            }
        };
        
        let file_size = metadata.len();
        
        // Check file size limit
        if file_size > self.config.max_file_size as u64 {
            return FileScanResult {
                file_path: file_path_str,
                scan_time: chrono::Utc::now(),
                detections: vec![],
                file_size,
                scan_duration_ms: start_time.elapsed().as_millis() as u64,
                error: Some("File too large for scanning".to_string()),
            };
        }
        
        // Read and scan file content securely
        let detections = match self.scan_file_content(file_path) {
            Ok(detections) => detections,
            Err(e) => {
                return FileScanResult {
                    file_path: file_path_str,
                    scan_time: chrono::Utc::now(),
                    detections: vec![],
                    file_size,
                    scan_duration_ms: start_time.elapsed().as_millis() as u64,
                    error: Some(format!("Scan error: {e}")),
                };
            }
        };
        
        FileScanResult {
            file_path: file_path_str,
            scan_time: chrono::Utc::now(),
            detections,
            file_size,
            scan_duration_ms: start_time.elapsed().as_millis() as u64,
            error: None,
        }
    }
    
    /// Scan multiple files in batch
    pub fn scan_files<P: AsRef<Path>>(&self, file_paths: Vec<P>) -> BatchScanResult {
        let start_time = std::time::Instant::now();
        let mut results = Vec::new();
        let mut total_detections = 0;
        let mut high_risk_files = 0;
        
        for file_path in file_paths {
            let result = self.scan_file(file_path);
            
            total_detections += result.detections.len();
            
            // Count high-risk files
            if result.detections.iter().any(|d| 
                matches!(d.severity, SeverityLevel::Critical | SeverityLevel::High)
            ) {
                high_risk_files += 1;
            }
            
            results.push(result);
        }
        
        BatchScanResult {
            total_files_scanned: results.len(),
            total_detections,
            high_risk_files,
            scan_duration_ms: start_time.elapsed().as_millis() as u64,
            files: results,
        }
    }
    
    /// Scan directory recursively
    pub fn scan_directory<P: AsRef<Path>>(&self, dir_path: P, recursive: bool) -> BatchScanResult {
        let mut file_paths = Vec::new();
        
        if let Err(e) = self.collect_files(dir_path.as_ref(), recursive, &mut file_paths) {
            // Return empty result with error
            return BatchScanResult {
                total_files_scanned: 0,
                total_detections: 0,
                high_risk_files: 0,
                scan_duration_ms: 0,
                files: vec![FileScanResult {
                    file_path: dir_path.as_ref().to_string_lossy().to_string(),
                    scan_time: chrono::Utc::now(),
                    detections: vec![],
                    file_size: 0,
                    scan_duration_ms: 0,
                    error: Some(format!("Directory scan error: {e}")),
                }],
            };
        }
        
        self.scan_files(file_paths)
    }
    
    /// Check if a file should be excluded from scanning
    fn should_exclude_file(&self, _file_path: &Path) -> bool {
        let file_path_str = _file_path.to_string_lossy().to_lowercase();
        
        // Check excluded paths
        for excluded_path in &self.config.excluded_paths {
            if file_path_str.contains(&excluded_path.to_lowercase()) {
                return true;
            }
        }
        
        // Check excluded extensions
        if let Some(extension) = _file_path.extension() {
            let ext_str = format!(".{}", extension.to_string_lossy().to_lowercase());
            if self.config.excluded_extensions.contains(&ext_str) {
                return true;
            }
        }
        
        false
    }
    
    /// Securely scan file content
    fn scan_file_content(&self, file_path: &Path) -> Result<Vec<DetectionResult>, Box<dyn std::error::Error>> {
        // Read file content with size limit
        let mut file = fs::File::open(file_path)?;
        let metadata = file.metadata()?;

        // Check file size limit
        if metadata.len() > self.config.max_file_size as u64 {
            return Ok(vec![]); // Skip large files
        }

        let mut buffer = Vec::with_capacity(metadata.len() as usize);
        file.read_to_end(&mut buffer)?;

        // Convert to string for pattern matching (if valid UTF-8)
        let content = match std::str::from_utf8(&buffer) {
            Ok(content) => content,
            Err(_) => {
                // If not valid UTF-8, skip scanning (binary file)
                return Ok(vec![]);
            }
        };

        // Perform pattern matching
        let detections = self.pattern_matcher.scan_content(content, &file_path.to_string_lossy());

        Ok(detections)
    }
    
    /// Recursively collect files for scanning
    fn collect_files(&self, dir_path: &Path, recursive: bool, file_paths: &mut Vec<std::path::PathBuf>) -> Result<(), Box<dyn std::error::Error>> {
        let entries = fs::read_dir(dir_path)?;
        
        for entry in entries {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                if !self.should_exclude_file(&path) {
                    file_paths.push(path);
                }
            } else if path.is_dir() && recursive {
                // Skip excluded directories
                if !self.should_exclude_file(&path) {
                    self.collect_files(&path, recursive, file_paths)?;
                }
            }
        }
        
        Ok(())
    }
    
    /// Get current configuration
    pub fn get_config(&self) -> &DetectionConfig {
        &self.config
    }
    
    /// Check if detection is enabled
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }
}

impl Default for SensitiveDataDetector {
    fn default() -> Self {
        Self::new().expect("Failed to create default SensitiveDataDetector")
    }
}
