/**
 * Advanced Accessibility Manager
 * Provides comprehensive accessibility features beyond WCAG 2.1 AA baseline
 */

export interface AccessibilityPreferences {
    reducedMotion: boolean;
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
    keyboardOnly: boolean;
    colorBlindness: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
    focusVisible: boolean;
}

export interface KeyboardShortcut {
    key: string;
    modifiers: string[];
    action: string;
    description: string;
    handler: () => void;
}

export interface FocusableElement {
    element: HTMLElement;
    tabIndex: number;
    role?: string;
    label?: string;
}

class AccessibilityManager {
    private preferences: AccessibilityPreferences;
    private keyboardShortcuts: Map<string, KeyboardShortcut> = new Map();
    private focusHistory: HTMLElement[] = [];
    // private currentFocusIndex: number = -1; // TODO: Implement focus index tracking
    private announcements: string[] = [];
    private liveRegion?: HTMLElement;

    constructor() {
        this.preferences = this.detectAccessibilityPreferences();
        this.initializeLiveRegion();
        this.setupKeyboardNavigation();
        this.setupFocusManagement();
        this.registerDefaultShortcuts();
        this.applyAccessibilityEnhancements();
    }

    /**
     * Detect user accessibility preferences
     */
    private detectAccessibilityPreferences(): AccessibilityPreferences {
        return {
            reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
            highContrast: window.matchMedia('(prefers-contrast: high)').matches,
            largeText: window.matchMedia('(prefers-font-size: large)').matches,
            screenReader: this.detectScreenReader(),
            keyboardOnly: false, // Will be detected based on usage
            colorBlindness: 'none', // Could be set by user preference
            focusVisible: window.matchMedia('(prefers-focus-visible)').matches,
        };
    }

    /**
     * Detect if screen reader is active
     */
    private detectScreenReader(): boolean {
        // Check for common screen reader indicators
        const indicators = [
            'speechSynthesis' in window,
            navigator.userAgent.includes('NVDA'),
            navigator.userAgent.includes('JAWS'),
            navigator.userAgent.includes('VoiceOver'),
            (window as any).speechSynthesis?.getVoices().length > 0
        ];
        
        return indicators.some(indicator => indicator);
    }

    /**
     * Initialize live region for screen reader announcements
     */
    private initializeLiveRegion(): void {
        this.liveRegion = document.createElement('div');
        this.liveRegion.setAttribute('aria-live', 'polite');
        this.liveRegion.setAttribute('aria-atomic', 'true');
        this.liveRegion.className = 'sr-only';
        this.liveRegion.style.cssText = `
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        `;
        
        document.body.appendChild(this.liveRegion);
    }

    /**
     * Setup keyboard navigation
     */
    private setupKeyboardNavigation(): void {
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // Detect keyboard-only usage
        document.addEventListener('mousedown', () => {
            this.preferences.keyboardOnly = false;
        });
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                this.preferences.keyboardOnly = true;
            }
        });
    }

    /**
     * Setup focus management
     */
    private setupFocusManagement(): void {
        document.addEventListener('focusin', this.handleFocusIn.bind(this));
        document.addEventListener('focusout', this.handleFocusOut.bind(this));
        
        // Create focus indicator styles
        this.createFocusStyles();
    }

    /**
     * Create enhanced focus indicator styles
     */
    private createFocusStyles(): void {
        const style = document.createElement('style');
        style.textContent = `
            /* Enhanced focus indicators */
            .focus-visible,
            *:focus-visible {
                outline: 3px solid #4A90E2 !important;
                outline-offset: 2px !important;
                border-radius: 4px !important;
            }
            
            /* High contrast focus indicators */
            @media (prefers-contrast: high) {
                .focus-visible,
                *:focus-visible {
                    outline: 4px solid HighlightText !important;
                    outline-offset: 2px !important;
                    background: Highlight !important;
                    color: HighlightText !important;
                }
            }
            
            /* Skip links */
            .skip-link {
                position: absolute;
                top: -40px;
                left: 6px;
                background: #000;
                color: #fff;
                padding: 8px;
                text-decoration: none;
                border-radius: 4px;
                z-index: 10000;
                transition: top 0.3s;
            }
            
            .skip-link:focus {
                top: 6px;
            }
            
            /* Screen reader only content */
            .sr-only {
                position: absolute !important;
                width: 1px !important;
                height: 1px !important;
                padding: 0 !important;
                margin: -1px !important;
                overflow: hidden !important;
                clip: rect(0, 0, 0, 0) !important;
                white-space: nowrap !important;
                border: 0 !important;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * Register default keyboard shortcuts
     */
    private registerDefaultShortcuts(): void {
        this.registerShortcut({
            key: '/',
            modifiers: [],
            action: 'search',
            description: 'Focus search input',
            handler: () => this.focusSearch()
        });

        this.registerShortcut({
            key: 'h',
            modifiers: ['alt'],
            action: 'home',
            description: 'Go to home/main view',
            handler: () => this.navigateToHome()
        });

        this.registerShortcut({
            key: 'c',
            modifiers: ['alt'],
            action: 'configuration',
            description: 'Open configuration panel',
            handler: () => this.navigateToConfiguration()
        });

        this.registerShortcut({
            key: 'r',
            modifiers: ['alt'],
            action: 'results',
            description: 'View scan results',
            handler: () => this.navigateToResults()
        });

        this.registerShortcut({
            key: 'Escape',
            modifiers: [],
            action: 'close',
            description: 'Close current dialog or panel',
            handler: () => this.closeCurrentDialog()
        });

        this.registerShortcut({
            key: '?',
            modifiers: ['shift'],
            action: 'help',
            description: 'Show keyboard shortcuts help',
            handler: () => this.showKeyboardHelp()
        });
    }

    /**
     * Handle keydown events
     */
    private handleKeyDown(event: KeyboardEvent): void {
        const shortcutKey = this.getShortcutKey(event);
        const shortcut = this.keyboardShortcuts.get(shortcutKey);
        
        if (shortcut) {
            event.preventDefault();
            shortcut.handler();
            this.announce(`Activated ${shortcut.description}`);
        }
    }

    /**
     * Handle keyup events
     */
    private handleKeyUp(_event: KeyboardEvent): void {
        // Handle any keyup-specific logic
    }

    /**
     * Handle focus in events
     */
    private handleFocusIn(event: FocusEvent): void {
        const target = event.target as HTMLElement;
        if (target && target !== document.body) {
            this.focusHistory.push(target);
            // this.currentFocusIndex = this.focusHistory.length - 1; // TODO: Implement focus index tracking
            
            // Announce focused element for screen readers
            if (this.preferences.screenReader) {
                this.announceFocusedElement(target);
            }
        }
    }

    /**
     * Handle focus out events
     */
    private handleFocusOut(_event: FocusEvent): void {
        // Handle any focus out logic
    }

    /**
     * Announce focused element
     */
    private announceFocusedElement(element: HTMLElement): void {
        const role = element.getAttribute('role') || element.tagName.toLowerCase();
        const label = element.getAttribute('aria-label') || 
                     element.getAttribute('title') || 
                     (element as HTMLInputElement).placeholder ||
                     element.textContent?.trim();
        
        if (label) {
            this.announce(`${role}: ${label}`);
        }
    }

    /**
     * Get shortcut key string
     */
    private getShortcutKey(event: KeyboardEvent): string {
        const modifiers = [];
        if (event.ctrlKey) modifiers.push('ctrl');
        if (event.altKey) modifiers.push('alt');
        if (event.shiftKey) modifiers.push('shift');
        if (event.metaKey) modifiers.push('meta');
        
        return [...modifiers, event.key.toLowerCase()].join('+');
    }

    /**
     * Register a keyboard shortcut
     */
    public registerShortcut(shortcut: KeyboardShortcut): void {
        const key = [...shortcut.modifiers, shortcut.key.toLowerCase()].join('+');
        this.keyboardShortcuts.set(key, shortcut);
    }

    /**
     * Announce message to screen readers
     */
    public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
        if (!this.liveRegion) return;
        
        this.announcements.push(message);
        this.liveRegion.setAttribute('aria-live', priority);
        this.liveRegion.textContent = message;
        
        // Clear after announcement
        setTimeout(() => {
            if (this.liveRegion) {
                this.liveRegion.textContent = '';
            }
        }, 1000);
    }

    /**
     * Focus search input
     */
    private focusSearch(): void {
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLElement;
        if (searchInput) {
            searchInput.focus();
        }
    }

    /**
     * Navigate to home
     */
    private navigateToHome(): void {
        const homeButton = document.querySelector('[data-view="scanner"], [data-view="guided-workflow"]') as HTMLElement;
        if (homeButton) {
            homeButton.click();
        }
    }

    /**
     * Navigate to configuration
     */
    private navigateToConfiguration(): void {
        const configButton = document.querySelector('[data-view="configuration"]') as HTMLElement;
        if (configButton) {
            configButton.click();
        }
    }

    /**
     * Navigate to results
     */
    private navigateToResults(): void {
        const resultsButton = document.querySelector('[data-view="results"]') as HTMLElement;
        if (resultsButton) {
            resultsButton.click();
        }
    }

    /**
     * Close current dialog
     */
    private closeCurrentDialog(): void {
        const closeButton = document.querySelector('[aria-label="Close"], .dialog-close, [data-action="close"]') as HTMLElement;
        if (closeButton) {
            closeButton.click();
        }
    }

    /**
     * Show keyboard shortcuts help
     */
    private showKeyboardHelp(): void {
        const shortcuts = Array.from(this.keyboardShortcuts.values());
        const helpText = shortcuts.map(s => 
            `${s.modifiers.join('+')}${s.modifiers.length ? '+' : ''}${s.key}: ${s.description}`
        ).join('\n');
        
        this.announce(`Keyboard shortcuts: ${helpText}`, 'assertive');
    }

    /**
     * Apply accessibility enhancements
     */
    private applyAccessibilityEnhancements(): void {
        // Add skip links
        this.addSkipLinks();
        
        // Enhance form labels
        this.enhanceFormLabels();
        
        // Add ARIA landmarks
        this.addAriaLandmarks();
        
        // Apply color blindness support if needed
        if (this.preferences.colorBlindness !== 'none') {
            this.applyColorBlindnessSupport();
        }
    }

    /**
     * Add skip links for keyboard navigation
     */
    private addSkipLinks(): void {
        const skipLinks = document.createElement('div');
        skipLinks.innerHTML = `
            <a href="#main-content" class="skip-link">Skip to main content</a>
            <a href="#navigation" class="skip-link">Skip to navigation</a>
            <a href="#search" class="skip-link">Skip to search</a>
        `;
        
        document.body.insertBefore(skipLinks, document.body.firstChild);
    }

    /**
     * Enhance form labels
     */
    private enhanceFormLabels(): void {
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (!input.getAttribute('aria-label') && !input.getAttribute('aria-labelledby')) {
                const placeholder = input.getAttribute('placeholder');
                if (placeholder) {
                    input.setAttribute('aria-label', placeholder);
                }
            }
        });
    }

    /**
     * Add ARIA landmarks
     */
    private addAriaLandmarks(): void {
        // Add main landmark
        const main = document.querySelector('main');
        if (main && !main.getAttribute('role')) {
            main.setAttribute('role', 'main');
            main.id = 'main-content';
        }
        
        // Add navigation landmark
        const nav = document.querySelector('nav');
        if (nav && !nav.getAttribute('role')) {
            nav.setAttribute('role', 'navigation');
            nav.id = 'navigation';
        }
    }

    /**
     * Apply color blindness support
     */
    private applyColorBlindnessSupport(): void {
        const style = document.createElement('style');
        let filters = '';
        
        switch (this.preferences.colorBlindness) {
            case 'protanopia':
                filters = 'filter: url(#protanopia);';
                break;
            case 'deuteranopia':
                filters = 'filter: url(#deuteranopia);';
                break;
            case 'tritanopia':
                filters = 'filter: url(#tritanopia);';
                break;
        }
        
        if (filters) {
            style.textContent = `html { ${filters} }`;
            document.head.appendChild(style);
        }
    }

    /**
     * Get accessibility preferences
     */
    public getPreferences(): AccessibilityPreferences {
        return { ...this.preferences };
    }

    /**
     * Update accessibility preferences
     */
    public updatePreferences(updates: Partial<AccessibilityPreferences>): void {
        this.preferences = { ...this.preferences, ...updates };
        this.applyAccessibilityEnhancements();
    }

    /**
     * Get all registered shortcuts
     */
    public getShortcuts(): KeyboardShortcut[] {
        return Array.from(this.keyboardShortcuts.values());
    }

    /**
     * Cleanup resources
     */
    public cleanup(): void {
        if (this.liveRegion) {
            document.body.removeChild(this.liveRegion);
        }
    }
}

// Create singleton instance
export const accessibilityManager = new AccessibilityManager();

// Export utility functions
export const announce = (message: string, priority?: 'polite' | 'assertive') => 
    accessibilityManager.announce(message, priority);

export const registerShortcut = (shortcut: KeyboardShortcut) => 
    accessibilityManager.registerShortcut(shortcut);

export const getAccessibilityPreferences = () => 
    accessibilityManager.getPreferences();
