import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Theme, ThemeMode, getTheme, generateThemeCSS } from './theme';

interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setThemeMode: (mode: ThemeMode) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
  defaultMode?: ThemeMode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  defaultMode = 'light' 
}) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    // Try to get saved theme from localStorage
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('privacyai-theme');
      if (saved === 'light' || saved === 'dark') {
        return saved;
      }
    }
    return defaultMode;
  });

  const theme = getTheme(themeMode);

  const toggleTheme = () => {
    setThemeMode(prev => prev === 'light' ? 'dark' : 'light');
  };

  // Save theme preference to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('privacyai-theme', themeMode);
    }
  }, [themeMode]);

  // Apply theme CSS custom properties
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const styleElement = document.getElementById('theme-variables');
      if (styleElement) {
        styleElement.textContent = generateThemeCSS(theme);
      } else {
        const newStyleElement = document.createElement('style');
        newStyleElement.id = 'theme-variables';
        newStyleElement.textContent = generateThemeCSS(theme);
        document.head.appendChild(newStyleElement);
      }
      
      // Add theme class to body for CSS targeting
      document.body.className = document.body.className.replace(/theme-\w+/g, '');
      document.body.classList.add(`theme-${themeMode}`);
    }
  }, [theme, themeMode]);

  const contextValue: ThemeContextType = {
    theme,
    themeMode,
    toggleTheme,
    setThemeMode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Utility hook for getting themed styles
export const useThemedStyles = () => {
  const { theme } = useTheme();
  
  return {
    // Button styles
    primaryButton: {
      padding: '10px 20px',
      background: theme.colors.primary[600],
      color: theme.colors.text.inverse,
      border: 'none',
      borderRadius: '5px',
      cursor: 'pointer',
      fontWeight: '500',
      transition: 'all 0.2s ease',
    },
    
    secondaryButton: {
      padding: '10px 20px',
      background: theme.colors.gray[500],
      color: theme.colors.text.inverse,
      border: 'none',
      borderRadius: '5px',
      cursor: 'pointer',
      fontWeight: '500',
      transition: 'all 0.2s ease',
    },
    
    dangerButton: {
      padding: '10px 20px',
      background: theme.colors.error[600],
      color: theme.colors.text.inverse,
      border: 'none',
      borderRadius: '5px',
      cursor: 'pointer',
      fontWeight: '500',
      transition: 'all 0.2s ease',
    },
    
    // Text styles
    primaryText: {
      color: theme.colors.text.primary,
    },
    
    secondaryText: {
      color: theme.colors.text.secondary,
    },
    
    tertiaryText: {
      color: theme.colors.text.tertiary,
    },
    
    // Card styles
    card: {
      background: theme.colors.background.primary,
      border: `1px solid ${theme.colors.gray[200]}`,
      borderRadius: '8px',
      padding: '20px',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    },
    
    // Input styles
    input: {
      padding: '8px 12px',
      border: `1px solid ${theme.colors.gray[300]}`,
      borderRadius: '4px',
      background: theme.colors.background.primary,
      color: theme.colors.text.primary,
      fontSize: '14px',
    },
    
    // Container styles
    container: {
      background: theme.colors.background.secondary,
      minHeight: '100vh',
    },
    
    // Section styles
    section: {
      background: theme.colors.background.primary,
      border: `1px solid ${theme.colors.gray[200]}`,
      borderRadius: '8px',
      padding: '20px',
      marginBottom: '20px',
    },
  };
};

// Theme toggle button component
export const ThemeToggle: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { themeMode, toggleTheme } = useTheme();
  const styles = useThemedStyles();
  
  return (
    <button
      onClick={toggleTheme}
      style={{
        ...styles.secondaryButton,
        padding: '8px 12px',
        fontSize: '12px',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
      }}
      className={className}
      title={`Switch to ${themeMode === 'light' ? 'dark' : 'light'} theme`}
    >
      {themeMode === 'light' ? '🌙' : '☀️'}
      {themeMode === 'light' ? 'Dark' : 'Light'}
    </button>
  );
};
