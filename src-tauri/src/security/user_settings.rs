/// User settings configuration for cryptocurrency and security detection
/// 
/// This module provides comprehensive user settings management for:
/// - Cryptocurrency detection inclusion/exclusion controls
/// - Search scope and detection method configuration
/// - Sensitivity and confidence threshold adjustments
/// - Performance mode selection
/// - File type filtering and exclusion rules
/// 
/// ## Settings Persistence
/// Settings are stored persistently using the Tauri app data directory
/// and automatically loaded on application startup.
/// 
/// ## Configuration Categories
/// 
/// ### Detection Controls
/// - Enable/disable specific cryptocurrency types
/// - Toggle wallet detection methods
/// - Configure corruption detection sensitivity
/// 
/// ### Performance Settings
/// - Choose between fast vs comprehensive scanning
/// - Set processing timeouts and limits
/// - Configure cache behavior
/// 
/// ### File Filtering
/// - Include/exclude specific file extensions
/// - Set file size limits for scanning
/// - Configure directory exclusions

use serde::{Serialize, Deserialize};
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;
use thiserror::Error;
use super::cryptocurrency::patterns::CryptoPatternType;

/// User settings errors
#[derive(Debug, Error, Clone, Serialize, Deserialize)]
pub enum SettingsError {
    #[error("Failed to load settings: {message}")]
    LoadError { message: String },
    
    #[error("Failed to save settings: {message}")]
    SaveError { message: String },
    
    #[error("Invalid setting value: {setting} = {value}")]
    InvalidValue { setting: String, value: String },
    
    #[error("Settings file not found")]
    FileNotFound,
}

/// Performance modes for scanning
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PerformanceMode {
    Fast,           // Path-based detection only
    Balanced,       // Path + limited content analysis
    Comprehensive,  // Full analysis with validation
    Custom,         // User-defined settings
}

/// Detection method configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionMethodSettings {
    pub path_based_detection: bool,
    pub content_analysis: bool,
    pub mnemonic_detection: bool,
    pub wallet_file_detection: bool,
    pub corruption_detection: bool,
}

/// Sensitivity configuration for different detection types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SensitivitySettings {
    pub cryptocurrency_confidence_threshold: f32,
    pub wallet_confidence_threshold: f32,
    pub corruption_entropy_threshold: f64,
    pub corruption_special_char_threshold: f64,
    pub mnemonic_word_validity_threshold: f32,
}

/// File filtering configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileFilterSettings {
    pub included_extensions: HashSet<String>,
    pub excluded_extensions: HashSet<String>,
    pub max_file_size_mb: u64,
    pub excluded_directories: HashSet<String>,
    pub scan_hidden_files: bool,
    pub scan_system_files: bool,
}

/// Main user settings configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSettings {
    /// Version of settings format
    pub version: String,
    
    /// Performance mode selection
    pub performance_mode: PerformanceMode,
    
    /// Enabled cryptocurrency pattern types
    pub enabled_crypto_patterns: HashSet<CryptoPatternType>,
    
    /// Detection method configuration
    pub detection_methods: DetectionMethodSettings,
    
    /// Sensitivity settings
    pub sensitivity: SensitivitySettings,
    
    /// File filtering settings
    pub file_filters: FileFilterSettings,
    
    /// Custom user preferences
    pub custom_settings: HashMap<String, String>,
    
    /// Last updated timestamp
    pub last_updated: String,
}

/// User settings manager
pub struct UserSettingsManager {
    settings: UserSettings,
    settings_file_path: PathBuf,
}

impl UserSettingsManager {
    /// Create a new settings manager with default settings
    pub fn new() -> Result<Self, SettingsError> {
        let settings_file_path = Self::get_settings_file_path()?;
        let settings = Self::load_or_create_default(&settings_file_path)?;
        
        Ok(Self {
            settings,
            settings_file_path,
        })
    }
    
    /// Load settings from file or create default if not found
    fn load_or_create_default(file_path: &PathBuf) -> Result<UserSettings, SettingsError> {
        if file_path.exists() {
            Self::load_from_file(file_path)
        } else {
            Ok(Self::create_default_settings())
        }
    }
    
    /// Load settings from file
    fn load_from_file(file_path: &PathBuf) -> Result<UserSettings, SettingsError> {
        let content = std::fs::read_to_string(file_path)
            .map_err(|e| SettingsError::LoadError { 
                message: format!("Failed to read settings file: {}", e) 
            })?;
        
        let settings: UserSettings = serde_json::from_str(&content)
            .map_err(|e| SettingsError::LoadError { 
                message: format!("Failed to parse settings JSON: {}", e) 
            })?;
        
        Ok(settings)
    }
    
    /// Create default settings
    fn create_default_settings() -> UserSettings {
        let mut enabled_crypto_patterns = HashSet::new();
        
        // Enable all cryptocurrency patterns by default
        enabled_crypto_patterns.insert(CryptoPatternType::BitcoinWIF);
        enabled_crypto_patterns.insert(CryptoPatternType::BitcoinWIFCompressed);
        enabled_crypto_patterns.insert(CryptoPatternType::EthereumPrivateKey);
        enabled_crypto_patterns.insert(CryptoPatternType::RawPrivateKey);
        enabled_crypto_patterns.insert(CryptoPatternType::BitcoinLegacy);
        enabled_crypto_patterns.insert(CryptoPatternType::BitcoinSegWit);
        enabled_crypto_patterns.insert(CryptoPatternType::BitcoinBech32);
        enabled_crypto_patterns.insert(CryptoPatternType::EthereumAddress);
        enabled_crypto_patterns.insert(CryptoPatternType::MoneroAddress);
        enabled_crypto_patterns.insert(CryptoPatternType::LitecoinAddress);
        enabled_crypto_patterns.insert(CryptoPatternType::CardanoAddress);
        enabled_crypto_patterns.insert(CryptoPatternType::BinanceApiKey);
        enabled_crypto_patterns.insert(CryptoPatternType::CoinbaseApiKey);
        enabled_crypto_patterns.insert(CryptoPatternType::KrakenApiKey);
        
        let mut included_extensions = HashSet::new();
        // Common file extensions to scan
        for ext in &["txt", "doc", "docx", "pdf", "json", "xml", "csv", "dat", "wallet", "key", "keystore"] {
            included_extensions.insert(ext.to_string());
        }
        
        let mut excluded_directories = HashSet::new();
        // Common directories to exclude
        for dir in &["node_modules", ".git", "target", "build", "dist", "cache"] {
            excluded_directories.insert(dir.to_string());
        }
        
        UserSettings {
            version: "1.0".to_string(),
            performance_mode: PerformanceMode::Balanced,
            enabled_crypto_patterns,
            detection_methods: DetectionMethodSettings {
                path_based_detection: true,
                content_analysis: true,
                mnemonic_detection: true,
                wallet_file_detection: true,
                corruption_detection: true,
            },
            sensitivity: SensitivitySettings {
                cryptocurrency_confidence_threshold: 0.8,
                wallet_confidence_threshold: 0.7,
                corruption_entropy_threshold: 4.0,
                corruption_special_char_threshold: 0.3,
                mnemonic_word_validity_threshold: 0.8,
            },
            file_filters: FileFilterSettings {
                included_extensions,
                excluded_extensions: HashSet::new(),
                max_file_size_mb: 100,
                excluded_directories,
                scan_hidden_files: false,
                scan_system_files: false,
            },
            custom_settings: HashMap::new(),
            last_updated: chrono::Utc::now().to_rfc3339(),
        }
    }
    
    /// Get the settings file path
    fn get_settings_file_path() -> Result<PathBuf, SettingsError> {
        // Use a simple approach for settings directory
        let home_dir = std::env::var("HOME")
            .or_else(|_| std::env::var("USERPROFILE"))
            .map_err(|_| SettingsError::LoadError {
                message: "Failed to get home directory".to_string()
            })?;

        let settings_dir = PathBuf::from(home_dir).join(".privacyai");
        std::fs::create_dir_all(&settings_dir)
            .map_err(|e| SettingsError::LoadError {
                message: format!("Failed to create settings directory: {}", e)
            })?;

        Ok(settings_dir.join("user_settings.json"))
    }
    
    /// Save current settings to file
    pub fn save(&self) -> Result<(), SettingsError> {
        let json = serde_json::to_string_pretty(&self.settings)
            .map_err(|e| SettingsError::SaveError { 
                message: format!("Failed to serialize settings: {}", e) 
            })?;
        
        std::fs::write(&self.settings_file_path, json)
            .map_err(|e| SettingsError::SaveError { 
                message: format!("Failed to write settings file: {}", e) 
            })?;
        
        Ok(())
    }
    
    /// Get current settings
    pub fn get_settings(&self) -> &UserSettings {
        &self.settings
    }
    
    /// Update performance mode
    pub fn set_performance_mode(&mut self, mode: PerformanceMode) -> Result<(), SettingsError> {
        self.settings.performance_mode = mode.clone();
        
        // Automatically adjust detection methods based on performance mode
        match mode {
            PerformanceMode::Fast => {
                self.settings.detection_methods.path_based_detection = true;
                self.settings.detection_methods.content_analysis = false;
                self.settings.detection_methods.mnemonic_detection = false;
                self.settings.detection_methods.wallet_file_detection = false;
                self.settings.detection_methods.corruption_detection = false;
            }
            PerformanceMode::Balanced => {
                self.settings.detection_methods.path_based_detection = true;
                self.settings.detection_methods.content_analysis = true;
                self.settings.detection_methods.mnemonic_detection = true;
                self.settings.detection_methods.wallet_file_detection = true;
                self.settings.detection_methods.corruption_detection = false;
            }
            PerformanceMode::Comprehensive => {
                self.settings.detection_methods.path_based_detection = true;
                self.settings.detection_methods.content_analysis = true;
                self.settings.detection_methods.mnemonic_detection = true;
                self.settings.detection_methods.wallet_file_detection = true;
                self.settings.detection_methods.corruption_detection = true;
            }
            PerformanceMode::Custom => {
                // Don't change detection methods for custom mode
            }
        }
        
        self.update_timestamp();
        Ok(())
    }
    
    /// Enable/disable specific cryptocurrency pattern
    pub fn set_crypto_pattern_enabled(&mut self, pattern: CryptoPatternType, enabled: bool) {
        if enabled {
            self.settings.enabled_crypto_patterns.insert(pattern);
        } else {
            self.settings.enabled_crypto_patterns.remove(&pattern);
        }
        self.update_timestamp();
    }
    
    /// Update sensitivity settings
    pub fn update_sensitivity(&mut self, sensitivity: SensitivitySettings) {
        self.settings.sensitivity = sensitivity;
        self.update_timestamp();
    }
    
    /// Update file filter settings
    pub fn update_file_filters(&mut self, filters: FileFilterSettings) {
        self.settings.file_filters = filters;
        self.update_timestamp();
    }
    
    /// Add custom setting
    pub fn set_custom_setting(&mut self, key: String, value: String) {
        self.settings.custom_settings.insert(key, value);
        self.update_timestamp();
    }
    
    /// Get custom setting
    pub fn get_custom_setting(&self, key: &str) -> Option<&String> {
        self.settings.custom_settings.get(key)
    }
    
    /// Check if cryptocurrency pattern is enabled
    pub fn is_crypto_pattern_enabled(&self, pattern: &CryptoPatternType) -> bool {
        self.settings.enabled_crypto_patterns.contains(pattern)
    }
    
    /// Check if file should be scanned based on filters
    pub fn should_scan_file(&self, file_path: &str, file_size_mb: u64) -> bool {
        // Check file size limit
        if file_size_mb > self.settings.file_filters.max_file_size_mb {
            return false;
        }
        
        // Check if file is hidden and hidden files are disabled
        if !self.settings.file_filters.scan_hidden_files {
            if let Some(filename) = std::path::Path::new(file_path).file_name() {
                if filename.to_string_lossy().starts_with('.') {
                    return false;
                }
            }
        }
        
        // Check file extension
        if let Some(extension) = std::path::Path::new(file_path).extension() {
            let ext = extension.to_string_lossy().to_lowercase();
            
            // If excluded extensions list is not empty and contains this extension
            if !self.settings.file_filters.excluded_extensions.is_empty() &&
               self.settings.file_filters.excluded_extensions.contains(&ext) {
                return false;
            }
            
            // If included extensions list is not empty and doesn't contain this extension
            if !self.settings.file_filters.included_extensions.is_empty() &&
               !self.settings.file_filters.included_extensions.contains(&ext) {
                return false;
            }
        }
        
        // Check excluded directories
        for excluded_dir in &self.settings.file_filters.excluded_directories {
            if file_path.contains(excluded_dir) {
                return false;
            }
        }
        
        true
    }
    
    /// Update timestamp
    fn update_timestamp(&mut self) {
        self.settings.last_updated = chrono::Utc::now().to_rfc3339();
    }
    
    /// Reset to default settings
    pub fn reset_to_defaults(&mut self) {
        self.settings = Self::create_default_settings();
    }
    
    /// Export settings to JSON string
    pub fn export_settings(&self) -> Result<String, SettingsError> {
        serde_json::to_string_pretty(&self.settings)
            .map_err(|e| SettingsError::SaveError { 
                message: format!("Failed to export settings: {}", e) 
            })
    }
    
    /// Import settings from JSON string
    pub fn import_settings(&mut self, json: &str) -> Result<(), SettingsError> {
        let imported_settings: UserSettings = serde_json::from_str(json)
            .map_err(|e| SettingsError::LoadError { 
                message: format!("Failed to parse imported settings: {}", e) 
            })?;
        
        self.settings = imported_settings;
        self.update_timestamp();
        Ok(())
    }
}

impl Default for UserSettingsManager {
    fn default() -> Self {
        Self::new().expect("Failed to create default settings manager")
    }
}
