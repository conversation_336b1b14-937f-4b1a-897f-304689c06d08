/**
 * Tesseract.js Integration for PrivacyAI
 * 
 * Provides OCR capabilities with performance optimization and error handling
 */

import { create<PERSON><PERSON><PERSON>, Worker, RecognizeR<PERSON>ult } from 'tesseract.js';
import { invoke } from '@tauri-apps/api/core';

export interface OCRRequest {
  image_data: string;
  format: string;
  config: OCRConfig;
  preprocess: boolean;
  language: string;
}

export interface OCRConfig {
  language: string;
  confidence_threshold: number;
  max_file_size: number;
  enable_preprocessing: boolean;
}

export interface OCRResponse {
  success: boolean;
  text: string;
  confidence: number;
  processing_time_ms: number;
  word_count: number;
  detected_language?: string;
  preprocessing_applied: string[];
  error?: string;
  performance_metrics: OCRPerformanceMetrics;
}

export interface OCRPerformanceMetrics {
  preprocessing_time_ms: number;
  ocr_time_ms: number;
  total_time_ms: number;
  image_dimensions: [number, number];
  image_size_bytes: number;
  memory_usage_bytes: number;
}

/**
 * Tesseract.js OCR Engine with Tauri integration
 */
export class TesseractOCREngine {
  private worker: Worker | null = null;
  private isInitialized = false;
  private performanceMetrics: Map<string, number> = new Map();

  constructor(private config: OCRConfig = {
    language: 'eng',
    confidence_threshold: 0.7,
    max_file_size: 50 * 1024 * 1024, // 50MB
    enable_preprocessing: true
  }) {}

  /**
   * Initialize Tesseract worker
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🔍 Initializing Tesseract.js OCR Engine');
    const startTime = performance.now();

    try {
      this.worker = await createWorker(this.config.language, 1, {
        logger: (m) => {
          if (m.status === 'recognizing text') {
            console.log(`📊 OCR Progress: ${Math.round(m.progress * 100)}%`);
          }
        }
      });

      await this.worker.setParameters({
        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?@#$%^&*()-_=+[]{}|;:\'\"<>/\\`~',
        tessedit_pageseg_mode: 6 as any, // Uniform block of text
        preserve_interword_spaces: '1',
      });

      this.isInitialized = true;
      const initTime = performance.now() - startTime;
      this.performanceMetrics.set('initialization_time_ms', initTime);
      
      console.log(`✅ Tesseract.js initialized in ${initTime.toFixed(2)}ms`);
    } catch (error) {
      console.error('❌ Failed to initialize Tesseract.js:', error);
      throw new Error(`OCR initialization failed: ${error}`);
    }
  }

  /**
   * Process OCR request with Rust preprocessing
   */
  async processOCR(imageData: string, format: string, language: string = 'eng'): Promise<OCRResponse> {
    const totalStartTime = performance.now();

    try {
      // Ensure worker is initialized
      await this.initialize();

      if (!this.worker) {
        throw new Error('OCR worker not initialized');
      }

      // Send to Rust for preprocessing
      const preprocessRequest: OCRRequest = {
        image_data: imageData,
        format: format,
        config: this.config,
        preprocess: this.config.enable_preprocessing,
        language: language
      };

      console.log('🔍 Sending image for preprocessing...');
      const preprocessResponse = await invoke<OCRResponse>('process_ocr', { request: preprocessRequest });

      if (!preprocessResponse.success) {
        throw new Error(preprocessResponse.error || 'Preprocessing failed');
      }

      // Use preprocessed image data for OCR
      const processedImageData = preprocessResponse.text; // Contains base64 processed image
      
      // Perform OCR with Tesseract.js
      const ocrStartTime = performance.now();
      console.log('🔍 Starting OCR recognition...');
      
      const result: RecognizeResult = await this.worker.recognize(
        `data:image/${format};base64,${processedImageData}`,
        {
          rectangle: undefined, // Process entire image
        }
      );

      const ocrTime = performance.now() - ocrStartTime;
      const totalTime = performance.now() - totalStartTime;

      // Extract text and confidence
      const extractedText = result.data.text.trim();
      const confidence = result.data.confidence / 100; // Convert to 0-1 scale
      const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;

      console.log(`✅ OCR completed: ${wordCount} words, confidence: ${(confidence * 100).toFixed(1)}%`);

      // Update performance metrics
      this.performanceMetrics.set('last_ocr_time_ms', ocrTime);
      this.performanceMetrics.set('last_total_time_ms', totalTime);

      // Create final response
      const response: OCRResponse = {
        success: true,
        text: extractedText,
        confidence: confidence,
        processing_time_ms: totalTime,
        word_count: wordCount,
        detected_language: result.data.text.length > 0 ? language : undefined,
        preprocessing_applied: preprocessResponse.preprocessing_applied,
        error: undefined,
        performance_metrics: {
          preprocessing_time_ms: preprocessResponse.performance_metrics.preprocessing_time_ms,
          ocr_time_ms: ocrTime,
          total_time_ms: totalTime,
          image_dimensions: preprocessResponse.performance_metrics.image_dimensions,
          image_size_bytes: preprocessResponse.performance_metrics.image_size_bytes,
          memory_usage_bytes: this.estimateMemoryUsage(result)
        }
      };

      // Validate confidence threshold
      if (confidence < this.config.confidence_threshold) {
        console.warn(`⚠️ Low confidence OCR result: ${(confidence * 100).toFixed(1)}% < ${(this.config.confidence_threshold * 100).toFixed(1)}%`);
        response.error = `Low confidence: ${(confidence * 100).toFixed(1)}%`;
      }

      return response;

    } catch (error) {
      const totalTime = performance.now() - totalStartTime;
      console.error('❌ OCR processing failed:', error);

      return {
        success: false,
        text: '',
        confidence: 0,
        processing_time_ms: totalTime,
        word_count: 0,
        detected_language: undefined,
        preprocessing_applied: [],
        error: error instanceof Error ? error.message : String(error),
        performance_metrics: {
          preprocessing_time_ms: 0,
          ocr_time_ms: 0,
          total_time_ms: totalTime,
          image_dimensions: [0, 0],
          image_size_bytes: 0,
          memory_usage_bytes: 0
        }
      };
    }
  }

  /**
   * Process multiple images in batch
   */
  async processBatch(images: Array<{ data: string; format: string; id: string }>): Promise<Map<string, OCRResponse>> {
    const results = new Map<string, OCRResponse>();
    const batchStartTime = performance.now();

    console.log(`🔍 Processing OCR batch: ${images.length} images`);

    for (const image of images) {
      try {
        const result = await this.processOCR(image.data, image.format);
        results.set(image.id, result);
        
        console.log(`✅ Batch item ${image.id}: ${result.success ? 'success' : 'failed'}`);
      } catch (error) {
        console.error(`❌ Batch item ${image.id} failed:`, error);
        results.set(image.id, {
          success: false,
          text: '',
          confidence: 0,
          processing_time_ms: 0,
          word_count: 0,
          preprocessing_applied: [],
          error: error instanceof Error ? error.message : String(error),
          performance_metrics: {
            preprocessing_time_ms: 0,
            ocr_time_ms: 0,
            total_time_ms: 0,
            image_dimensions: [0, 0],
            image_size_bytes: 0,
            memory_usage_bytes: 0
          }
        });
      }
    }

    const batchTime = performance.now() - batchStartTime;
    console.log(`✅ OCR batch completed in ${batchTime.toFixed(2)}ms`);

    return results;
  }

  /**
   * Update OCR configuration
   */
  async updateConfig(newConfig: Partial<OCRConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    
    // Send configuration to Rust backend
    await invoke('configure_ocr', { config: this.config });
    
    // Reinitialize worker if language changed
    if (newConfig.language && newConfig.language !== this.config.language) {
      await this.terminate();
      this.isInitialized = false;
      await this.initialize();
    }

    console.log('✅ OCR configuration updated:', this.config);
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): Record<string, number> {
    const metrics: Record<string, number> = {};
    this.performanceMetrics.forEach((value, key) => {
      metrics[key] = value;
    });
    return metrics;
  }

  /**
   * Estimate memory usage from OCR result
   */
  private estimateMemoryUsage(result: RecognizeResult): number {
    // Estimate based on text length and recognition data
    const textSize = result.data.text.length * 2; // UTF-16 encoding
    const wordsSize = (result.data as any).words?.length * 100 || 0; // Approximate word object size
    const linesSize = (result.data as any).lines?.length * 50 || 0; // Approximate line object size
    
    return textSize + wordsSize + linesSize;
  }

  /**
   * Terminate OCR worker
   */
  async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      console.log('🔍 Tesseract.js worker terminated');
    }
  }

  /**
   * Check if OCR engine is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.worker !== null;
  }
}

/**
 * Global OCR engine instance
 */
export const ocrEngine = new TesseractOCREngine();

/**
 * Convenience function for single image OCR
 */
export async function extractTextFromImage(
  imageData: string, 
  format: string, 
  language: string = 'eng'
): Promise<OCRResponse> {
  return await ocrEngine.processOCR(imageData, format, language);
}

/**
 * Convenience function for batch OCR processing
 */
export async function extractTextFromImages(
  images: Array<{ data: string; format: string; id: string }>
): Promise<Map<string, OCRResponse>> {
  return await ocrEngine.processBatch(images);
}
