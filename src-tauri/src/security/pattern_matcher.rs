use regex::Regex;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Types of sensitive data that can be detected
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SensitiveDataType {
    Password,
    CreditCard,
    SocialSecurityNumber,
    PassportNumber,
    DriverLicense,
    CryptoSeedPhrase,
    ApiKey,
    AuthToken,
    Email,
    PhoneNumber,
    Address,
    BankAccount,
    TaxId,
}

/// Severity levels for detected sensitive data
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum SeverityLevel {
    Critical,  // Unencrypted files on desktop/downloads
    High,      // Unencrypted files in documents
    Medium,    // Files in user directories
    Low,       // Files in secure locations
}

/// Detection result without storing actual sensitive content
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionResult {
    pub data_type: SensitiveDataType,
    pub severity: SeverityLevel,
    pub line_number: Option<usize>,
    pub column_start: Option<usize>,
    pub column_end: Option<usize>,
    pub confidence: f32,  // 0.0 to 1.0
    pub context_hint: String,  // Safe context description, no actual data
}

/// Pattern matcher for sensitive data detection
#[derive(Debug)]
pub struct PatternMatcher {
    patterns: HashMap<SensitiveDataType, Vec<Regex>>,
    enabled_types: Vec<SensitiveDataType>,
}

impl PatternMatcher {
    /// Create a minimal pattern matcher without compiling regex patterns
    pub fn minimal() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(Self {
            patterns: HashMap::new(),
            enabled_types: Vec::new(),
        })
    }

    /// Create a new pattern matcher with default patterns
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let mut patterns = HashMap::new();
        
        // Credit Card patterns (Luhn algorithm validation will be applied separately)
        patterns.insert(SensitiveDataType::CreditCard, vec![
            Regex::new(r"\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\b")?,
            Regex::new(r"\b[0-9]{4}[\s\-]?[0-9]{4}[\s\-]?[0-9]{4}[\s\-]?[0-9]{4}\b")?,
        ]);
        
        // Social Security Number patterns
        patterns.insert(SensitiveDataType::SocialSecurityNumber, vec![
            Regex::new(r"\b[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{4}\b")?,
            Regex::new(r"\bSSN[\s:]*[0-9]{3}[\s\-]?[0-9]{2}[\s\-]?[0-9]{4}\b")?,
        ]);
        
        // Password patterns (common formats)
        patterns.insert(SensitiveDataType::Password, vec![
            Regex::new(r"(?i)password[\s:=]+[^\s\n]{8,}")?,
            Regex::new(r"(?i)pwd[\s:=]+[^\s\n]{6,}")?,
            Regex::new(r"(?i)pass[\s:=]+[^\s\n]{8,}")?,
            Regex::new(r"(?i)secret[\s:=]+[^\s\n]{8,}")?,
        ]);
        
        // API Key patterns
        patterns.insert(SensitiveDataType::ApiKey, vec![
            Regex::new(r"(?i)api[_\s]?key[\s:=]+[a-zA-Z0-9]{20,}")?,
            Regex::new(r"(?i)access[_\s]?token[\s:=]+[a-zA-Z0-9]{20,}")?,
            Regex::new(r"(?i)secret[_\s]?key[\s:=]+[a-zA-Z0-9]{20,}")?,
            Regex::new(r"\b[A-Za-z0-9]{32,}\b")?, // Generic long alphanumeric strings
        ]);
        
        // Crypto seed phrase patterns
        patterns.insert(SensitiveDataType::CryptoSeedPhrase, vec![
            Regex::new(r"\b(?:[a-z]+\s+){11}[a-z]+\b")?, // 12 words
            Regex::new(r"\b(?:[a-z]+\s+){23}[a-z]+\b")?, // 24 words
            Regex::new(r"(?i)seed\s+phrase[\s:]+(?:[a-z]+\s+){11,23}[a-z]+")?,
            Regex::new(r"(?i)mnemonic[\s:]+(?:[a-z]+\s+){11,23}[a-z]+")?,
        ]);
        
        // Email patterns
        patterns.insert(SensitiveDataType::Email, vec![
            Regex::new(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b")?,
        ]);
        
        // Phone number patterns
        patterns.insert(SensitiveDataType::PhoneNumber, vec![
            Regex::new(r"\b(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b")?,
            Regex::new(r"\b[0-9]{3}[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}\b")?,
        ]);
        
        // Passport number patterns (generic international format)
        patterns.insert(SensitiveDataType::PassportNumber, vec![
            Regex::new(r"\b[A-Z]{1,2}[0-9]{6,9}\b")?,
            Regex::new(r"(?i)passport[\s#:]*[A-Z0-9]{6,12}")?,
        ]);
        
        // Driver's License patterns (US format)
        patterns.insert(SensitiveDataType::DriverLicense, vec![
            Regex::new(r"\b[A-Z]{1,2}[0-9]{6,8}\b")?,
            Regex::new(r"(?i)(?:driver|dl)[\s#:]*[A-Z0-9]{6,12}")?,
        ]);
        
        // Bank account patterns
        patterns.insert(SensitiveDataType::BankAccount, vec![
            Regex::new(r"\b[0-9]{8,17}\b")?, // Generic bank account length
            Regex::new(r"(?i)account[\s#:]*[0-9]{8,17}")?,
            Regex::new(r"(?i)routing[\s#:]*[0-9]{9}")?,
        ]);
        
        // Tax ID patterns
        patterns.insert(SensitiveDataType::TaxId, vec![
            Regex::new(r"\b[0-9]{2}[\s\-]?[0-9]{7}\b")?, // EIN format
            Regex::new(r"(?i)ein[\s#:]*[0-9]{2}[\s\-]?[0-9]{7}")?,
            Regex::new(r"(?i)tax[\s_]?id[\s#:]*[0-9\-]{9,}")?,
        ]);
        
        // Auth token patterns
        patterns.insert(SensitiveDataType::AuthToken, vec![
            Regex::new(r"(?i)bearer[\s]+[a-zA-Z0-9\-._~+/]+=*")?,
            Regex::new(r"(?i)token[\s:=]+[a-zA-Z0-9\-._~+/]{20,}")?,
            Regex::new(r"\bghp_[a-zA-Z0-9]{36}\b")?, // GitHub personal access token
            Regex::new(r"\bsk-[a-zA-Z0-9]{48}\b")?, // OpenAI API key
        ]);
        
        // All types enabled by default
        let enabled_types = vec![
            SensitiveDataType::Password,
            SensitiveDataType::CreditCard,
            SensitiveDataType::SocialSecurityNumber,
            SensitiveDataType::PassportNumber,
            SensitiveDataType::DriverLicense,
            SensitiveDataType::CryptoSeedPhrase,
            SensitiveDataType::ApiKey,
            SensitiveDataType::AuthToken,
            SensitiveDataType::Email,
            SensitiveDataType::PhoneNumber,
            SensitiveDataType::BankAccount,
            SensitiveDataType::TaxId,
        ];
        
        Ok(PatternMatcher {
            patterns,
            enabled_types,
        })
    }
    
    /// Configure which data types to detect
    pub fn set_enabled_types(&mut self, types: Vec<SensitiveDataType>) {
        self.enabled_types = types;
    }
    
    /// Scan text content for sensitive data patterns
    pub fn scan_content(&self, content: &str, file_path: &str) -> Vec<DetectionResult> {
        let mut results = Vec::new();
        let severity = self.determine_severity(file_path);
        
        for data_type in &self.enabled_types {
            if let Some(patterns) = self.patterns.get(data_type).cloned() {
                for pattern in patterns {
                    for (line_num, line) in content.lines().enumerate() {
                        for mat in pattern.find_iter(line) {
                            let confidence = self.calculate_confidence(data_type, mat.as_str());
                            
                            // Additional validation for specific types
                            if self.validate_match(data_type, mat.as_str()) {
                                results.push(DetectionResult {
                                    data_type: data_type.clone(),
                                    severity: severity.clone(),
                                    line_number: Some(line_num + 1),
                                    column_start: Some(mat.start()),
                                    column_end: Some(mat.end()),
                                    confidence,
                                    context_hint: self.generate_context_hint(data_type, line),
                                });
                            }
                        }
                    }
                }
            }
        }
        
        results
    }
    
    /// Determine severity based on file location
    fn determine_severity(&self, file_path: &str) -> SeverityLevel {
        let path_lower = file_path.to_lowercase();
        
        if path_lower.contains("desktop") || path_lower.contains("downloads") {
            SeverityLevel::Critical
        } else if path_lower.contains("documents") || path_lower.contains("onedrive") {
            SeverityLevel::High
        } else if path_lower.contains("users") || path_lower.contains("home") {
            SeverityLevel::Medium
        } else {
            SeverityLevel::Low
        }
    }
    
    /// Calculate confidence score for a match
    fn calculate_confidence(&self, data_type: &SensitiveDataType, matched_text: &str) -> f32 {
        match data_type {
            SensitiveDataType::CreditCard => {
                if self.validate_luhn(matched_text) { 0.9 } else { 0.3 }
            },
            SensitiveDataType::Email => {
                if matched_text.contains('@') && matched_text.contains('.') { 0.8 } else { 0.4 }
            },
            SensitiveDataType::CryptoSeedPhrase => {
                let word_count = matched_text.split_whitespace().count();
                if word_count == 12 || word_count == 24 { 0.9 } else { 0.5 }
            },
            _ => 0.7, // Default confidence
        }
    }
    
    /// Validate specific patterns with additional checks
    fn validate_match(&self, data_type: &SensitiveDataType, matched_text: &str) -> bool {
        match data_type {
            SensitiveDataType::CreditCard => {
                // Only report if Luhn algorithm validates or high confidence pattern
                self.validate_luhn(matched_text) || matched_text.len() >= 15
            },
            SensitiveDataType::SocialSecurityNumber => {
                // Avoid false positives like dates
                !self.looks_like_date(matched_text)
            },
            SensitiveDataType::PhoneNumber => {
                // Must have reasonable phone number characteristics
                matched_text.chars().filter(|c| c.is_ascii_digit()).count() >= 10
            },
            _ => true, // Default: accept all matches
        }
    }
    
    /// Validate credit card number using Luhn algorithm
    fn validate_luhn(&self, number: &str) -> bool {
        let digits: Vec<u32> = number
            .chars()
            .filter(|c| c.is_ascii_digit())
            .map(|c| c.to_digit(10).unwrap())
            .collect();
        
        if digits.len() < 13 || digits.len() > 19 {
            return false;
        }
        
        let mut sum = 0;
        let mut alternate = false;
        
        for &digit in digits.iter().rev() {
            let mut n = digit;
            if alternate {
                n *= 2;
                if n > 9 {
                    n = (n / 10) + (n % 10);
                }
            }
            sum += n;
            alternate = !alternate;
        }
        
        sum % 10 == 0
    }
    
    /// Check if a string looks like a date (to avoid SSN false positives)
    fn looks_like_date(&self, _text: &str) -> bool {
        // Simple heuristic: if it contains common date separators and reasonable ranges
        if _text.contains('/') || _text.contains('-') {
            let parts: Vec<&str> = _text.split(['/', '-']).collect();
            if parts.len() == 3 {
                if let (Ok(a), Ok(b), Ok(c)) = (parts[0].parse::<u32>(), parts[1].parse::<u32>(), parts[2].parse::<u32>()) {
                    // Check for reasonable date ranges
                    return (a <= 12 && b <= 31) || (a <= 31 && b <= 12) || c > 1900;
                }
            }
        }
        false
    }
    
    /// Generate a safe context hint without exposing sensitive data
    fn generate_context_hint(&self, data_type: &SensitiveDataType, line: &str) -> String {
        let context_words: Vec<&str> = line
            .split_whitespace()
            .filter(|word| !self.might_be_sensitive(word))
            .take(3)
            .collect();
        
        let _context = if context_words.is_empty() {
            "sensitive data detected".to_string()
        } else {
            format!("near: {}", context_words.join(" "))
        };
        
        format!("{data_type:?} {_context}")
    }
    
    /// Check if a word might contain sensitive information
    fn might_be_sensitive(&self, word: &str) -> bool {
        word.len() > 15 || // Long strings might be sensitive
        word.chars().all(|c| c.is_ascii_digit()) || // All numbers
        (word.chars().any(|c| c.is_ascii_uppercase()) && word.chars().any(|c| c.is_ascii_lowercase())) // Mixed case (might be encoded)
    }
}

impl Default for PatternMatcher {
    fn default() -> Self {
        Self::new().expect("Failed to create default PatternMatcher")
    }
}
