import React, { useState, useEffect } from 'react';
// import { invoke } from '@tauri-apps/api/core'; // TODO: Implement backend integration
import {
    <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
    <PERSON><PERSON>hart, Pie, Cell, LineChart, Line, Area, AreaChart
} from 'recharts';
import { TrendingUp, Shield, AlertTriangle, FileText, Clock, Database } from 'lucide-react';
import './EnhancedAnalyticsDashboard.css';

// Analytics data interfaces
interface ScanMetrics {
    total_scans: number;
    files_processed: number;
    privacy_findings: number;
    crypto_findings: number;
    risk_score_average: number;
    processing_time_average: number;
}

interface RiskDistribution {
    level: string;
    count: number;
    percentage: number;
    color: string;
}

interface TimeSeriesData {
    date: string;
    scans: number;
    findings: number;
    risk_score: number;
}

interface FileTypeStats {
    type: string;
    count: number;
    findings: number;
    avg_risk: number;
}

const EnhancedAnalyticsDashboard: React.FC = () => {
    const [metrics, setMetrics] = useState<ScanMetrics>({
        total_scans: 0,
        files_processed: 0,
        privacy_findings: 0,
        crypto_findings: 0,
        risk_score_average: 0,
        processing_time_average: 0,
    });

    const [riskDistribution, setRiskDistribution] = useState<RiskDistribution[]>([]);

    const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);

    const [fileTypeStats, setFileTypeStats] = useState<FileTypeStats[]>([]);

    const [isLoading, setIsLoading] = useState(false);
    const [selectedTimeRange, setSelectedTimeRange] = useState<'7d' | '30d' | '90d'>('7d');

    useEffect(() => {
        loadAnalyticsData();
    }, [selectedTimeRange]);

    const loadAnalyticsData = async () => {
        try {
            setIsLoading(true);

            // TODO: Implement real backend integration
            // For now, show empty state until real data is available
            setMetrics({
                total_scans: 0,
                files_processed: 0,
                privacy_findings: 0,
                crypto_findings: 0,
                risk_score_average: 0,
                processing_time_average: 0,
            });

            // Clear test data
            setRiskDistribution([]);
            setTimeSeriesData([]);
            setFileTypeStats([]);

        } catch (error) {
            console.error('Failed to load analytics data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const formatNumber = (num: number): string => {
        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
        if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
        return num.toString();
    };

    const MetricCard: React.FC<{
        title: string;
        value: string | number;
        icon: React.ReactNode;
        trend?: string;
        color: string;
    }> = ({ title, value, icon, trend, color }) => (
        <div className="metric-card">
            <div className="metric-header">
                <div className="metric-icon" style={{ color }}>
                    {icon}
                </div>
                {trend && (
                    <div className={`metric-trend ${trend.startsWith('+') ? 'positive' : 'negative'}`}>
                        <TrendingUp size={14} />
                        {trend}
                    </div>
                )}
            </div>
            <div className="metric-content">
                <div className="metric-value">{value}</div>
                <div className="metric-title">{title}</div>
            </div>
        </div>
    );

    if (isLoading) {
        return (
            <div className="analytics-dashboard loading">
                <div className="loading-spinner"></div>
                <p>Loading analytics data...</p>
            </div>
        );
    }

    return (
        <div className="analytics-dashboard">
            <div className="dashboard-header">
                <div>
                    <h2>Analytics Dashboard</h2>
                    <p>Privacy scan insights and performance metrics</p>
                </div>
                
                <div className="time-range-selector">
                    {(['7d', '30d', '90d'] as const).map((range) => (
                        <button
                            key={range}
                            className={`range-button ${selectedTimeRange === range ? 'active' : ''}`}
                            onClick={() => setSelectedTimeRange(range)}
                        >
                            {range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : '90 Days'}
                        </button>
                    ))}
                </div>
            </div>

            {/* Empty State Message */}
            {metrics.total_scans === 0 && !isLoading && (
                <div style={{
                    textAlign: 'center',
                    padding: '40px 20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '12px',
                    margin: '20px 0',
                    border: '2px dashed #dee2e6'
                }}>
                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
                    <h3 style={{ color: '#495057', marginBottom: '8px' }}>No Analytics Data Yet</h3>
                    <p style={{ color: '#6c757d', marginBottom: '16px' }}>
                        Start scanning files to see analytics data and insights here.
                    </p>
                    <p style={{ fontSize: '14px', color: '#868e96' }}>
                        Analytics will automatically populate as you perform scans using the Scanner or Document Detection features.
                    </p>
                </div>
            )}

            <div className="metrics-grid">
                <MetricCard
                    title="Total Scans"
                    value={formatNumber(metrics.total_scans)}
                    icon={<FileText size={24} />}
                    trend={metrics.total_scans > 0 ? "+12%" : ""}
                    color="#3b82f6"
                />
                <MetricCard
                    title="Files Processed"
                    value={formatNumber(metrics.files_processed)}
                    icon={<Database size={24} />}
                    trend={metrics.files_processed > 0 ? "+8%" : ""}
                    color="#10b981"
                />
                <MetricCard
                    title="Privacy Findings"
                    value={formatNumber(metrics.privacy_findings)}
                    icon={<Shield size={24} />}
                    trend={metrics.privacy_findings > 0 ? "+15%" : ""}
                    color="#f59e0b"
                />
                <MetricCard
                    title="Avg Risk Score"
                    value={metrics.risk_score_average > 0 ? metrics.risk_score_average.toFixed(1) : "0.0"}
                    icon={<AlertTriangle size={24} />}
                    trend={metrics.risk_score_average > 0 ? "-3%" : ""}
                    color="#ef4444"
                />
                <MetricCard
                    title="Avg Processing Time"
                    value={metrics.processing_time_average > 0 ? `${metrics.processing_time_average}s` : "0.0s"}
                    icon={<Clock size={24} />}
                    trend={metrics.processing_time_average > 0 ? "-18%" : ""}
                    color="#8b5cf6"
                />
            </div>

            <div className="charts-grid">
                <div className="chart-container">
                    <h3>Risk Distribution</h3>
                    <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                            <Pie
                                data={riskDistribution}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={({ level, percentage }) => `${level} (${percentage}%)`}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="count"
                            >
                                {riskDistribution.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                            </Pie>
                            <Tooltip />
                        </PieChart>
                    </ResponsiveContainer>
                </div>

                <div className="chart-container">
                    <h3>Scan Activity Trend</h3>
                    <ResponsiveContainer width="100%" height={300}>
                        <AreaChart data={timeSeriesData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                                dataKey="date" 
                                tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                            />
                            <YAxis />
                            <Tooltip 
                                labelFormatter={(value) => new Date(value).toLocaleDateString()}
                            />
                            <Area 
                                type="monotone" 
                                dataKey="scans" 
                                stackId="1" 
                                stroke="#3b82f6" 
                                fill="#3b82f6" 
                                fillOpacity={0.6}
                                name="Scans"
                            />
                            <Area 
                                type="monotone" 
                                dataKey="findings" 
                                stackId="2" 
                                stroke="#ef4444" 
                                fill="#ef4444" 
                                fillOpacity={0.6}
                                name="Findings"
                            />
                        </AreaChart>
                    </ResponsiveContainer>
                </div>

                <div className="chart-container full-width">
                    <h3>File Type Analysis</h3>
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={fileTypeStats}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="type" />
                            <YAxis yAxisId="left" />
                            <YAxis yAxisId="right" orientation="right" />
                            <Tooltip />
                            <Legend />
                            <Bar yAxisId="left" dataKey="count" fill="#3b82f6" name="Files Processed" />
                            <Bar yAxisId="left" dataKey="findings" fill="#ef4444" name="Findings" />
                            <Line yAxisId="right" type="monotone" dataKey="avg_risk" stroke="#f59e0b" name="Avg Risk Score" />
                        </BarChart>
                    </ResponsiveContainer>
                </div>

                <div className="chart-container">
                    <h3>Risk Score Trend</h3>
                    <ResponsiveContainer width="100%" height={300}>
                        <LineChart data={timeSeriesData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                                dataKey="date" 
                                tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                            />
                            <YAxis domain={[0, 10]} />
                            <Tooltip 
                                labelFormatter={(value) => new Date(value).toLocaleDateString()}
                                formatter={(value: number) => [value.toFixed(1), 'Risk Score']}
                            />
                            <Line 
                                type="monotone" 
                                dataKey="risk_score" 
                                stroke="#f59e0b" 
                                strokeWidth={3}
                                dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                                activeDot={{ r: 6 }}
                            />
                        </LineChart>
                    </ResponsiveContainer>
                </div>
            </div>
        </div>
    );
};

export default EnhancedAnalyticsDashboard;
