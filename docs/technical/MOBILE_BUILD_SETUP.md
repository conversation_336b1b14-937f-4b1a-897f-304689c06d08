# 📱 **PrivacyAI Mobile Build Setup Guide**

**Version**: 1.0  
**Created**: July 27, 2025  
**Platforms**: iOS 14+, Android 8+ (API 26+)  
**Prerequisites**: Completed desktop build setup  

## 🎯 **Overview**

This guide provides step-by-step instructions for setting up PrivacyAI mobile development environment and building for iOS and Android platforms using Tauri 2.0.

## 📋 **Prerequisites**

### **Desktop Development Environment**
- ✅ Rust toolchain installed and working
- ✅ Node.js 18+ and npm installed
- ✅ PrivacyAI desktop build successful
- ✅ Git repository cloned and up-to-date

### **Platform-Specific Requirements**

#### **iOS Development**
- **macOS**: Required for iOS development
- **Xcode**: Version 14.0 or later
- **iOS Simulator**: For testing
- **Apple Developer Account**: For device testing and App Store deployment

#### **Android Development**
- **Android Studio**: Latest stable version
- **Android SDK**: API level 26+ (Android 8.0)
- **Android NDK**: For native code compilation
- **Java Development Kit**: JDK 11 or later

## 🔧 **Installation Steps**

### **Step 1: Install Mobile Rust Targets**

```bash
# Add iOS target (macOS only)
rustup target add aarch64-apple-ios
rustup target add x86_64-apple-ios  # For simulator

# Add Android targets
rustup target add aarch64-linux-android
rustup target add armv7-linux-androideabi
rustup target add i686-linux-android
rustup target add x86_64-linux-android
```

### **Step 2: Install Tauri Mobile CLI**

```bash
# Install latest Tauri CLI with mobile support
npm install -g @tauri-apps/cli@next

# Verify mobile support
npx tauri info
```

### **Step 3: Configure Mobile Dependencies**

```bash
# Navigate to project root
cd PrivacyAI

# Install mobile-specific dependencies
npm install @tauri-apps/api@next
npm install @capacitor/camera
npm install @capacitor/filesystem
npm install @capacitor/device

# Update package.json scripts
npm run setup:mobile
```

### **Step 4: iOS Setup (macOS Only)**

```bash
# Install iOS development tools
xcode-select --install

# Install CocoaPods (for iOS dependencies)
sudo gem install cocoapods

# Initialize iOS project
npx tauri ios init

# Install iOS dependencies
cd src-tauri/gen/apple && pod install
```

### **Step 5: Android Setup**

```bash
# Set Android environment variables
export ANDROID_HOME=$HOME/Library/Android/sdk  # macOS
export ANDROID_HOME=$HOME/Android/Sdk          # Linux
export ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk # Windows

export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Initialize Android project
npx tauri android init

# Accept Android licenses
$ANDROID_HOME/tools/bin/sdkmanager --licenses
```

## 📱 **Mobile Configuration**

### **Update Tauri Configuration for Mobile**

Create or update `src-tauri/tauri.conf.json`:

```json
{
  "$schema": "https://schema.tauri.app/config/2",
  "productName": "PrivacyAI",
  "version": "0.1.0",
  "identifier": "com.privacyai.app",
  "build": {
    "beforeDevCommand": "npm run dev",
    "devUrl": "http://localhost:1420",
    "beforeBuildCommand": "npm run build",
    "frontendDist": "../dist"
  },
  "app": {
    "withGlobalTauri": true,
    "windows": [
      {
        "title": "PrivacyAI",
        "width": 800,
        "height": 600,
        "resizable": true,
        "fullscreen": false
      }
    ],
    "security": {
      "csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost"
    }
  },
  "bundle": {
    "active": true,
    "targets": "all",
    "icon": [
      "icons/32x32.png",
      "icons/128x128.png",
      "icons/<EMAIL>",
      "icons/icon.icns",
      "icons/icon.ico"
    ]
  },
  "plugins": {
    "fs": {
      "all": true,
      "scope": ["$APPDATA/*", "$DOCUMENT/*", "$PICTURE/*"]
    },
    "shell": {
      "all": false,
      "open": true
    }
  }
}
```

### **Mobile-Specific Rust Configuration**

Update `src-tauri/Cargo.toml`:

```toml
[dependencies]
tauri = { version = "2", features = ["mobile"] }
tauri-plugin-fs = "2"
tauri-plugin-shell = "2"

# Mobile-specific dependencies
[target.'cfg(any(target_os = "android", target_os = "ios"))'.dependencies]
tauri-plugin-camera = "2"
tauri-plugin-device = "2"

# Platform-specific optimizations
[profile.release]
opt-level = "s"  # Optimize for size on mobile
lto = true       # Link-time optimization
codegen-units = 1
panic = "abort"
```

## 🚀 **Building and Testing**

### **Development Builds**

```bash
# iOS development (macOS only)
npm run tauri ios dev

# Android development
npm run tauri android dev

# Check build status
npm run tauri info
```

### **Production Builds**

```bash
# iOS production build
npm run tauri ios build

# Android production build
npm run tauri android build

# Universal build (all platforms)
npm run tauri build --target universal
```

### **Testing on Devices**

```bash
# iOS Simulator
npm run tauri ios dev --target simulator

# Android Emulator
npm run tauri android dev --target emulator

# Physical device testing
npm run tauri ios dev --target device
npm run tauri android dev --target device
```

## 🔍 **Mobile-Specific Code Examples**

### **Platform Detection**

```rust
// src-tauri/src/mobile.rs
use tauri::Manager;

#[tauri::command]
pub fn get_platform_info() -> PlatformInfo {
    #[cfg(target_os = "ios")]
    return PlatformInfo {
        platform: "iOS".to_string(),
        is_mobile: true,
        supports_file_system: false,
        max_memory_mb: 256,
    };
    
    #[cfg(target_os = "android")]
    return PlatformInfo {
        platform: "Android".to_string(),
        is_mobile: true,
        supports_file_system: true,
        max_memory_mb: 512,
    };
    
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    return PlatformInfo {
        platform: "Desktop".to_string(),
        is_mobile: false,
        supports_file_system: true,
        max_memory_mb: 2048,
    };
}
```

### **Mobile File Access**

```typescript
// src/utils/mobileFileAccess.ts
import { invoke } from '@tauri-apps/api/core';

export async function selectMobileFile(): Promise<string | null> {
  try {
    if (await isMobile()) {
      // Use mobile-specific file picker
      return await invoke('select_mobile_file');
    } else {
      // Use desktop file dialog
      return await invoke('select_desktop_file');
    }
  } catch (error) {
    console.error('File selection failed:', error);
    return null;
  }
}

export async function isMobile(): Promise<boolean> {
  const platform = await invoke('get_platform_info');
  return platform.is_mobile;
}
```

### **Mobile UI Adaptations**

```tsx
// src/components/MobileAdaptiveUI.tsx
import React from 'react';
import { useEffect, useState } from 'react';

export function MobileAdaptiveUI() {
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    checkMobilePlatform();
  }, []);
  
  const checkMobilePlatform = async () => {
    const platform = await invoke('get_platform_info');
    setIsMobile(platform.is_mobile);
  };
  
  return (
    <div className={`app-container ${isMobile ? 'mobile' : 'desktop'}`}>
      {isMobile ? (
        <MobilePrivacyScanner />
      ) : (
        <DesktopPrivacyScanner />
      )}
    </div>
  );
}
```

## 🧪 **Testing and Debugging**

### **Mobile Testing Checklist**

- [ ] **App Installation**: Installs successfully on target devices
- [ ] **UI Responsiveness**: Interface adapts to mobile screen sizes
- [ ] **Touch Interactions**: All buttons and controls work with touch
- [ ] **File Access**: Can select and process files on mobile
- [ ] **Memory Usage**: Stays within mobile memory constraints
- [ ] **Battery Life**: Acceptable battery consumption during use
- [ ] **Performance**: Scan times meet mobile performance targets

### **Debugging Tools**

```bash
# iOS debugging
npx tauri ios dev --debug

# Android debugging
npx tauri android dev --debug
adb logcat | grep PrivacyAI

# Performance profiling
npx tauri ios build --profile
npx tauri android build --profile
```

### **Common Issues and Solutions**

**Issue**: Build fails with "target not found"
```bash
# Solution: Ensure mobile targets are installed
rustup target list --installed
rustup target add aarch64-apple-ios  # If missing
```

**Issue**: iOS app crashes on startup
```bash
# Solution: Check iOS deployment target
# Update src-tauri/gen/apple/PrivacyAI.xcodeproj settings
# Set iOS Deployment Target to 14.0+
```

**Issue**: Android permissions denied
```bash
# Solution: Update AndroidManifest.xml
# Add required permissions for file access
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.CAMERA" />
```

## 📊 **Performance Optimization**

### **Mobile Build Optimizations**

```toml
# src-tauri/Cargo.toml mobile optimizations
[profile.release-mobile]
inherits = "release"
opt-level = "s"        # Optimize for size
lto = true            # Link-time optimization
codegen-units = 1     # Single codegen unit
panic = "abort"       # Smaller binary size
strip = true          # Remove debug symbols
```

### **Bundle Size Optimization**

```bash
# Analyze bundle size
npx tauri build --analyze

# Mobile-specific optimizations
npm run build:mobile-optimized
```

## 🎯 **Deployment**

### **iOS App Store Deployment**

1. **Code Signing**: Configure in Xcode
2. **App Store Connect**: Upload build
3. **TestFlight**: Beta testing
4. **App Review**: Submit for review

### **Android Play Store Deployment**

1. **Signing Key**: Generate release keystore
2. **Play Console**: Upload AAB file
3. **Internal Testing**: Test with internal users
4. **Production**: Release to Play Store

---

**Next Steps**: Complete mobile build setup, then begin Phase 1 mobile development  
**Support**: Check troubleshooting section for common issues  
**Updates**: This guide will be updated as mobile development progresses
