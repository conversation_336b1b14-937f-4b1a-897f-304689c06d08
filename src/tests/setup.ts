import { vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock Tauri API
const mockInvoke = vi.fn();

// Setup mock responses for common Tauri calls
mockInvoke.mockImplementation((command: string) => {
  switch (command) {
    case 'get_system_info':
      return Promise.resolve({
        platform: 'test',
        version: '1.0.0',
        ocr_available: true,
        gpu_acceleration: true,
        memory_gb: 8,
        cpu_cores: 4,
        supported_formats: ['txt', 'pdf', 'docx', 'jpg', 'png']
      });
    case 'initialize_privacy_engine':
    case 'initialize_ocr_engine':
      return Promise.resolve();
    case 'scan_file':
    case 'scan_directory':
      return Promise.resolve([]);
    default:
      return Promise.resolve();
  }
});

// Setup global mocks
(global as any).mockInvoke = mockInvoke;
(global as any).__TAURI__ = {
  core: {
    invoke: mockInvoke,
  },
  event: {
    listen: vi.fn(),
    emit: vi.fn(),
  },
  window: {
    getCurrent: vi.fn(() => ({
      listen: vi.fn(),
      emit: vi.fn(),
    })),
  },
};

// Mock the Tauri API module
vi.mock('@tauri-apps/api/core', () => ({
  invoke: mockInvoke,
}));

// Mock mobile device capabilities for testing
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 375, // Mobile width
});

Object.defineProperty(window, 'innerHeight', {
  writable: true,
  configurable: true,
  value: 667, // Mobile height
});

Object.defineProperty(navigator, 'userAgent', {
  writable: true,
  configurable: true,
  value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
});

Object.defineProperty(navigator, 'maxTouchPoints', {
  writable: true,
  configurable: true,
  value: 5,
});

Object.defineProperty(window, 'ontouchstart', {
  writable: true,
  configurable: true,
  value: {},
});

Object.defineProperty(window, 'devicePixelRatio', {
  writable: true,
  configurable: true,
  value: 2,
});

// Mock matchMedia for accessibility preferences
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: query.includes('prefers-reduced-motion') ? false : query.includes('prefers-contrast') ? false : false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock PerformanceObserver
global.PerformanceObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock speechSynthesis
Object.defineProperty(window, 'speechSynthesis', {
  writable: true,
  value: {
    getVoices: vi.fn(() => []),
    speak: vi.fn(),
    cancel: vi.fn(),
    pause: vi.fn(),
    resume: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  },
});

// Mock navigator properties
Object.defineProperty(navigator, 'userAgent', {
  writable: true,
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
});

Object.defineProperty(navigator, 'maxTouchPoints', {
  writable: true,
  value: 0,
});

// Mock performance.now
Object.defineProperty(performance, 'now', {
  writable: true,
  value: vi.fn(() => Date.now()),
});

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id));

// Reset mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
  mockInvoke.mockReset();
});
