use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::Mutex;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;


/// Auto-scaling configuration for different resource types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoScalingConfig {
    pub enabled: bool,
    pub memory_scaling: MemoryScalingConfig,
    pub cpu_scaling: CpuScalingConfig,
    pub thread_scaling: ThreadScalingConfig,
    pub prediction_enabled: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MemoryScalingConfig {
    pub scale_up_threshold: f32,        // 80%
    pub scale_down_threshold: f32,      // 50%
    pub aggressive_cleanup_threshold: f32, // 90%
    pub cooldown_minutes: u32,          // 5 minutes
    pub max_cache_size_mb: u64,         // 500MB
    pub min_cache_size_mb: u64,         // 50MB
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CpuScalingConfig {
    pub scale_up_threshold: f32,        // 85%
    pub scale_down_threshold: f32,      // 40%
    pub target_utilization: f32,        // 70%
    pub cooldown_minutes: u32,          // 3 minutes
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThreadScalingConfig {
    pub min_threads: usize,             // 2
    pub max_threads: usize,             // CPU cores * 2
    pub scale_up_threshold: f32,        // 80% CPU
    pub scale_down_threshold: f32,      // 30% CPU
    pub cooldown_minutes: u32,          // 2 minutes
}

/// Scaling event record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingEvent {
    pub timestamp: u64,
    pub event_type: ScalingEventType,
    pub resource_type: ResourceType,
    pub old_value: f64,
    pub new_value: f64,
    pub trigger_reason: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ScalingEventType {
    ScaleUp,
    ScaleDown,
    ThresholdAdjust,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ResourceType {
    Memory,
    Cpu,
    Threads,
}

/// Workload prediction data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkloadPrediction {
    pub predicted_memory_usage_mb: f64,
    pub predicted_cpu_usage_percent: f64,
    pub confidence_score: f64,
    pub time_horizon_minutes: u32,
}

/// Resource metrics for auto-scaling decisions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceMetrics {
    pub memory_usage_mb: f64,
    pub memory_limit_mb: f64,
    pub cpu_usage_percent: f64,
    pub disk_usage_mb: f64,
    pub active_processes: u32,
    pub thread_pool_size: usize,
    pub optimal_thread_count: usize,
    pub scaling_events: Vec<ScalingEvent>,
    pub workload_prediction: Option<WorkloadPrediction>,
}

/// Cooldown manager to prevent rapid scaling oscillations
#[derive(Debug)]
struct CooldownManager {
    last_scaling_events: HashMap<ResourceType, Instant>,
}

impl CooldownManager {
    fn new() -> Self {
        Self {
            last_scaling_events: HashMap::new(),
        }
    }

    fn is_in_cooldown(&self, resource_type: &ResourceType, cooldown_duration: Duration) -> bool {
        if let Some(last_event) = self.last_scaling_events.get(resource_type) {
            last_event.elapsed() < cooldown_duration
        } else {
            false
        }
    }

    fn record_scaling_event(&mut self, resource_type: ResourceType) {
        self.last_scaling_events.insert(resource_type, Instant::now());
    }
}

/// Main auto-scaling engine
pub struct AutoScalingEngine {
    config: Arc<RwLock<AutoScalingConfig>>,
    cooldown_manager: Arc<Mutex<CooldownManager>>,
    scaling_events: Arc<Mutex<VecDeque<ScalingEvent>>>,
    current_thread_pool_size: Arc<Mutex<usize>>,
    workload_predictor: Arc<Mutex<WorkloadPredictor>>,
}

/// Simple workload predictor using moving averages
struct WorkloadPredictor {
    memory_history: VecDeque<f64>,
    cpu_history: VecDeque<f64>,
    max_history_size: usize,
}

impl WorkloadPredictor {
    fn new() -> Self {
        Self {
            memory_history: VecDeque::new(),
            cpu_history: VecDeque::new(),
            max_history_size: 20, // Keep last 20 samples
        }
    }

    fn add_sample(&mut self, memory_usage: f64, cpu_usage: f64) {
        self.memory_history.push_back(memory_usage);
        self.cpu_history.push_back(cpu_usage);

        if self.memory_history.len() > self.max_history_size {
            self.memory_history.pop_front();
        }
        if self.cpu_history.len() > self.max_history_size {
            self.cpu_history.pop_front();
        }
    }

    fn predict_workload(&self, horizon_minutes: u32) -> WorkloadPrediction {
        let memory_avg = self.memory_history.iter().sum::<f64>() / self.memory_history.len() as f64;
        let cpu_avg = self.cpu_history.iter().sum::<f64>() / self.cpu_history.len() as f64;

        // Simple trend analysis
        let memory_trend = if self.memory_history.len() >= 2 {
            let recent = self.memory_history.iter().rev().take(5).sum::<f64>() / 5.0;
            let older = self.memory_history.iter().take(5).sum::<f64>() / 5.0;
            recent - older
        } else {
            0.0
        };

        let cpu_trend = if self.cpu_history.len() >= 2 {
            let recent = self.cpu_history.iter().rev().take(5).sum::<f64>() / 5.0;
            let older = self.cpu_history.iter().take(5).sum::<f64>() / 5.0;
            recent - older
        } else {
            0.0
        };

        // Project forward based on trend
        let time_factor = horizon_minutes as f64 / 60.0; // Convert to hours
        let predicted_memory = memory_avg + (memory_trend * time_factor);
        let predicted_cpu = cpu_avg + (cpu_trend * time_factor);

        // Calculate confidence based on data availability and trend stability
        let confidence = if self.memory_history.len() >= 10 {
            let trend_stability = 1.0 - (memory_trend.abs() / memory_avg).min(1.0);
            (trend_stability * 100.0).max(50.0).min(95.0)
        } else {
            50.0 // Low confidence with insufficient data
        };

        WorkloadPrediction {
            predicted_memory_usage_mb: predicted_memory.max(0.0),
            predicted_cpu_usage_percent: predicted_cpu.max(0.0).min(100.0),
            confidence_score: confidence,
            time_horizon_minutes: horizon_minutes,
        }
    }
}

impl AutoScalingEngine {
    pub fn new() -> Self {
        let default_config = AutoScalingConfig {
            enabled: true,
            memory_scaling: MemoryScalingConfig {
                scale_up_threshold: 80.0,
                scale_down_threshold: 50.0,
                aggressive_cleanup_threshold: 90.0,
                cooldown_minutes: 5,
                max_cache_size_mb: 500,
                min_cache_size_mb: 50,
            },
            cpu_scaling: CpuScalingConfig {
                scale_up_threshold: 85.0,
                scale_down_threshold: 40.0,
                target_utilization: 70.0,
                cooldown_minutes: 3,
            },
            thread_scaling: ThreadScalingConfig {
                min_threads: 2,
                max_threads: num_cpus::get() * 2,
                scale_up_threshold: 80.0,
                scale_down_threshold: 30.0,
                cooldown_minutes: 2,
            },
            prediction_enabled: true,
        };

        Self {
            config: Arc::new(RwLock::new(default_config)),
            cooldown_manager: Arc::new(Mutex::new(CooldownManager::new())),
            scaling_events: Arc::new(Mutex::new(VecDeque::new())),
            current_thread_pool_size: Arc::new(Mutex::new(num_cpus::get())),
            workload_predictor: Arc::new(Mutex::new(WorkloadPredictor::new())),
        }
    }

    pub async fn update_config(&self, new_config: AutoScalingConfig) {
        let mut config = self.config.write().await;
        *config = new_config;
    }

    pub async fn get_config(&self) -> AutoScalingConfig {
        self.config.read().await.clone()
    }

    pub async fn process_metrics(&self, metrics: &ResourceMetrics) -> Result<(), Box<dyn std::error::Error>> {
        let config = self.config.read().await;
        
        if !config.enabled {
            return Ok(());
        }

        // Update workload predictor
        if config.prediction_enabled {
            let mut predictor = self.workload_predictor.lock().await;
            predictor.add_sample(metrics.memory_usage_mb, metrics.cpu_usage_percent);
        }

        // Check memory scaling
        self.check_memory_scaling(&config, metrics).await?;

        // Check CPU/thread scaling
        self.check_thread_scaling(&config, metrics).await?;

        Ok(())
    }

    async fn check_memory_scaling(&self, config: &AutoScalingConfig, metrics: &ResourceMetrics) -> Result<(), Box<dyn std::error::Error>> {
        let memory_usage_percent = (metrics.memory_usage_mb / metrics.memory_limit_mb) * 100.0;
        let cooldown_duration = Duration::from_secs(config.memory_scaling.cooldown_minutes as u64 * 60);

        let mut cooldown_manager = self.cooldown_manager.lock().await;

        if cooldown_manager.is_in_cooldown(&ResourceType::Memory, cooldown_duration) {
            return Ok(());
        }

        if memory_usage_percent >= config.memory_scaling.aggressive_cleanup_threshold as f64 {
            // Trigger aggressive cleanup
            self.record_scaling_event(
                ScalingEventType::ScaleUp,
                ResourceType::Memory,
                memory_usage_percent,
                config.memory_scaling.aggressive_cleanup_threshold as f64,
                "Aggressive cleanup triggered".to_string(),
            ).await;
            cooldown_manager.record_scaling_event(ResourceType::Memory);
        } else if memory_usage_percent >= config.memory_scaling.scale_up_threshold as f64 {
            // Trigger memory optimization
            self.record_scaling_event(
                ScalingEventType::ScaleUp,
                ResourceType::Memory,
                memory_usage_percent,
                config.memory_scaling.scale_up_threshold as f64,
                "Memory optimization triggered".to_string(),
            ).await;
            cooldown_manager.record_scaling_event(ResourceType::Memory);
        }

        Ok(())
    }

    async fn check_thread_scaling(&self, config: &AutoScalingConfig, metrics: &ResourceMetrics) -> Result<(), Box<dyn std::error::Error>> {
        let cooldown_duration = Duration::from_secs(config.thread_scaling.cooldown_minutes as u64 * 60);
        let mut cooldown_manager = self.cooldown_manager.lock().await;

        if cooldown_manager.is_in_cooldown(&ResourceType::Threads, cooldown_duration) {
            return Ok(());
        }

        let current_size = *self.current_thread_pool_size.lock().await;

        if metrics.cpu_usage_percent >= config.thread_scaling.scale_up_threshold as f64 && current_size < config.thread_scaling.max_threads {
            let new_size = (current_size + 1).min(config.thread_scaling.max_threads);
            *self.current_thread_pool_size.lock().await = new_size;
            
            self.record_scaling_event(
                ScalingEventType::ScaleUp,
                ResourceType::Threads,
                current_size as f64,
                new_size as f64,
                format!("CPU usage {}% exceeded threshold", metrics.cpu_usage_percent),
            ).await;
            cooldown_manager.record_scaling_event(ResourceType::Threads);
        } else if metrics.cpu_usage_percent <= config.thread_scaling.scale_down_threshold as f64 && current_size > config.thread_scaling.min_threads {
            let new_size = (current_size - 1).max(config.thread_scaling.min_threads);
            *self.current_thread_pool_size.lock().await = new_size;
            
            self.record_scaling_event(
                ScalingEventType::ScaleDown,
                ResourceType::Threads,
                current_size as f64,
                new_size as f64,
                format!("CPU usage {}% below threshold", metrics.cpu_usage_percent),
            ).await;
            cooldown_manager.record_scaling_event(ResourceType::Threads);
        }

        Ok(())
    }

    async fn record_scaling_event(&self, event_type: ScalingEventType, resource_type: ResourceType, old_value: f64, new_value: f64, reason: String) {
        let event = ScalingEvent {
            timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            event_type,
            resource_type,
            old_value,
            new_value,
            trigger_reason: reason,
        };

        let mut events = self.scaling_events.lock().await;
        events.push_back(event);
        
        // Keep only the last 50 events
        if events.len() > 50 {
            events.pop_front();
        }
    }

    pub async fn get_scaling_events(&self) -> Vec<ScalingEvent> {
        self.scaling_events.lock().await.iter().cloned().collect()
    }

    pub async fn get_current_thread_pool_size(&self) -> usize {
        *self.current_thread_pool_size.lock().await
    }

    pub async fn get_workload_prediction(&self, horizon_minutes: u32) -> Option<WorkloadPrediction> {
        let predictor = self.workload_predictor.lock().await;
        if predictor.memory_history.len() >= 5 {
            Some(predictor.predict_workload(horizon_minutes))
        } else {
            None
        }
    }

    pub async fn calculate_optimal_thread_count(&self, cpu_usage: f64) -> usize {
        let config_guard = self.config.try_read();
        if let Ok(config) = config_guard {
            let target_utilization = config.cpu_scaling.target_utilization;
            let current_threads = self.get_current_thread_pool_size().await;

            // Simple calculation: adjust threads based on CPU utilization vs target
            if cpu_usage > target_utilization as f64 {
                (current_threads + 1).min(config.thread_scaling.max_threads)
            } else if cpu_usage < (target_utilization * 0.7) as f64 {
                (current_threads - 1).max(config.thread_scaling.min_threads)
            } else {
                current_threads
            }
        } else {
            num_cpus::get()
        }
    }
}
