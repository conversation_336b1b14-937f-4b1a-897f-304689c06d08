/* Document Type Detector Styles */

.document-type-detector {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.detector-header {
    text-align: center;
    margin-bottom: 30px;
}

.detector-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2rem;
}

.detector-header p {
    color: #7f8c8d;
    margin-bottom: 15px;
}

.status-indicator {
    display: inline-block;
}

.status {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.9rem;
}

.status.ready {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.initializing {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Configuration Panel */
.config-panel {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.config-panel h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
}

.config-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.config-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #495057;
}

.config-options input[type="checkbox"] {
    margin: 0;
}

.config-options select,
.config-options input[type="range"] {
    margin-left: 8px;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
    margin-bottom: 20px;
}

.upload-area:hover:not(.disabled) {
    border-color: #007bff;
    background: #f8f9ff;
}

.upload-area.drag-active {
    border-color: #007bff;
    background: #e3f2fd;
    transform: scale(1.02);
}

.upload-area.processing {
    border-color: #ffc107;
    background: #fffbf0;
    cursor: not-allowed;
}

.upload-area.disabled {
    border-color: #dee2e6;
    background: #f8f9fa;
    cursor: not-allowed;
    opacity: 0.6;
}

.upload-prompt {
    color: #6c757d;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-prompt p {
    font-size: 1.1rem;
    margin-bottom: 8px;
}

.upload-prompt small {
    color: #adb5bd;
}

/* Processing Indicator */
.processing-indicator {
    color: #495057;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

/* Error Message */
.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-message button {
    background: none;
    border: none;
    color: #721c24;
    cursor: pointer;
    font-size: 1.2rem;
    margin-left: auto;
}

/* Detection Results */
.detection-results {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.detection-results h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
}

.main-result {
    text-align: center;
    margin-bottom: 30px;
}

.document-type {
    display: inline-flex;
    align-items: center;
    gap: 15px;
    background: #f8f9fa;
    padding: 20px 30px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.type-icon {
    font-size: 2.5rem;
}

.type-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.confidence-badge {
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Detailed Results */
.detailed-results {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.result-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.result-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.1rem;
}

.method-results,
.layout-stats,
.performance-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.method,
.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.method:last-child,
.stat:last-child {
    border-bottom: none;
}

.method span:first-child,
.stat span:first-child {
    color: #6c757d;
    font-size: 0.9rem;
}

.method span:last-child,
.stat span:last-child {
    font-weight: 600;
    color: #495057;
}

/* Supported Types */
.supported-types {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
}

.supported-types h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c3e50;
}

.types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
}

.type-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.type-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.type-item .type-icon {
    font-size: 1.5rem;
}

.type-item .type-name {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.init-button,
.clear-button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.init-button {
    background: #007bff;
    color: white;
}

.init-button:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-2px);
}

.init-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.clear-button {
    background: #6c757d;
    color: white;
}

.clear-button:hover {
    background: #545b62;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .document-type-detector {
        padding: 15px;
    }
    
    .config-options {
        grid-template-columns: 1fr;
    }
    
    .detailed-results {
        grid-template-columns: 1fr;
    }
    
    .types-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .document-type {
        flex-direction: column;
        gap: 10px;
    }
}
