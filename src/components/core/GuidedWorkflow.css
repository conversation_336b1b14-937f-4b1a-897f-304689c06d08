/* Guided Workflow Component Styling */

.guided-workflow {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8fafc;
    min-height: 100vh;
}

/* Welcome Dialog */
.welcome-dialog {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 600px;
    max-height: 85vh;
    padding: 32px;
    z-index: 1001;
    overflow-y: auto;
}

.welcome-title {
    margin: 0 0 16px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
}

.welcome-description {
    margin: 0 0 24px 0;
    color: #64748b;
    line-height: 1.6;
}

.workflow-preview {
    background: #f8fafc;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 24px;
}

.workflow-preview h4 {
    margin: 0 0 16px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

.steps-preview {
    list-style: none;
    padding: 0;
    margin: 0;
}

.steps-preview li {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    color: #374151;
}

.steps-preview li.optional {
    opacity: 0.7;
}

.optional-badge {
    background: #fbbf24;
    color: #92400e;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.welcome-actions {
    display: flex;
    justify-content: center;
}

/* Workflow Header */
.workflow-header {
    background: white;
    padding: 24px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 24px;
}

.workflow-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
}

.progress-info h2 {
    margin: 0 0 4px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
}

.progress-info p {
    margin: 0;
    color: #64748b;
    font-size: 1rem;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
}

.progress-root {
    position: relative;
    overflow: hidden;
    background: #e5e7eb;
    border-radius: 99999px;
    width: 100%;
    height: 8px;
}

.progress-indicator {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    width: 100%;
    height: 100%;
    transition: transform 660ms cubic-bezier(0.65, 0, 0.35, 1);
}

.progress-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
}

/* Step Navigation */
.step-navigation {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding: 4px;
}

.step-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 200px;
    flex-shrink: 0;
}

.step-button:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.step-button.active {
    border-color: #3b82f6;
    background: #eff6ff;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.step-button.completed {
    border-color: #10b981;
    background: #f0fdf4;
}

.step-button.completed .step-icon {
    color: #10b981;
}

.step-icon {
    color: #64748b;
    flex-shrink: 0;
}

.step-button.active .step-icon {
    color: #3b82f6;
}

.step-info {
    text-align: left;
}

.step-title {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2px;
}

.step-optional {
    font-size: 0.75rem;
    color: #f59e0b;
    font-weight: 500;
}

/* Step Content */
.step-content {
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    margin-bottom: 24px;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid #f1f5f9;
    background: #fafbfc;
}

.step-title-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.step-title-section h3 {
    margin: 0 0 4px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.step-title-section p {
    margin: 0;
    color: #64748b;
}

.completion-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f0fdf4;
    color: #059669;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.875rem;
}

.step-component {
    padding: 24px;
}

/* Secure Operations Placeholder */
.secure-operations-placeholder {
    text-align: center;
    padding: 48px 24px;
}

.placeholder-icon {
    color: #3b82f6;
    margin-bottom: 16px;
}

.secure-operations-placeholder h3 {
    margin: 0 0 12px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.secure-operations-placeholder p {
    margin: 0 0 24px 0;
    color: #64748b;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Workflow Controls */
.workflow-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.control-center {
    display: flex;
    gap: 12px;
}

.control-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.control-button.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.control-button.primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.control-button.secondary {
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
}

.control-button.secondary:hover:not(:disabled) {
    background: #e2e8f0;
}

.control-button.outline {
    background: transparent;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.control-button.outline:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.control-button.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.control-button.success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Action Button (reusable) */
.action-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.action-button.primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Dialog Overlay */
.dialog-overlay {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    inset: 0;
    animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
    z-index: 1000;
}

.dialog-close {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.dialog-close:hover {
    background: #f1f5f9;
    color: #374151;
}

/* Animations */
@keyframes overlayShow {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .workflow-progress {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .progress-bar-container {
        width: 100%;
        min-width: unset;
    }
}

@media (max-width: 768px) {
    .guided-workflow {
        padding: 16px;
    }
    
    .workflow-header {
        padding: 16px;
    }
    
    .step-navigation {
        flex-direction: column;
        gap: 8px;
    }
    
    .step-button {
        min-width: unset;
        width: 100%;
    }
    
    .step-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        padding: 16px;
    }
    
    .step-component {
        padding: 16px;
    }
    
    .workflow-controls {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .control-center {
        order: -1;
    }
    
    .welcome-dialog {
        padding: 24px;
        width: 95vw;
    }
}

@media (max-width: 480px) {
    .control-button {
        padding: 10px 16px;
        font-size: 0.8rem;
    }
    
    .step-title-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .workflow-progress h2 {
        font-size: 1.25rem;
    }
}
