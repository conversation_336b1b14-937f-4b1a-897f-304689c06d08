use std::path::{Path, PathBuf};
use std::time::SystemTime;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use super::error::{FileManagerError, Result};

/// Represents a file or directory item with comprehensive metadata
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct FileItem {
    /// Unique identifier for the file item
    pub id: String,
    
    /// Absolute path to the file or directory
    pub path: PathBuf,
    
    /// File or directory name (without path)
    pub name: String,
    
    /// File size in bytes (0 for directories)
    pub size: u64,
    
    /// Last modified timestamp
    pub modified: DateTime<Utc>,
    
    /// Created timestamp
    pub created: DateTime<Utc>,
    
    /// Last accessed timestamp
    pub accessed: DateTime<Utc>,
    
    /// File type classification
    pub file_type: FileType,
    
    /// MIME type if available
    pub mime_type: Option<String>,
    
    /// File extension (without the dot)
    pub extension: Option<String>,
    
    /// Whether the file is hidden
    pub is_hidden: bool,
    
    /// Whether the file is read-only
    pub is_readonly: bool,
    
    /// File permissions (Unix-style)
    pub permissions: Option<u32>,
    
    /// File checksum (computed on demand)
    pub checksum: Option<String>,
}

/// File type classification
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileType {
    /// Regular file
    File,
    /// Directory
    Directory,
    /// Symbolic link
    Symlink,
    /// Other special file types
    Other,
}

impl std::fmt::Display for FileType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            FileType::File => write!(f, "File"),
            FileType::Directory => write!(f, "Directory"),
            FileType::Symlink => write!(f, "Symlink"),
            FileType::Other => write!(f, "Other"),
        }
    }
}

impl FileItem {
    /// Enhanced file size detection with cross-platform support
    pub fn get_enhanced_file_size(_path: &Path, metadata: &std::fs::Metadata) -> u64 {
        // For directories, always return 0
        if metadata.is_dir() {
            return 0;
        }
        
        // First attempt: use provided metadata
        let size = metadata.len();
        if size > 0 {
            return size;
        }
        
        // Second attempt: try symlink metadata for special files
        if let Ok(symlink_metadata) = std::fs::symlink_metadata(_path) {
            let symlink_size = symlink_metadata.len();
            if symlink_size > 0 {
                return symlink_size;
            }
        }
        
        // Third attempt: direct file access
        if let Ok(file) = std::fs::File::open(_path) {
            if let Ok(file_metadata) = file.metadata() {
                return file_metadata.len();
            }
        }
        
        // Fallback: return 0 for inaccessible files
        0
    }
    
    /// Create a new FileItem from a path
    pub fn from_path(_path: &Path) -> Result<Self> {
        let metadata = std::fs::metadata(_path)
            .map_err(|e| match e.kind() {
                std::io::ErrorKind::NotFound => FileManagerError::not_found(format!("File not found: {}", _path.display())),
                std::io::ErrorKind::PermissionDenied => FileManagerError::permission_denied(format!("Permission denied: {}", _path.display())),
                _ => FileManagerError::io_error(format!("Failed to read metadata for {}: {}", _path.display(), e)),
            })?;

        let file_name = _path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("Unknown")
            .to_string();

        let extension = _path.extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_string());

        Ok(Self {
            id: uuid::Uuid::new_v4().to_string(),
            name: file_name,
            path: _path.to_path_buf(),
            size: Self::get_enhanced_file_size(_path, &metadata),
            file_type: if metadata.is_dir() {
                FileType::Directory
            } else if metadata.is_file() {
                FileType::File
            } else if metadata.file_type().is_symlink() {
                FileType::Symlink
            } else {
                FileType::Other
            },
            modified: Self::system_time_to_utc(metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH)),
            created: Self::system_time_to_utc(metadata.created().unwrap_or(SystemTime::UNIX_EPOCH)),
            accessed: Self::system_time_to_utc(metadata.accessed().unwrap_or(SystemTime::UNIX_EPOCH)),
            mime_type: None, // TODO: Implement MIME type detection
            extension,
            is_hidden: Self::is_file_hidden(_path),
            is_readonly: metadata.permissions().readonly(),
            permissions: Self::get_file_permissions(&metadata),
            checksum: None, // Computed on demand
        })
    }
    
    /// Check if this item is a file
    pub fn is_file(&self) -> bool {
        matches!(self.file_type, FileType::File)
    }
    
    /// Check if this item is a directory
    pub fn is_directory(&self) -> bool {
        matches!(self.file_type, FileType::Directory)
    }
    
    /// Convert SystemTime to UTC DateTime
    fn system_time_to_utc(time: SystemTime) -> DateTime<Utc> {
        DateTime::from(time)
    }
    
    /// Get file permissions as a u32
    fn get_file_permissions(metadata: &std::fs::Metadata) -> Option<u32> {
        #[cfg(unix)]
        {
            use std::os::unix::fs::MetadataExt;
            Some(metadata.mode())
        }
        
        #[cfg(not(unix))]
        {
            let _ = metadata;
            None
        }
    }
    
    /// Check if a file is hidden
    fn is_file_hidden(_path: &Path) -> bool {
        if let Some(name) = _path.file_name().and_then(|n| n.to_str()) {
            // Unix convention: files starting with '.'
            if name.starts_with('.') {
                return true;
            }
        }
        false
    }
}

/// Format file size in human-readable format
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB", "PB"];
    const THRESHOLD: f64 = 1024.0;
    
    if size == 0 {
        return "0 B".to_string();
    }
    
    let mut size_in_unit = size as f64;
    let mut unit_index = 0;
    
    while size_in_unit >= THRESHOLD && unit_index < UNITS.len() - 1 {
        size_in_unit /= THRESHOLD;
        unit_index += 1;
    }
    
    if size_in_unit.fract() == 0.0 {
        format!("{} {}", size_in_unit as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size_in_unit, UNITS[unit_index])
    }
}
