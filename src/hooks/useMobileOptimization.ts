/**
 * Mobile Optimization Hook
 * Provides mobile-specific optimizations and responsive behavior
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { mobileOptimization, DeviceCapabilities, getOptimalConfig } from '../utils/mobileOptimization';
import { getAccessibilityPreferences } from '../utils/accessibilityManager';
import { debounce, throttle } from '../utils/performanceOptimization';

export interface MobileOptimizationState {
    deviceCapabilities: DeviceCapabilities;
    isLoading: boolean;
    orientation: 'portrait' | 'landscape';
    viewportSize: { width: number; height: number };
    safeArea: { top: number; right: number; bottom: number; left: number };
    networkStatus: 'online' | 'offline' | 'slow';
    batteryLevel?: number;
    isLowPowerMode: boolean;
}

export interface TouchGestureHandlers {
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    onSwipeUp?: () => void;
    onSwipeDown?: () => void;
    onPinch?: (scale: number) => void;
    onLongPress?: () => void;
    onDoubleTap?: () => void;
}

export interface ResponsiveConfig {
    breakpoints: {
        sm: number;
        md: number;
        lg: number;
        xl: number;
    };
    touchTargetSize: number;
    scrollThreshold: number;
    gestureThreshold: number;
}

const defaultConfig: ResponsiveConfig = {
    breakpoints: {
        sm: 640,
        md: 768,
        lg: 1024,
        xl: 1280,
    },
    touchTargetSize: 44,
    scrollThreshold: 10,
    gestureThreshold: 50,
};

export const useMobileOptimization = (config: Partial<ResponsiveConfig> = {}) => {
    const finalConfig = { ...defaultConfig, ...config };
    
    const [state, setState] = useState<MobileOptimizationState>({
        deviceCapabilities: mobileOptimization.getDeviceCapabilities(),
        isLoading: true,
        orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
        viewportSize: { width: window.innerWidth, height: window.innerHeight },
        safeArea: { top: 0, right: 0, bottom: 0, left: 0 },
        networkStatus: navigator.onLine ? 'online' : 'offline',
        isLowPowerMode: false,
    });

    // Debounced resize handler
    const handleResize = useCallback(
        debounce(() => {
            setState(prev => ({
                ...prev,
                orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
                viewportSize: { width: window.innerWidth, height: window.innerHeight },
            }));
        }, 150),
        []
    );

    // Throttled scroll handler
    const handleScroll = useCallback(
        throttle(() => {
            // Trigger scroll-based optimizations
            const event = new CustomEvent('optimizedScroll', {
                detail: { scrollY: window.scrollY }
            });
            document.dispatchEvent(event);
        }, 16), // ~60fps
        []
    );

    // Network status handler
    const handleNetworkChange = useCallback(() => {
        const connection = (navigator as any).connection;
        let networkStatus: 'online' | 'offline' | 'slow' = navigator.onLine ? 'online' : 'offline';
        
        if (connection && navigator.onLine) {
            const slowConnections = ['slow-2g', '2g'];
            if (slowConnections.includes(connection.effectiveType)) {
                networkStatus = 'slow';
            }
        }
        
        setState(prev => ({ ...prev, networkStatus }));
    }, []);

    // Battery status handler
    const handleBatteryChange = useCallback((battery: any) => {
        setState(prev => ({
            ...prev,
            batteryLevel: battery.level * 100,
            isLowPowerMode: battery.level < 0.2 || battery.charging === false,
        }));
    }, []);

    // Initialize mobile optimizations
    useEffect(() => {
        // Apply mobile-specific CSS optimizations
        mobileOptimization.applyMobileOptimizations();
        
        // Setup event listeners
        window.addEventListener('resize', handleResize);
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('online', handleNetworkChange);
        window.addEventListener('offline', handleNetworkChange);
        
        // Setup orientation change listener
        const handleOrientationChange = () => {
            setTimeout(() => {
                setState(prev => ({
                    ...prev,
                    orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
                    viewportSize: { width: window.innerWidth, height: window.innerHeight },
                }));
            }, 100); // Small delay to ensure viewport has updated
        };
        
        window.addEventListener('orientationchange', handleOrientationChange);
        
        // Setup battery API if available
        if ('getBattery' in navigator) {
            (navigator as any).getBattery().then((battery: any) => {
                handleBatteryChange(battery);
                battery.addEventListener('levelchange', () => handleBatteryChange(battery));
                battery.addEventListener('chargingchange', () => handleBatteryChange(battery));
            });
        }
        
        // Detect safe area insets
        const detectSafeArea = () => {
            const safeAreaTop = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sat') || '0');
            const safeAreaRight = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sar') || '0');
            const safeAreaBottom = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sab') || '0');
            const safeAreaLeft = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sal') || '0');
            
            setState(prev => ({
                ...prev,
                safeArea: {
                    top: safeAreaTop,
                    right: safeAreaRight,
                    bottom: safeAreaBottom,
                    left: safeAreaLeft,
                }
            }));
        };
        
        detectSafeArea();
        setState(prev => ({ ...prev, isLoading: false }));
        
        return () => {
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('online', handleNetworkChange);
            window.removeEventListener('offline', handleNetworkChange);
            window.removeEventListener('orientationchange', handleOrientationChange);
        };
    }, [handleResize, handleScroll, handleNetworkChange]);

    // Responsive breakpoint utilities
    const breakpoints = useMemo(() => ({
        isSm: state.viewportSize.width >= finalConfig.breakpoints.sm,
        isMd: state.viewportSize.width >= finalConfig.breakpoints.md,
        isLg: state.viewportSize.width >= finalConfig.breakpoints.lg,
        isXl: state.viewportSize.width >= finalConfig.breakpoints.xl,
        isSmOnly: state.viewportSize.width >= finalConfig.breakpoints.sm && state.viewportSize.width < finalConfig.breakpoints.md,
        isMdOnly: state.viewportSize.width >= finalConfig.breakpoints.md && state.viewportSize.width < finalConfig.breakpoints.lg,
        isLgOnly: state.viewportSize.width >= finalConfig.breakpoints.lg && state.viewportSize.width < finalConfig.breakpoints.xl,
    }), [state.viewportSize.width, finalConfig.breakpoints]);

    // Touch gesture handler
    const createGestureHandler = useCallback((handlers: TouchGestureHandlers) => {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;
        let tapCount = 0;
        let tapTimeout: any;

        const handleTouchStart = (e: TouchEvent) => {
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
            touchStartTime = Date.now();
        };

        const handleTouchEnd = (e: TouchEvent) => {
            const touch = e.changedTouches[0];
            const touchEndX = touch.clientX;
            const touchEndY = touch.clientY;
            const touchEndTime = Date.now();
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const deltaTime = touchEndTime - touchStartTime;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            // Long press detection
            if (deltaTime > 500 && distance < finalConfig.gestureThreshold && handlers.onLongPress) {
                handlers.onLongPress();
                return;
            }

            // Swipe detection
            if (distance > finalConfig.gestureThreshold) {
                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                    // Horizontal swipe
                    if (deltaX > 0 && handlers.onSwipeRight) {
                        handlers.onSwipeRight();
                    } else if (deltaX < 0 && handlers.onSwipeLeft) {
                        handlers.onSwipeLeft();
                    }
                } else {
                    // Vertical swipe
                    if (deltaY > 0 && handlers.onSwipeDown) {
                        handlers.onSwipeDown();
                    } else if (deltaY < 0 && handlers.onSwipeUp) {
                        handlers.onSwipeUp();
                    }
                }
                return;
            }

            // Tap detection
            if (distance < finalConfig.gestureThreshold && deltaTime < 300) {
                tapCount++;
                
                if (tapCount === 1) {
                    tapTimeout = setTimeout(() => {
                        tapCount = 0;
                    }, 300);
                } else if (tapCount === 2 && handlers.onDoubleTap) {
                    clearTimeout(tapTimeout);
                    tapCount = 0;
                    handlers.onDoubleTap();
                }
            }
        };

        return {
            onTouchStart: handleTouchStart,
            onTouchEnd: handleTouchEnd,
        };
    }, [finalConfig.gestureThreshold]);

    // Performance optimization recommendations
    const getOptimizationRecommendations = useCallback(() => {
        const recommendations: string[] = [];
        // const optimalConfig = getOptimalConfig(); // TODO: Use for optimization recommendations
        const accessibilityPrefs = getAccessibilityPreferences();

        if (state.deviceCapabilities.isMobile) {
            recommendations.push('Mobile device detected - using optimized settings');
        }

        if (state.networkStatus === 'slow') {
            recommendations.push('Slow network detected - reducing image quality and enabling aggressive caching');
        }

        if (state.isLowPowerMode) {
            recommendations.push('Low battery detected - disabling animations and reducing background processing');
        }

        if (accessibilityPrefs.reducedMotion) {
            recommendations.push('Reduced motion preference detected - disabling animations');
        }

        if (state.deviceCapabilities.screenSize === 'small') {
            recommendations.push('Small screen detected - using compact layout');
        }

        return recommendations;
    }, [state]);

    // Adaptive configuration based on device capabilities
    const adaptiveConfig = useMemo(() => {
        const baseConfig = getOptimalConfig();
        const accessibilityPrefs = getAccessibilityPreferences();
        
        return {
            ...baseConfig,
            enableAnimations: baseConfig.enableAnimations && 
                             !state.isLowPowerMode && 
                             !accessibilityPrefs.reducedMotion,
            maxConcurrentFiles: state.networkStatus === 'slow' ? 1 : baseConfig.maxConcurrentFiles,
            imageQuality: state.networkStatus === 'slow' ? 'low' : baseConfig.imageQuality,
        };
    }, [state.isLowPowerMode, state.networkStatus]);

    return {
        // State
        ...state,
        breakpoints,
        adaptiveConfig,
        
        // Utilities
        createGestureHandler,
        getOptimizationRecommendations,
        
        // Helper functions
        isMobile: state.deviceCapabilities.isMobile,
        isTablet: state.deviceCapabilities.isTablet,
        hasTouch: state.deviceCapabilities.hasTouch,
        isPortrait: state.orientation === 'portrait',
        isLandscape: state.orientation === 'landscape',
        hasLimitedResources: mobileOptimization.hasLimitedResources(),
    };
};

export default useMobileOptimization;
