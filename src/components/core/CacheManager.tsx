/**
 * Cache Management Component
 * Provides advanced caching strategies and cache monitoring
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Database, Trash2, RefreshCw, Bar<PERSON><PERSON>3, Settings, HardDrive, Clock, TrendingUp } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import './CacheManager.css';

interface CacheStatistics {
    total_entries: number;
    cache_size_mb: number;
    hit_rate_percent: number;
    miss_rate_percent: number;
    evictions: number;
    expirations: number;
    last_cleanup: number;
    memory_usage_mb: number;
}

interface CacheConfig {
    max_entries: number;
    default_ttl_seconds: number;
    enable_file_tracking: boolean;
    cleanup_interval_seconds: number;
    target_hit_rate: number;
}

interface CacheEntry {
    key: string;
    size_bytes: number;
    created_at: number;
    last_accessed: number;
    access_count: number;
    ttl_seconds: number;
    entry_type: string;
}

const CacheManager: React.FC = () => {
    const [statistics, setStatistics] = useState<CacheStatistics | null>(null);
    const [config, setConfig] = useState<CacheConfig | null>(null);
    const [entries, setEntries] = useState<CacheEntry[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showConfig, setShowConfig] = useState(false);
    const [autoRefresh, setAutoRefresh] = useState(false);

    // Fetch cache statistics
    const fetchStatistics = useCallback(async () => {
        try {
            setIsLoading(true);
            const stats = await invoke<CacheStatistics>('get_cache_statistics');
            setStatistics(stats);
        } catch (error) {
            console.error('Failed to fetch cache statistics:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Fetch cache configuration
    const fetchConfig = useCallback(async () => {
        try {
            const cacheConfig = await invoke<CacheConfig>('get_cache_config');
            setConfig(cacheConfig);
        } catch (error) {
            console.error('Failed to fetch cache config:', error);
        }
    }, []);

    // Fetch cache entries
    const fetchEntries = useCallback(async () => {
        try {
            const cacheEntries = await invoke<CacheEntry[]>('get_cache_entries');
            setEntries(cacheEntries);
        } catch (error) {
            console.error('Failed to fetch cache entries:', error);
        }
    }, []);

    // Clear cache
    const clearCache = useCallback(async () => {
        try {
            setIsLoading(true);
            await invoke('clear_cache');
            await fetchStatistics();
            await fetchEntries();
        } catch (error) {
            console.error('Failed to clear cache:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchStatistics, fetchEntries]);

    // Cleanup expired entries
    const cleanupCache = useCallback(async () => {
        try {
            setIsLoading(true);
            await invoke('cleanup_cache');
            await fetchStatistics();
            await fetchEntries();
        } catch (error) {
            console.error('Failed to cleanup cache:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchStatistics, fetchEntries]);

    // Update cache configuration
    const updateConfig = useCallback(async (newConfig: Partial<CacheConfig>) => {
        try {
            setIsLoading(true);
            await invoke('update_cache_config', { config: newConfig });
            await fetchConfig();
        } catch (error) {
            console.error('Failed to update cache config:', error);
        } finally {
            setIsLoading(false);
        }
    }, [fetchConfig]);

    // Auto-refresh effect
    useEffect(() => {
        if (!autoRefresh) return;

        const interval = setInterval(() => {
            fetchStatistics();
            fetchEntries();
        }, 5000);

        return () => clearInterval(interval);
    }, [autoRefresh, fetchStatistics, fetchEntries]);

    // Initial data fetch
    useEffect(() => {
        fetchStatistics();
        fetchConfig();
        fetchEntries();
    }, [fetchStatistics, fetchConfig, fetchEntries]);

    // Format bytes to human readable (currently unused but may be needed for future features)
    // const formatBytes = (bytes: number): string => {
    //     if (bytes === 0) return '0 B';
    //     const k = 1024;
    //     const sizes = ['B', 'KB', 'MB', 'GB'];
    //     const i = Math.floor(Math.log(bytes) / Math.log(k));
    //     return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    // };

    // Format timestamp
    const formatTimestamp = (timestamp: number): string => {
        return new Date(timestamp * 1000).toLocaleString();
    };

    // Calculate cache efficiency
    const getCacheEfficiency = (): string => {
        if (!statistics) return 'Unknown';
        if (statistics.hit_rate_percent >= 90) return 'Excellent';
        if (statistics.hit_rate_percent >= 80) return 'Good';
        if (statistics.hit_rate_percent >= 70) return 'Fair';
        return 'Poor';
    };

    return (
        <div className="cache-manager">
            <div className="cache-header">
                <div className="header-content">
                    <Database className="header-icon" />
                    <div>
                        <h3>Cache Management</h3>
                        <p>Advanced caching strategies and monitoring ({entries.length} entries)</p>
                    </div>
                </div>
                
                <div className="cache-controls">
                    <button
                        className="refresh-button"
                        onClick={fetchStatistics}
                        disabled={isLoading}
                        aria-label="Refresh cache data"
                    >
                        <RefreshCw className={isLoading ? 'spinning' : ''} size={16} />
                        Refresh
                    </button>
                    
                    <button
                        className="cleanup-button"
                        onClick={cleanupCache}
                        disabled={isLoading}
                        aria-label="Cleanup expired entries"
                    >
                        <Clock size={16} />
                        Cleanup
                    </button>
                    
                    <button
                        className="clear-button"
                        onClick={clearCache}
                        disabled={isLoading}
                        aria-label="Clear all cache"
                    >
                        <Trash2 size={16} />
                        Clear
                    </button>
                    
                    <button
                        className="config-button"
                        onClick={() => setShowConfig(!showConfig)}
                        aria-label="Toggle configuration"
                    >
                        <Settings size={16} />
                        Config
                    </button>
                </div>
            </div>

            {/* Cache Statistics */}
            {statistics && (
                <div className="cache-statistics">
                    <div className="stats-grid">
                        <div className="stat-card">
                            <div className="stat-header">
                                <HardDrive size={20} />
                                <span>Cache Size</span>
                            </div>
                            <div className="stat-value">{statistics.cache_size_mb.toFixed(1)} MB</div>
                            <div className="stat-detail">{statistics.total_entries} entries</div>
                        </div>
                        
                        <div className="stat-card">
                            <div className="stat-header">
                                <TrendingUp size={20} />
                                <span>Hit Rate</span>
                            </div>
                            <div className="stat-value">{statistics.hit_rate_percent.toFixed(1)}%</div>
                            <div className="stat-detail">{getCacheEfficiency()}</div>
                        </div>
                        
                        <div className="stat-card">
                            <div className="stat-header">
                                <BarChart3 size={20} />
                                <span>Memory Usage</span>
                            </div>
                            <div className="stat-value">{statistics.memory_usage_mb.toFixed(1)} MB</div>
                            <div className="stat-detail">{statistics.evictions} evictions</div>
                        </div>
                        
                        <div className="stat-card">
                            <div className="stat-header">
                                <Clock size={20} />
                                <span>Last Cleanup</span>
                            </div>
                            <div className="stat-value">
                                {statistics.last_cleanup ? formatTimestamp(statistics.last_cleanup) : 'Never'}
                            </div>
                            <div className="stat-detail">{statistics.expirations} expired</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Cache Configuration */}
            {showConfig && config && (
                <div className="cache-config">
                    <h4>Cache Configuration</h4>
                    <div className="config-grid">
                        <div className="config-item">
                            <label>Max Entries</label>
                            <input
                                type="number"
                                value={config.max_entries}
                                onChange={(e) => updateConfig({ max_entries: parseInt(e.target.value) })}
                                min="100"
                                max="10000"
                            />
                        </div>
                        
                        <div className="config-item">
                            <label>Default TTL (seconds)</label>
                            <input
                                type="number"
                                value={config.default_ttl_seconds}
                                onChange={(e) => updateConfig({ default_ttl_seconds: parseInt(e.target.value) })}
                                min="60"
                                max="86400"
                            />
                        </div>
                        
                        <div className="config-item">
                            <label>Target Hit Rate (%)</label>
                            <input
                                type="number"
                                value={config.target_hit_rate}
                                onChange={(e) => updateConfig({ target_hit_rate: parseFloat(e.target.value) })}
                                min="50"
                                max="100"
                                step="0.1"
                            />
                        </div>
                        
                        <div className="config-item">
                            <label>
                                <input
                                    type="checkbox"
                                    checked={config.enable_file_tracking}
                                    onChange={(e) => updateConfig({ enable_file_tracking: e.target.checked })}
                                />
                                Enable File Tracking
                            </label>
                        </div>
                    </div>
                </div>
            )}

            {/* Auto-refresh toggle */}
            <div className="auto-refresh">
                <label>
                    <input
                        type="checkbox"
                        checked={autoRefresh}
                        onChange={(e) => setAutoRefresh(e.target.checked)}
                    />
                    Auto-refresh every 5 seconds
                </label>
            </div>
        </div>
    );
};

export default CacheManager;
