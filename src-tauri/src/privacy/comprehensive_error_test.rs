/// Comprehensive Error Reporting Validation Tests
/// 
/// These tests validate that our enhanced detection system captures and reports
/// ALL error conditions without any filtering or suppression mechanisms.

use std::collections::HashMap;
use crate::privacy::context_aware_detector::{
    ContextAwareDetector, DocumentContext
};
use crate::privacy::enhanced_crypto_detector::EnhancedCryptoDetector;
use crate::privacy::comprehensive_error_reporting::{
    GLOBAL_ERROR_REPORTER
};

#[cfg(test)]
mod comprehensive_error_tests {
    use super::*;

    /// Test that all validation errors are captured and reported
    #[tokio::test]
    async fn test_comprehensive_error_capture() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let detector = ContextAwareDetector::new();
        
        // Test with various problematic inputs that should generate errors/warnings
        let very_long_text = "x".repeat(10000);
        let test_cases = vec![
            // Edge cases that might cause issues
            ("", "empty_text"),
            ("a", "single_character"),
            ("123", "too_short_number"),
            ("***********", "sequential_ssn"),
            ("***********", "repeated_ssn"),
            ("***********", "invalid_area_ssn"),
            ("***********", "high_area_ssn"),
            // Very long text
            (very_long_text.as_str(), "very_long_text"),
            // Special characters
            ("SSN: *********** with émojis 🔒", "unicode_text"),
            // Multiple potential SSNs
            ("SSN1: ***********, SSN2: ***********", "multiple_ssns"),
        ];
        
        for (text, description) in test_cases {
            println!("🧪 Testing error capture for: {}", description);
            
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: Some("test".to_string()),
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            // This should not panic or fail, but should capture all issues
            let result = detector.detect_with_context(text, &context).await;
            
            match result {
                Ok(findings) => {
                    println!("✅ Detection completed for {}: {} findings", description, findings.len());
                },
                Err(e) => {
                    println!("⚠️  Detection error for {}: {}", description, e);
                    // Errors should be captured in the reporter, not cause test failure
                }
            }
        }
        
        // Verify that errors and warnings were captured
        if let Ok(reporter) = GLOBAL_ERROR_REPORTER.lock() {
            let report = reporter.get_comprehensive_report();
            
            println!("📊 Comprehensive Error Report:");
            println!("   Total Errors: {}", report.total_errors);
            println!("   Total Warnings: {}", report.total_warnings);
            println!("   Total Debug Messages: {}", report.total_debug_messages);
            println!("   Total Performance Metrics: {}", report.total_performance_metrics);
            
            // We expect warnings for invalid SSN patterns
            assert!(report.total_warnings > 0, "Expected warnings for invalid SSN patterns");
            
            // We expect debug messages for all processing steps
            assert!(report.total_debug_messages > 0, "Expected debug messages for processing steps");
            
            // We expect performance metrics for all operations
            assert!(report.total_performance_metrics > 0, "Expected performance metrics");
            
            // Print detailed error information
            for (i, error) in report.errors.iter().enumerate() {
                println!("   Error {}: {}", i + 1, error);
            }
            
            // Print warning summary
            println!("   Warning Summary:");
            for (i, warning) in report.warnings.iter().take(5).enumerate() {
                println!("     {}: {}", i + 1, warning);
            }
            
            if report.warnings.len() > 5 {
                println!("     ... and {} more warnings", report.warnings.len() - 5);
            }
        }
    }
    
    /// Test cryptocurrency detection error reporting
    #[test]
    fn test_crypto_error_reporting() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let detector = EnhancedCryptoDetector::new();
        
        // Test with various problematic crypto inputs
        let too_long_bitcoin = "1".to_string() + &"x".repeat(100);
        let invalid_ethereum = "0x".to_string() + &"g".repeat(40);
        let too_long_bech32 = "bc1".to_string() + &"0".repeat(100);
        let very_long_potential = "potential crypto: ".to_string() + &"1".repeat(1000);

        let test_cases = vec![
            ("", "empty_text"),
            ("invalid_crypto_address_123", "invalid_address"),
            (too_long_bitcoin.as_str(), "too_long_bitcoin_like"),
            (invalid_ethereum.as_str(), "invalid_ethereum_chars"),
            (too_long_bech32.as_str(), "too_long_bech32"),
            // Very long text with potential crypto patterns
            (very_long_potential.as_str(), "very_long_potential"),
        ];
        
        for (text, description) in test_cases {
            println!("🧪 Testing crypto error capture for: {}", description);
            
            let result = detector.detect_crypto_assets(text);
            
            match result {
                Ok(findings) => {
                    println!("✅ Crypto detection completed for {}: {} findings", description, findings.len());
                },
                Err(e) => {
                    println!("⚠️  Crypto detection error for {}: {}", description, e);
                }
            }
        }
        
        // Verify error capture
        if let Ok(reporter) = GLOBAL_ERROR_REPORTER.lock() {
            let report = reporter.get_comprehensive_report();
            
            println!("📊 Crypto Detection Error Report:");
            println!("   Total Errors: {}", report.total_errors);
            println!("   Total Warnings: {}", report.total_warnings);
            println!("   Total Debug Messages: {}", report.total_debug_messages);
            
            // Print any captured errors
            for (i, error) in report.errors.iter().enumerate() {
                println!("   Crypto Error {}: {}", i + 1, error);
            }
        }
    }
    
    /// Test that no errors are suppressed during normal operation
    #[tokio::test]
    async fn test_no_error_suppression() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let detector = ContextAwareDetector::new();
        
        // Test with a mix of valid and invalid inputs
        let mixed_text = "Valid SSN: *********** in employment context. Invalid: ***********. Another invalid: ***********.";
        
        let context = DocumentContext {
            full_text: mixed_text.to_string(),
            document_type: Some("employment".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };
        
        let findings = detector.detect_with_context(mixed_text, &context).await.unwrap();
        
        // Verify comprehensive reporting
        if let Ok(reporter) = GLOBAL_ERROR_REPORTER.lock() {
            let report = reporter.get_comprehensive_report();
            
            println!("📊 Mixed Input Test Report:");
            println!("   Findings: {}", findings.len());
            println!("   Total Errors: {}", report.total_errors);
            println!("   Total Warnings: {}", report.total_warnings);
            println!("   Total Debug Messages: {}", report.total_debug_messages);
            
            // We should have debug messages for all processing steps
            assert!(report.total_debug_messages > 10, 
                   "Expected extensive debug logging, got {}", report.total_debug_messages);
            
            // We should have warnings for invalid SSN patterns
            assert!(report.total_warnings > 0, 
                   "Expected warnings for invalid SSN patterns, got {}", report.total_warnings);
            
            // Verify that all validation attempts are logged
            let validation_debug_count = report.debug_messages.iter()
                .filter(|msg| msg.contains("validation"))
                .count();
            
            assert!(validation_debug_count > 0, 
                   "Expected validation debug messages, got {}", validation_debug_count);
            
            println!("✅ Comprehensive reporting verified: {} debug messages, {} warnings", 
                    report.total_debug_messages, report.total_warnings);
        }
    }
    
    /// Test performance metrics collection
    #[tokio::test]
    async fn test_performance_metrics_collection() {
        // Clear any existing metrics
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let detector = ContextAwareDetector::new();
        
        // Run multiple detection operations
        for i in 0..5 {
            let text = format!("Test {} with SSN: ***********", i);
            let context = DocumentContext {
                full_text: text.clone(),
                document_type: Some("test".to_string()),
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            let _ = detector.detect_with_context(&text, &context).await;
        }
        
        // Verify performance metrics were collected
        if let Ok(reporter) = GLOBAL_ERROR_REPORTER.lock() {
            let report = reporter.get_comprehensive_report();
            
            println!("📊 Performance Metrics Report:");
            println!("   Total Performance Metrics: {}", report.total_performance_metrics);
            
            // We should have metrics for each operation
            assert!(report.total_performance_metrics >= 5, 
                   "Expected at least 5 performance metrics, got {}", report.total_performance_metrics);
            
            // Print performance summary
            for (i, metric) in report.performance_metrics.iter().enumerate() {
                println!("   Metric {}: {} took {}ms (input: {} bytes, output: {} items)", 
                        i + 1, metric.operation, metric.duration_ms, metric.input_size, metric.output_size);
            }
            
            println!("✅ Performance metrics collection verified");
        }
    }
    
    /// Test error count reporting by type
    #[tokio::test]
    async fn test_error_categorization() {
        // Clear any existing errors
        if let Ok(mut reporter) = GLOBAL_ERROR_REPORTER.lock() {
            *reporter = crate::privacy::comprehensive_error_reporting::ComprehensiveErrorReporter::new();
        }
        
        let detector = ContextAwareDetector::new();
        
        // Generate various types of potential errors
        let very_long_ssn_text = "SSN: ".to_string() + &"x".repeat(1000);
        let problematic_inputs = vec![
            ("", "empty"),
            ("***********", "sequential"),
            ("***********", "invalid_area"),
            (very_long_ssn_text.as_str(), "very_long"),
        ];
        
        for (text, _desc) in problematic_inputs {
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: Some("test".to_string()),
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            let _ = detector.detect_with_context(text, &context).await;
        }
        
        // Verify error categorization
        if let Ok(reporter) = GLOBAL_ERROR_REPORTER.lock() {
            let error_counts = reporter.get_error_counts_by_type();
            
            println!("📊 Error Categorization Report:");
            for (error_type, count) in error_counts {
                println!("   {}: {} errors", error_type, count);
            }
            
            println!("✅ Error categorization verified");
        }
    }
}
