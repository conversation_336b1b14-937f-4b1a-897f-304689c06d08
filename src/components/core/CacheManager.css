/* Cache Manager Component Styling */

.cache-manager {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin: 24px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.cache-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f1f5f9;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    color: #3b82f6;
    width: 24px;
    height: 24px;
}

.cache-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.cache-header p {
    margin: 2px 0 0 0;
    color: #64748b;
    font-size: 0.875rem;
}

/* Controls */
.cache-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.cache-controls button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #475569;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cache-controls button:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #334155;
}

.cache-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.refresh-button:hover:not(:disabled) {
    background: #dbeafe;
    border-color: #3b82f6;
    color: #1d4ed8;
}

.cleanup-button:hover:not(:disabled) {
    background: #fef3c7;
    border-color: #f59e0b;
    color: #d97706;
}

.clear-button:hover:not(:disabled) {
    background: #fee2e2;
    border-color: #ef4444;
    color: #dc2626;
}

.config-button:hover:not(:disabled) {
    background: #f3e8ff;
    border-color: #8b5cf6;
    color: #7c3aed;
}

.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Statistics Grid */
.cache-statistics {
    margin-bottom: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.stat-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
}

.stat-card:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.stat-detail {
    font-size: 0.75rem;
    color: #64748b;
}

/* Configuration */
.cache-config {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.cache-config h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.config-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-item input[type="number"] {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    background: white;
    transition: border-color 0.2s ease;
}

.config-item input[type="number"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
}

/* Auto-refresh */
.auto-refresh {
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
}

.auto-refresh label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: #64748b;
    cursor: pointer;
}

.auto-refresh input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .cache-manager {
        padding: 16px;
        margin: 16px 0;
    }
    
    .cache-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .cache-controls {
        flex-wrap: wrap;
        width: 100%;
    }
    
    .cache-controls button {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .config-grid {
        grid-template-columns: 1fr;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .cache-manager {
        background: #1e293b;
        color: #f1f5f9;
    }
    
    .cache-header {
        border-bottom-color: #334155;
    }
    
    .cache-header h3 {
        color: #f1f5f9;
    }
    
    .stat-card {
        background: #334155;
        border-color: #475569;
        color: #f1f5f9;
    }
    
    .stat-card:hover {
        background: #475569;
        border-color: #64748b;
    }
    
    .stat-value {
        color: #f1f5f9;
    }
    
    .cache-config {
        background: #334155;
        border-color: #475569;
    }
    
    .cache-config h4 {
        color: #f1f5f9;
    }
    
    .config-item input[type="number"] {
        background: #475569;
        border-color: #64748b;
        color: #f1f5f9;
    }
    
    .auto-refresh {
        border-top-color: #475569;
    }
}
