/// Comprehensive Error Reporting System for Enhanced Pattern Recognition
/// 
/// This module provides complete visibility into all error conditions, validation failures,
/// and edge cases without any filtering or suppression mechanisms.

use std::fmt;
use std::error::Error as StdError;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};

/// Comprehensive error types for enhanced detection system
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum DetectionError {
    /// Pattern matching errors
    PatternError {
        pattern_type: String,
        pattern_description: String,
        input_text: String,
        error_details: String,
        timestamp: DateTime<Utc>,
    },
    
    /// Validation errors with full context
    ValidationError {
        validator_type: String,
        candidate_content: String,
        validation_details: String,
        context_info: String,
        error_reason: String,
        timestamp: DateTime<Utc>,
    },
    
    /// Confidence calculation errors
    ConfidenceError {
        calculator_type: String,
        candidate_content: String,
        base_confidence: f32,
        adjustment_details: String,
        error_reason: String,
        timestamp: DateTime<Utc>,
    },
    
    /// Cryptocurrency validation errors
    CryptoValidationError {
        crypto_type: String,
        address_content: String,
        validation_method: String,
        expected_format: String,
        actual_format: String,
        error_details: String,
        timestamp: DateTime<Utc>,
    },
    
    /// Context analysis errors
    ContextAnalysisError {
        document_type: Option<String>,
        text_length: usize,
        analysis_stage: String,
        error_details: String,
        timestamp: DateTime<Utc>,
    },
    
    /// Performance and resource errors
    PerformanceError {
        operation_type: String,
        processing_time_ms: u64,
        memory_usage_mb: Option<f64>,
        error_details: String,
        timestamp: DateTime<Utc>,
    },
    
    /// Integration errors between components
    IntegrationError {
        source_component: String,
        target_component: String,
        operation: String,
        error_details: String,
        timestamp: DateTime<Utc>,
    },
}

impl fmt::Display for DetectionError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DetectionError::PatternError { pattern_type, pattern_description, input_text, error_details, timestamp } => {
                write!(f, "[{}] Pattern Error in {}: {} | Input: '{}' | Details: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       pattern_type, pattern_description, 
                       input_text.chars().take(100).collect::<String>(), 
                       error_details)
            },
            DetectionError::ValidationError { validator_type, candidate_content, validation_details, context_info, error_reason, timestamp } => {
                write!(f, "[{}] Validation Error in {}: '{}' | Context: {} | Validation: {} | Reason: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       validator_type, candidate_content, context_info, validation_details, error_reason)
            },
            DetectionError::ConfidenceError { calculator_type, candidate_content, base_confidence, adjustment_details, error_reason, timestamp } => {
                write!(f, "[{}] Confidence Error in {}: '{}' | Base: {:.2} | Adjustments: {} | Reason: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       calculator_type, candidate_content, base_confidence, adjustment_details, error_reason)
            },
            DetectionError::CryptoValidationError { crypto_type, address_content, validation_method, expected_format, actual_format, error_details, timestamp } => {
                write!(f, "[{}] Crypto Validation Error for {}: '{}' | Method: {} | Expected: {} | Actual: {} | Details: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       crypto_type, address_content, validation_method, expected_format, actual_format, error_details)
            },
            DetectionError::ContextAnalysisError { document_type, text_length, analysis_stage, error_details, timestamp } => {
                write!(f, "[{}] Context Analysis Error: Stage '{}' | Doc Type: {:?} | Text Length: {} | Details: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       analysis_stage, document_type, text_length, error_details)
            },
            DetectionError::PerformanceError { operation_type, processing_time_ms, memory_usage_mb, error_details, timestamp } => {
                write!(f, "[{}] Performance Error in {}: {}ms | Memory: {:?}MB | Details: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       operation_type, processing_time_ms, memory_usage_mb, error_details)
            },
            DetectionError::IntegrationError { source_component, target_component, operation, error_details, timestamp } => {
                write!(f, "[{}] Integration Error: {} -> {} | Operation: {} | Details: {}", 
                       timestamp.format("%Y-%m-%d %H:%M:%S UTC"), 
                       source_component, target_component, operation, error_details)
            },
        }
    }
}

impl StdError for DetectionError {}

/// Comprehensive error reporter that captures ALL issues without filtering
#[derive(Debug, Clone)]
pub struct ComprehensiveErrorReporter {
    /// All errors captured (no filtering)
    pub all_errors: Vec<DetectionError>,
    /// Warnings that might indicate potential issues
    pub all_warnings: Vec<String>,
    /// Debug information for troubleshooting
    pub debug_info: Vec<String>,
    /// Performance metrics for all operations
    pub performance_metrics: Vec<PerformanceMetric>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetric {
    pub operation: String,
    pub duration_ms: u64,
    pub memory_usage_mb: Option<f64>,
    pub input_size: usize,
    pub output_size: usize,
    pub timestamp: DateTime<Utc>,
}

impl ComprehensiveErrorReporter {
    pub fn new() -> Self {
        Self {
            all_errors: Vec::new(),
            all_warnings: Vec::new(),
            debug_info: Vec::new(),
            performance_metrics: Vec::new(),
        }
    }
    
    /// Report an error without any filtering or suppression
    pub fn report_error(&mut self, error: DetectionError) {
        // Log to console immediately for visibility
        eprintln!("🚨 DETECTION ERROR: {}", error);
        
        // Store for analysis
        self.all_errors.push(error);
    }
    
    /// Report a warning without any filtering
    pub fn report_warning(&mut self, warning: String) {
        let timestamped_warning = format!("[{}] WARNING: {}", 
                                        Utc::now().format("%Y-%m-%d %H:%M:%S UTC"), 
                                        warning);
        
        // Log to console immediately
        eprintln!("⚠️  {}", timestamped_warning);
        
        // Store for analysis
        self.all_warnings.push(timestamped_warning);
    }
    
    /// Report debug information without any filtering
    pub fn report_debug(&mut self, debug_msg: String) {
        let timestamped_debug = format!("[{}] DEBUG: {}", 
                                      Utc::now().format("%Y-%m-%d %H:%M:%S UTC"), 
                                      debug_msg);
        
        // Always log debug info for full visibility
        println!("🔍 {}", timestamped_debug);
        
        // Store for analysis
        self.debug_info.push(timestamped_debug);
    }
    
    /// Record performance metrics for all operations
    pub fn record_performance(&mut self, metric: PerformanceMetric) {
        println!("📊 PERFORMANCE: {} took {}ms (input: {} bytes, output: {} items)", 
                metric.operation, metric.duration_ms, metric.input_size, metric.output_size);
        
        self.performance_metrics.push(metric);
    }
    
    /// Get comprehensive report of all issues (no filtering)
    pub fn get_comprehensive_report(&self) -> ComprehensiveReport {
        ComprehensiveReport {
            total_errors: self.all_errors.len(),
            total_warnings: self.all_warnings.len(),
            total_debug_messages: self.debug_info.len(),
            total_performance_metrics: self.performance_metrics.len(),
            errors: self.all_errors.clone(),
            warnings: self.all_warnings.clone(),
            debug_messages: self.debug_info.clone(),
            performance_metrics: self.performance_metrics.clone(),
            report_timestamp: Utc::now(),
        }
    }
    
    /// Check if any errors occurred (for testing)
    pub fn has_errors(&self) -> bool {
        !self.all_errors.is_empty()
    }
    
    /// Check if any warnings occurred (for testing)
    pub fn has_warnings(&self) -> bool {
        !self.all_warnings.is_empty()
    }
    
    /// Get error count by type
    pub fn get_error_counts_by_type(&self) -> std::collections::HashMap<String, usize> {
        let mut counts = std::collections::HashMap::new();
        
        for error in &self.all_errors {
            let error_type = match error {
                DetectionError::PatternError { .. } => "PatternError",
                DetectionError::ValidationError { .. } => "ValidationError",
                DetectionError::ConfidenceError { .. } => "ConfidenceError",
                DetectionError::CryptoValidationError { .. } => "CryptoValidationError",
                DetectionError::ContextAnalysisError { .. } => "ContextAnalysisError",
                DetectionError::PerformanceError { .. } => "PerformanceError",
                DetectionError::IntegrationError { .. } => "IntegrationError",
            };
            
            *counts.entry(error_type.to_string()).or_insert(0) += 1;
        }
        
        counts
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComprehensiveReport {
    pub total_errors: usize,
    pub total_warnings: usize,
    pub total_debug_messages: usize,
    pub total_performance_metrics: usize,
    pub errors: Vec<DetectionError>,
    pub warnings: Vec<String>,
    pub debug_messages: Vec<String>,
    pub performance_metrics: Vec<PerformanceMetric>,
    pub report_timestamp: DateTime<Utc>,
}

impl Default for ComprehensiveErrorReporter {
    fn default() -> Self {
        Self::new()
    }
}

/// Global error reporter instance for comprehensive tracking
use std::sync::{Arc, Mutex};
use once_cell::sync::Lazy;

pub static GLOBAL_ERROR_REPORTER: Lazy<Arc<Mutex<ComprehensiveErrorReporter>>> = 
    Lazy::new(|| Arc::new(Mutex::new(ComprehensiveErrorReporter::new())));

/// Convenience macros for error reporting without suppression
#[macro_export]
macro_rules! report_error {
    ($error:expr) => {
        if let Ok(mut reporter) = crate::privacy::comprehensive_error_reporting::GLOBAL_ERROR_REPORTER.lock() {
            reporter.report_error($error);
        }
    };
}

#[macro_export]
macro_rules! report_warning {
    ($warning:expr) => {
        if let Ok(mut reporter) = crate::privacy::comprehensive_error_reporting::GLOBAL_ERROR_REPORTER.lock() {
            reporter.report_warning($warning.to_string());
        }
    };
}

#[macro_export]
macro_rules! report_debug {
    ($debug:expr) => {
        if let Ok(mut reporter) = crate::privacy::comprehensive_error_reporting::GLOBAL_ERROR_REPORTER.lock() {
            reporter.report_debug($debug.to_string());
        }
    };
}
