.configuration-panel {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.configuration-panel.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.config-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.config-header h2 {
    margin: 0 0 10px 0;
    font-size: 2.2em;
    font-weight: 600;
    color: #2c3e50;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-header p {
    margin: 0;
    font-size: 1.1em;
    color: #5a6c7d;
    line-height: 1.4;
}

.config-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.tab-button {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.3);
    color: #2c3e50;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.tab-button:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tab-button.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.config-content {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.config-section h3 {
    margin: 0 0 25px 0;
    font-size: 1.6em;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.config-group {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(52, 152, 219, 0.05);
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.config-group h4 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    font-weight: 600;
    color: #34495e;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.checkbox-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: #3498db;
    transform: translateY(-1px);
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3498db;
    cursor: pointer;
}

.checkbox-item span {
    font-weight: 500;
    color: #2c3e50;
}

.slider-container {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
}

.slider-container label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2c3e50;
}

.sensitivity-slider,
.confidence-slider,
.passes-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #ddd;
    outline: none;
    margin: 10px 0;
    cursor: pointer;
}

.sensitivity-slider::-webkit-slider-thumb,
.confidence-slider::-webkit-slider-thumb,
.passes-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3498db;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.4);
    transition: all 0.3s ease;
}

.sensitivity-slider::-webkit-slider-thumb:hover,
.confidence-slider::-webkit-slider-thumb:hover,
.passes-slider::-webkit-slider-thumb:hover {
    background: #2980b9;
    transform: scale(1.1);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
    color: #7f8c8d;
    margin-top: 5px;
}

.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.radio-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.radio-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: #3498db;
}

.radio-item input[type="radio"] {
    width: 16px;
    height: 16px;
    accent-color: #3498db;
    cursor: pointer;
}

.radio-item span {
    font-weight: 500;
    color: #2c3e50;
}

.select-container {
    margin: 15px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
}

.select-container label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.select-container select {
    width: 100%;
    padding: 10px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    background: white;
    color: #2c3e50;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.select-container select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.config-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.reset-button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #c0392b, #e74c3c);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
}

.reset-button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.save-button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

.save-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

.save-button:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.save-button.saving {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.save-button.saved {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.save-button.error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Responsive design */
@media (max-width: 768px) {
    .configuration-panel {
        margin: 10px;
        padding: 15px;
    }
    
    .config-tabs {
        flex-direction: column;
        gap: 5px;
    }
    
    .tab-button {
        width: 100%;
    }
    
    .checkbox-grid {
        grid-template-columns: 1fr;
    }
    
    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .config-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .reset-button,
    .save-button {
        width: 100%;
    }
}
