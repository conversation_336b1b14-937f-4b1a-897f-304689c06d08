/**
 * Performance Optimization Utilities
 * Provides lazy loading, code splitting, and performance monitoring
 */

import { lazy, ComponentType } from 'react';

export interface PerformanceConfig {
    enableLazyLoading: boolean;
    enableCodeSplitting: boolean;
    enableImageOptimization: boolean;
    enableCaching: boolean;
    maxBundleSize: number; // in KB
    maxImageSize: number; // in KB
}

export interface LoadingMetrics {
    componentName: string;
    loadTime: number;
    bundleSize: number;
    cacheHit: boolean;
}

export interface OptimizationResult {
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
    loadTimeImprovement: number;
}

class PerformanceOptimizer {
    private config: PerformanceConfig;
    private loadingMetrics: Map<string, LoadingMetrics> = new Map();
    private componentCache: Map<string, ComponentType<any>> = new Map();
    private imageCache: Map<string, string> = new Map();

    constructor(config: Partial<PerformanceConfig> = {}) {
        this.config = {
            enableLazyLoading: true,
            enableCodeSplitting: true,
            enableImageOptimization: true,
            enableCaching: true,
            maxBundleSize: 500, // 500KB
            maxImageSize: 200, // 200KB
            ...config
        };

        this.initializePerformanceMonitoring();
    }

    /**
     * Initialize performance monitoring
     */
    private initializePerformanceMonitoring(): void {
        // Monitor bundle sizes
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'resource' && entry.name.includes('.js')) {
                        this.trackBundleSize(entry.name, (entry as any).transferSize || 0);
                    }
                });
            });

            try {
                observer.observe({ entryTypes: ['resource'] });
            } catch (error) {
                console.warn('Performance monitoring not supported:', error);
            }
        }
    }

    /**
     * Create lazy-loaded component with performance tracking
     */
    public createLazyComponent<T extends ComponentType<any>>(
        importFn: () => Promise<{ default: T }>,
        componentName: string
    ): ComponentType<any> {
        if (!this.config.enableLazyLoading) {
            // Return synchronous component if lazy loading is disabled
            return lazy(importFn);
        }

        const cachedComponent = this.componentCache.get(componentName);
        if (cachedComponent && this.config.enableCaching) {
            return cachedComponent;
        }

        const startTime = performance.now();
        
        const LazyComponent = lazy(async () => {
            try {
                const module = await importFn();
                const loadTime = performance.now() - startTime;
                
                // Track loading metrics
                this.loadingMetrics.set(componentName, {
                    componentName,
                    loadTime,
                    bundleSize: 0, // Will be updated by resource observer
                    cacheHit: false
                });

                console.log(`Lazy loaded ${componentName} in ${loadTime.toFixed(2)}ms`);
                
                return module;
            } catch (error) {
                console.error(`Failed to lazy load ${componentName}:`, error);
                throw error;
            }
        });

        if (this.config.enableCaching) {
            this.componentCache.set(componentName, LazyComponent);
        }

        return LazyComponent;
    }

    /**
     * Optimize image loading with lazy loading and compression
     */
    public optimizeImage(
        src: string,
        options: {
            width?: number;
            height?: number;
            quality?: number;
            format?: 'webp' | 'jpeg' | 'png';
            lazy?: boolean;
        } = {}
    ): Promise<string> {
        return new Promise((resolve, reject) => {
            const cacheKey = `${src}_${JSON.stringify(options)}`;
            
            // Check cache first
            if (this.config.enableCaching && this.imageCache.has(cacheKey)) {
                resolve(this.imageCache.get(cacheKey)!);
                return;
            }

            const img = new Image();
            
            img.onload = () => {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    if (!ctx) {
                        reject(new Error('Canvas context not available'));
                        return;
                    }

                    // Set dimensions
                    const targetWidth = options.width || img.width;
                    const targetHeight = options.height || img.height;
                    
                    canvas.width = targetWidth;
                    canvas.height = targetHeight;

                    // Draw and compress
                    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
                    
                    const quality = options.quality || 0.8;
                    const format = options.format || 'jpeg';
                    const mimeType = `image/${format}`;
                    
                    const optimizedDataUrl = canvas.toDataURL(mimeType, quality);
                    
                    // Check if optimization was beneficial
                    const originalSize = this.estimateImageSize(src);
                    const optimizedSize = this.estimateImageSize(optimizedDataUrl);
                    
                    if (optimizedSize < originalSize || optimizedSize < this.config.maxImageSize * 1024) {
                        if (this.config.enableCaching) {
                            this.imageCache.set(cacheKey, optimizedDataUrl);
                        }
                        resolve(optimizedDataUrl);
                    } else {
                        // Use original if optimization didn't help
                        resolve(src);
                    }
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => {
                reject(new Error(`Failed to load image: ${src}`));
            };

            img.src = src;
        });
    }

    /**
     * Estimate image size in bytes
     */
    private estimateImageSize(src: string): number {
        if (src.startsWith('data:')) {
            // For data URLs, estimate based on string length
            return src.length * 0.75; // Base64 overhead
        }
        
        // For regular URLs, we can't know the exact size without fetching
        // Return a reasonable estimate
        return 100 * 1024; // 100KB estimate
    }

    /**
     * Track bundle size
     */
    private trackBundleSize(resourceName: string, size: number): void {
        const sizeKB = size / 1024;
        
        if (sizeKB > this.config.maxBundleSize) {
            console.warn(`Large bundle detected: ${resourceName} (${sizeKB.toFixed(2)}KB)`);
        }

        // Update metrics if we have a matching component
        for (const [componentName, metrics] of this.loadingMetrics.entries()) {
            if (resourceName.includes(componentName.toLowerCase())) {
                metrics.bundleSize = sizeKB;
                break;
            }
        }
    }

    /**
     * Preload critical resources
     */
    public preloadCriticalResources(resources: string[]): void {
        resources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            
            if (resource.endsWith('.js')) {
                link.as = 'script';
            } else if (resource.endsWith('.css')) {
                link.as = 'style';
            } else if (resource.match(/\.(jpg|jpeg|png|webp|gif)$/i)) {
                link.as = 'image';
            }
            
            link.href = resource;
            document.head.appendChild(link);
        });
    }

    /**
     * Implement intersection observer for lazy loading
     */
    public createIntersectionObserver(
        callback: (entries: IntersectionObserverEntry[]) => void,
        options: IntersectionObserverInit = {}
    ): IntersectionObserver {
        const defaultOptions: IntersectionObserverInit = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1,
            ...options
        };

        return new IntersectionObserver(callback, defaultOptions);
    }

    /**
     * Debounce function for performance optimization
     */
    public debounce<T extends (...args: any[]) => any>(
        func: T,
        wait: number
    ): (...args: Parameters<T>) => void {
        let timeout: any;
        
        return (...args: Parameters<T>) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    /**
     * Throttle function for performance optimization
     */
    public throttle<T extends (...args: any[]) => any>(
        func: T,
        limit: number
    ): (...args: Parameters<T>) => void {
        let inThrottle: boolean;
        
        return (...args: Parameters<T>) => {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Measure component render time
     */
    public measureRenderTime<T>(
        componentName: string,
        renderFn: () => T
    ): T {
        const startTime = performance.now();
        const result = renderFn();
        const endTime = performance.now();
        
        const renderTime = endTime - startTime;
        console.log(`${componentName} rendered in ${renderTime.toFixed(2)}ms`);
        
        if (renderTime > 16) { // More than one frame at 60fps
            console.warn(`Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms`);
        }
        
        return result;
    }

    /**
     * Get performance metrics
     */
    public getPerformanceMetrics(): {
        loadingMetrics: LoadingMetrics[];
        averageLoadTime: number;
        totalBundleSize: number;
        cacheHitRate: number;
    } {
        const metrics = Array.from(this.loadingMetrics.values());
        const averageLoadTime = metrics.reduce((sum, m) => sum + m.loadTime, 0) / metrics.length || 0;
        const totalBundleSize = metrics.reduce((sum, m) => sum + m.bundleSize, 0);
        const cacheHits = metrics.filter(m => m.cacheHit).length;
        const cacheHitRate = metrics.length > 0 ? cacheHits / metrics.length : 0;

        return {
            loadingMetrics: metrics,
            averageLoadTime,
            totalBundleSize,
            cacheHitRate
        };
    }

    /**
     * Clear caches
     */
    public clearCaches(): void {
        this.componentCache.clear();
        this.imageCache.clear();
        this.loadingMetrics.clear();
    }

    /**
     * Get optimization recommendations
     */
    public getOptimizationRecommendations(): string[] {
        const recommendations: string[] = [];
        const metrics = this.getPerformanceMetrics();

        if (metrics.averageLoadTime > 1000) {
            recommendations.push('Consider reducing component complexity or implementing code splitting');
        }

        if (metrics.totalBundleSize > this.config.maxBundleSize * 5) {
            recommendations.push('Bundle size is large - consider lazy loading more components');
        }

        if (metrics.cacheHitRate < 0.5) {
            recommendations.push('Low cache hit rate - consider enabling caching for better performance');
        }

        if (recommendations.length === 0) {
            recommendations.push('Performance looks good! No immediate optimizations needed.');
        }

        return recommendations;
    }
}

// Create singleton instance
export const performanceOptimizer = new PerformanceOptimizer();

// Export utility functions
export const createLazyComponent = <T extends ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    componentName: string
) => performanceOptimizer.createLazyComponent(importFn, componentName);

export const optimizeImage = (src: string, options?: any) => 
    performanceOptimizer.optimizeImage(src, options);

export const debounce = <T extends (...args: any[]) => any>(func: T, wait: number) =>
    performanceOptimizer.debounce(func, wait);

export const throttle = <T extends (...args: any[]) => any>(func: T, limit: number) =>
    performanceOptimizer.throttle(func, limit);
