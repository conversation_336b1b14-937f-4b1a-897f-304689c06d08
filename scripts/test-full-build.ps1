# Full Build Test Script
Write-Host "=== PrivacyAI Full Build Test ===" -ForegroundColor Green

# Install npm dependencies
Write-Host "`n1. Installing npm dependencies..." -ForegroundColor Yellow
npm install

# Test Tauri development build
Write-Host "`n2. Testing Tauri development build..." -ForegroundColor Yellow
try {
    # Set timeout for build test (5 minutes)
    $timeout = 300
    $process = Start-Process -FilePath "npm" -ArgumentList "run", "tauri", "dev" -PassThru -NoNewWindow
    
    Write-Host "Build started (PID: $($process.Id)). Waiting for successful compilation..." -ForegroundColor Yellow
    
    # Wait for process to start and check for success indicators
    Start-Sleep -Seconds 30
    
    if (!$process.HasExited) {
        Write-Host "SUCCESS: Tauri dev server started successfully" -ForegroundColor Green
        Write-Host "Stopping dev server..." -ForegroundColor Yellow
        Stop-Process -Id $process.Id -Force
        Write-Host "Build test completed successfully" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Build failed during startup" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "ERROR: Build test failed - $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Full Build Test Complete ===" -ForegroundColor Green
Write-Host "Ready for Phase 2: OCR Integration" -ForegroundColor Cyan