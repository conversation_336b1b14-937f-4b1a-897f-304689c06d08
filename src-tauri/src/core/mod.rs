//! Core modules for file analysis and management
//! 
//! This module contains the foundational components migrated from FileManager AI
//! including duplicate detection, corruption detection, and error handling.

pub mod error;
pub mod file_item;
pub mod duplicate_detector;
pub mod corrupt_file_detector;

// Re-export commonly used types
pub use error::{FileManagerError, Result};
pub use file_item::FileItem;
pub use duplicate_detector::{DuplicateDetector, DuplicateGroup, DuplicateType};
pub use corrupt_file_detector::{CorruptFileDetector, ValidationResult, CorruptionType, CorruptionDetectionResult};
