/// Enhanced cryptocurrency detection tests
/// 
/// Tests for the expanded cryptocurrency coverage including:
/// - Cardano (ADA) address detection and validation
/// - World Mobile Token (WMT) pattern detection
/// - AdaHandle detection ($handle format)
/// - Unstoppable Domains blockchain domain detection
/// - File corruption detection capabilities
/// - User settings configuration and persistence

use tauri_appprivacy_ai_lib::security::{
    CryptocurrencyDetector, CorruptionDetector, UserSettingsManager,
    PerformanceMode, CryptocurrencyType
};
use tauri_appprivacy_ai_lib::security::cryptocurrency::patterns::{CryptoPatternType, CryptoSeverity};

#[cfg(test)]
mod enhanced_cryptocurrency_tests {
    use super::*;

    #[test]
    fn test_cardano_address_detection() {
        let detector = CryptocurrencyDetector::new().expect("Failed to create detector");
        
        // Test Cardano addresses (Bech32 format starting with "addr1")
        let test_addresses = vec![
            "addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3n0d3vllmyqwsx5wktcd8cc3sq835lu7drv2xwl2wywfgse35a3x",
            "addr1q8la4cg7yr07iuqy40kskj54cfyng4magjr3klf06tqmkjj6aa7p9fysnx4kxkn9qrr0l5ls2p8xwfn82mu0peuzn8mq67n5ul",
            "addr1qydhkllsyxwqzpwx4self2luallmghnt87hd4z2wgzta0lrn4sjjskqgfems8rkrqf4vklpgpwfwwkpzfkslmwhqmcqmqrqwgd",
        ];
        
        for test_address in test_addresses {
            let result = detector.detect(test_address);
            
            assert!(result.total_findings > 0, "Should detect Cardano address: {}", test_address);
            assert!(result.high_findings > 0, "Cardano address should be high severity");
            
            let finding = &result.findings[0];
            assert_eq!(finding.finding_type, CryptocurrencyType::Address);
            assert_eq!(finding.pattern_type, CryptoPatternType::CardanoAddress);
            assert_eq!(finding.severity, CryptoSeverity::High);
            assert!(finding.confidence > 0.5, "Should have reasonable confidence for valid Cardano address, got: {}", finding.confidence);
            
            println!("✅ Detected Cardano address: {} (confidence: {:.2})", test_address, finding.confidence);
        }
    }

    #[test]
    fn test_world_mobile_token_detection() {
        let detector = CryptocurrencyDetector::new().expect("Failed to create detector");
        
        // Test World Mobile Token references
        let test_cases = vec![
            "World Mobile Token address: addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3n0d3vllmyqwsx5wktcd8cc3sq835lu7drv2xwl2wywfgse35a3x",
            "WMT token balance: 1000 WMT",
            "WMT address for staking rewards",
            "World Mobile Token staking pool",
        ];
        
        for test_case in test_cases {
            let result = detector.detect(test_case);
            
            // Should detect WMT reference
            let wmt_findings: Vec<_> = result.findings.iter()
                .filter(|f| f.pattern_type == CryptoPatternType::WorldMobileToken)
                .collect();
            
            assert!(!wmt_findings.is_empty(), "Should detect WMT reference: {}", test_case);
            
            let finding = wmt_findings[0];
            assert_eq!(finding.finding_type, CryptocurrencyType::Address);
            assert_eq!(finding.severity, CryptoSeverity::High);
            
            println!("✅ Detected WMT reference: {} (confidence: {:.2})", test_case, finding.confidence);
        }
    }

    #[test]
    fn test_ada_handle_detection() {
        let detector = CryptocurrencyDetector::new().expect("Failed to create detector");
        
        // Test AdaHandle patterns ($handle format)
        let test_handles = vec![
            "$alice",
            "$bob_crypto",
            "$cardano-fan",
            "$stake_pool_1",
            "$my_wallet123",
        ];
        
        for test_handle in test_handles {
            let result = detector.detect(test_handle);
            
            assert!(result.total_findings > 0, "Should detect AdaHandle: {}", test_handle);
            
            let finding = &result.findings[0];
            assert_eq!(finding.finding_type, CryptocurrencyType::Address);
            assert_eq!(finding.pattern_type, CryptoPatternType::AdaHandle);
            assert_eq!(finding.severity, CryptoSeverity::Medium);
            
            println!("✅ Detected AdaHandle: {} (confidence: {:.2})", test_handle, finding.confidence);
        }
    }

    #[test]
    fn test_unstoppable_domains_detection() {
        let detector = CryptocurrencyDetector::new().expect("Failed to create detector");
        
        // Test Unstoppable Domains
        let test_domains = vec![
            "alice.crypto",
            "myportfolio.nft",
            "defi-trader.blockchain",
            "satoshi.bitcoin",
            "hodler.wallet",
            "web3dev.x",
            "lucky.888",
            "community.dao",
            "ethereum.zil",
        ];
        
        for test_domain in test_domains {
            let result = detector.detect(test_domain);
            
            assert!(result.total_findings > 0, "Should detect Unstoppable Domain: {}", test_domain);
            
            let finding = &result.findings[0];
            assert_eq!(finding.finding_type, CryptocurrencyType::Address);
            assert_eq!(finding.pattern_type, CryptoPatternType::UnstoppableDomains);
            assert_eq!(finding.severity, CryptoSeverity::Medium);
            
            println!("✅ Detected Unstoppable Domain: {} (confidence: {:.2})", test_domain, finding.confidence);
        }
    }

    #[test]
    fn test_comprehensive_new_patterns() {
        let detector = CryptocurrencyDetector::new().expect("Failed to create detector");
        
        // Test comprehensive text with all new patterns
        let comprehensive_text = "
            Cardano wallet: addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3n0d3vllmyqwsx5wktcd8cc3sq835lu7drv2xwl2wywfgse35a3x
            AdaHandle: $crypto_trader
            WMT token balance: 5000 WMT
            Unstoppable domain: portfolio.crypto
            NFT collection: art.nft
            DeFi protocol: yield.dao
        ";
        
        let result = detector.detect(comprehensive_text);
        
        // Should detect multiple new pattern types
        assert!(result.total_findings >= 4, "Should detect multiple new cryptocurrency patterns");
        
        // Check for specific pattern types
        let mut found_cardano = false;
        let mut found_ada_handle = false;
        let mut found_wmt = false;
        let mut found_unstoppable = false;
        
        for finding in &result.findings {
            match finding.pattern_type {
                CryptoPatternType::CardanoAddress => found_cardano = true,
                CryptoPatternType::AdaHandle => found_ada_handle = true,
                CryptoPatternType::WorldMobileToken => found_wmt = true,
                CryptoPatternType::UnstoppableDomains => found_unstoppable = true,
                _ => {}
            }
        }
        
        assert!(found_cardano, "Should detect Cardano address");
        assert!(found_ada_handle, "Should detect AdaHandle");
        assert!(found_wmt, "Should detect WMT reference");
        assert!(found_unstoppable, "Should detect Unstoppable Domains");
        
        println!("✅ Comprehensive new patterns test: {} findings detected", result.total_findings);
    }

    #[test]
    fn test_file_corruption_detection() {
        let detector = CorruptionDetector::new();
        
        // Test corrupted/suspicious filenames
        let corrupted_filenames = vec![
            "xK9mP2qR7nL4vB8wE3tY6uI1oA5sD0fG.txt", // High entropy
            "file###$$$%%%^^^&&&***.doc", // Excessive special characters
            "a1b2c3d4e5f6g7h8i9j0.pdf", // Alternating pattern
            "zxcvbnmasdfghjklqwertyuiop.txt", // Random keyboard mashing
            "QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiop.dat", // Random keyboard mashing
        ];
        
        for filename in corrupted_filenames {
            let result = detector.analyze_filename(filename).expect("Analysis should not fail");
            
            assert!(result.is_potentially_corrupted, "Should detect corruption in: {}", filename);
            assert!(result.confidence > 0.6, "Should have reasonable confidence for corrupted filename");
            assert!(!result.corruption_types.is_empty(), "Should identify specific corruption types");
            
            println!("✅ Detected corruption in: {} (confidence: {:.2}, types: {:?})", 
                     filename, result.confidence, result.corruption_types);
        }
    }

    #[test]
    fn test_legitimate_filenames_not_corrupted() {
        let detector = CorruptionDetector::new();
        
        // Test legitimate filenames that should NOT be flagged as corrupted
        let legitimate_filenames = vec![
            "document.pdf",
            "my_photo.jpg",
            "financial_report_2024.xlsx",
            "backup_data.zip",
            "user_settings.json",
            "README.md",
            "config.xml",
        ];
        
        for filename in legitimate_filenames {
            let result = detector.analyze_filename(filename).expect("Analysis should not fail");
            
            assert!(!result.is_potentially_corrupted, "Should NOT detect corruption in: {}", filename);
            assert!(result.confidence < 0.6, "Should have low confidence for legitimate filename");
            
            println!("✅ Legitimate filename passed: {} (confidence: {:.2})", filename, result.confidence);
        }
    }

    #[test]
    fn test_user_settings_creation_and_persistence() {
        let mut settings_manager = UserSettingsManager::new().expect("Failed to create settings manager");

        // Reset to defaults to ensure clean test state
        settings_manager.reset_to_defaults();

        // Test default settings
        let settings = settings_manager.get_settings();
        assert_eq!(settings.performance_mode, PerformanceMode::Balanced);
        assert!(settings.detection_methods.path_based_detection);
        assert!(settings.detection_methods.content_analysis);
        assert!(settings.detection_methods.mnemonic_detection);
        
        // Test cryptocurrency pattern enablement
        assert!(settings_manager.is_crypto_pattern_enabled(&CryptoPatternType::BitcoinWIF));
        assert!(settings_manager.is_crypto_pattern_enabled(&CryptoPatternType::CardanoAddress));
        
        // Test performance mode changes
        settings_manager.set_performance_mode(PerformanceMode::Fast).expect("Failed to set performance mode");
        let updated_settings = settings_manager.get_settings();
        assert_eq!(updated_settings.performance_mode, PerformanceMode::Fast);
        assert!(!updated_settings.detection_methods.content_analysis); // Should be disabled in fast mode
        
        // Test custom settings
        settings_manager.set_custom_setting("test_key".to_string(), "test_value".to_string());
        assert_eq!(settings_manager.get_custom_setting("test_key"), Some(&"test_value".to_string()));
        
        // Test settings persistence
        settings_manager.save().expect("Failed to save settings");
        
        println!("✅ User settings creation and persistence test passed");
    }

    #[test]
    fn test_file_filtering_settings() {
        let mut settings_manager = UserSettingsManager::new().expect("Failed to create settings manager");
        
        // Test file filtering
        assert!(settings_manager.should_scan_file("document.pdf", 10)); // Should scan PDF under size limit
        assert!(!settings_manager.should_scan_file("large_file.pdf", 200)); // Should not scan over size limit
        assert!(!settings_manager.should_scan_file(".hidden_file.txt", 10)); // Should not scan hidden files by default
        assert!(!settings_manager.should_scan_file("node_modules/package.json", 10)); // Should not scan excluded directories
        
        // Test enabling hidden file scanning
        let mut file_filters = settings_manager.get_settings().file_filters.clone();
        file_filters.scan_hidden_files = true;
        settings_manager.update_file_filters(file_filters);
        
        assert!(settings_manager.should_scan_file(".hidden_file.txt", 10)); // Should now scan hidden files
        
        println!("✅ File filtering settings test passed");
    }

    #[test]
    fn test_performance_targets_maintained() {
        let detector = CryptocurrencyDetector::new().expect("Failed to create detector");
        let corruption_detector = CorruptionDetector::new();
        
        // Create large test content with multiple patterns
        let large_content = format!("
            {}
            {}
            {}
            {}
        ", 
            "Cardano: addr1qx2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer3n0d3vllmyqwsx5wktcd8cc3sq835lu7drv2xwl2wywfgse35a3x ".repeat(50),
            "AdaHandle: $crypto_trader ".repeat(50),
            "Domain: portfolio.crypto ".repeat(50),
            "WMT: World Mobile Token balance ".repeat(50)
        );
        
        // Test cryptocurrency detection performance
        let start = std::time::Instant::now();
        let crypto_result = detector.detect(&large_content);
        let _crypto_time = start.elapsed().as_millis();
        
        // Test corruption detection performance
        let start = std::time::Instant::now();
        for i in 0..100 {
            let _ = corruption_detector.analyze_filename(&format!("test_file_{}.txt", i));
        }
        let corruption_time = start.elapsed().as_millis();
        
        // Validate performance targets
        let total_crypto_time = crypto_result.processing_time_ms + crypto_result.validation_time_ms;
        assert!(total_crypto_time <= 100, "Cryptocurrency detection took {}ms, should be ≤100ms", total_crypto_time);
        assert!(corruption_time <= 100, "Corruption detection took {}ms for 100 files, should be ≤100ms", corruption_time);
        
        // Validate detection accuracy
        assert!(crypto_result.total_findings > 0, "Should detect cryptocurrency patterns in large content");
        
        println!("✅ Performance targets maintained:");
        println!("   - Cryptocurrency detection: {}ms", total_crypto_time);
        println!("   - Corruption detection: {}ms (100 files)", corruption_time);
        println!("   - Total findings: {}", crypto_result.total_findings);
    }

    #[test]
    fn test_settings_export_import() {
        let mut settings_manager = UserSettingsManager::new().expect("Failed to create settings manager");
        
        // Modify some settings
        settings_manager.set_performance_mode(PerformanceMode::Comprehensive).expect("Failed to set mode");
        settings_manager.set_crypto_pattern_enabled(CryptoPatternType::BitcoinWIF, false);
        settings_manager.set_custom_setting("export_test".to_string(), "success".to_string());
        
        // Export settings
        let exported = settings_manager.export_settings().expect("Failed to export settings");
        assert!(!exported.is_empty(), "Exported settings should not be empty");
        
        // Create new settings manager and import
        let mut new_settings_manager = UserSettingsManager::new().expect("Failed to create new settings manager");
        new_settings_manager.import_settings(&exported).expect("Failed to import settings");
        
        // Verify imported settings
        let imported_settings = new_settings_manager.get_settings();
        assert_eq!(imported_settings.performance_mode, PerformanceMode::Comprehensive);
        assert!(!new_settings_manager.is_crypto_pattern_enabled(&CryptoPatternType::BitcoinWIF));
        assert_eq!(new_settings_manager.get_custom_setting("export_test"), Some(&"success".to_string()));
        
        println!("✅ Settings export/import test passed");
    }
}
