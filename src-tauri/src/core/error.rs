/// Core error types for FileManager AI
///
/// This module provides comprehensive error handling for all FileManager AI operations
/// with proper serialization support for Tauri frontend integration.
// Removed unused import
use thiserror::Error;
use serde::{Serialize, Deserialize};

/// Result type alias for core operations
pub type Result<T> = std::result::Result<T, FileManagerError>;

/// Main error type for FileManager AI operations
#[derive(<PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub enum FileManagerError {
    /// I/O operation failed
    #[error("I/O operation failed: {message}")]
    Io { message: String },

    /// File not found
    #[error("File not found: {path}")]
    FileNotFound { path: String },

    /// Permission denied
    #[error("Permission denied: {path}")]
    PermissionDenied { path: String },

    /// Invalid file path
    #[error("Invalid file path: {path}")]
    InvalidPath { path: String },

    /// Operation not supported
    #[error("Operation not supported: {operation}")]
    UnsupportedOperation { operation: String },

    /// File already exists
    #[error("File already exists: {path}")]
    FileExists { path: String },

    /// Directory not empty
    #[error("Directory not empty: {path}")]
    DirectoryNotEmpty { path: String },

    /// Serialization error
    #[error("Serialization error: {message}")]
    Serialization { message: String },

    /// Cache operation error
    #[error("Cache error: {message}")]
    Cache { message: String },

    /// GPU acceleration error
    #[error("GPU error: {message}")]
    Gpu { message: String },

    /// WebAssembly error
    #[error("WASM error: {message}")]
    Wasm { message: String },

    /// Performance monitoring error
    #[error("Performance error: {message}")]
    Performance { message: String },

    /// Configuration error
    #[error("Configuration error: {message}")]
    Config { message: String },

    /// Archive operation error
    #[error("Archive error: {message}")]
    Archive { message: String },

    /// Search operation error
    #[error("Search error: {message}")]
    Search { message: String },

    /// Database operation error
    #[error("Database error: {message}")]
    Database { message: String },

    /// Background processing error
    #[error("Background processing error: {message}")]
    Background { message: String },

    /// Service not initialized error
    #[error("Service not initialized: {message}")]
    NotInitialized { message: String },

    /// Encryption/Decryption error
    #[error("Encryption error: {message}")]
    Encryption { message: String },

    /// Resource not found error  
    #[error("Not found: {message}")]
    NotFound { message: String },

        /// Invalid input error
    #[error("Invalid input: {message}")]
    InvalidInput { message: String },

    /// Generic error with message
    #[error("{message}")]
    Generic { message: String },
}

impl FileManagerError {
    /// Create a new generic error with a message
    pub fn new(message: impl Into<String>) -> Self {
        Self::Generic {
            message: message.into(),
        }
    }

    /// Create a file not found error
    pub fn file_not_found(path: impl Into<String>) -> Self {
        Self::FileNotFound { path: path.into() }
    }

    /// Create a permission denied error
    pub fn permission_denied(path: impl Into<String>) -> Self {
        Self::PermissionDenied { path: path.into() }
    }

    /// Create an invalid path error
    pub fn invalid_path(path: impl Into<String>) -> Self {
        Self::InvalidPath { path: path.into() }
    }

    /// Create an unsupported operation error
    pub fn unsupported_operation(operation: impl Into<String>) -> Self {
        Self::UnsupportedOperation {
            operation: operation.into(),
        }
    }

    /// Create a file exists error
    pub fn file_exists(path: impl Into<String>) -> Self {
        Self::FileExists { path: path.into() }
    }

    /// Create a directory not empty error
    pub fn directory_not_empty(path: impl Into<String>) -> Self {
        Self::DirectoryNotEmpty { path: path.into() }
    }

    /// Create a cache error
    pub fn cache_error(message: impl Into<String>) -> Self {
        Self::Cache {
            message: message.into(),
        }
    }

    /// Create a GPU error
    pub fn gpu_error(message: impl Into<String>) -> Self {
        Self::Gpu {
            message: message.into(),
        }
    }

    /// Create a WASM error
    pub fn wasm_error(message: impl Into<String>) -> Self {
        Self::Wasm {
            message: message.into(),
        }
    }

    /// Create a performance error
    pub fn performance_error(message: impl Into<String>) -> Self {
        Self::Performance {
            message: message.into(),
        }
    }

    /// Create a configuration error
    pub fn config_error(message: impl Into<String>) -> Self {
        Self::Config {
            message: message.into(),
        }
    }

    /// Create an archive error
    pub fn archive_error(message: impl Into<String>) -> Self {
        Self::Archive {
            message: message.into(),
        }
    }

    /// Create a search error
    pub fn search_error(message: impl Into<String>) -> Self {
        Self::Search {
            message: message.into(),
        }
    }

    /// Create a database error
    pub fn database_error(message: impl Into<String>) -> Self {
        Self::Database {
            message: message.into(),
        }
    }

    /// Create a background processing error
    pub fn background_error(message: impl Into<String>) -> Self {
        Self::Background {
            message: message.into(),
        }
    }

    /// Create a not initialized error
    pub fn not_initialized(message: impl Into<String>) -> Self {
        Self::NotInitialized {
            message: message.into(),
        }
    }

    /// Create an I/O error (alias for compatibility)
    pub fn io_error(message: impl Into<String>) -> Self {
        Self::Io {
            message: message.into(),
        }
    }

    /// Create a serialization error
    pub fn serialization_error(message: impl Into<String>) -> Self {
        Self::Serialization {
            message: message.into(),
        }
    }

    /// Create an invalid input error
    pub fn invalid_input(message: impl Into<String>) -> Self {
        Self::InvalidInput {
            message: message.into(),
        }
    }

    /// Create an encryption error
    pub fn encryption_error(message: impl Into<String>) -> Self {
        Self::Encryption {
            message: message.into(),
        }
    }

    /// Create a not found error
    pub fn not_found(message: impl Into<String>) -> Self {
        Self::NotFound {
            message: message.into(),
        }
    }

        /// Create an operation failed error
    pub fn operation_failed(message: impl Into<String>) -> Self {
        Self::Generic {
            message: message.into(),
        }
    }
}

/// Convert std::io::Error to FileManagerError
impl From<std::io::Error> for FileManagerError {
    fn from(error: std::io::Error) -> Self {
        Self::Io {
            message: error.to_string(),
        }
    }
}

/// Convert serde_json::Error to FileManagerError
impl From<serde_json::Error> for FileManagerError {
    fn from(error: serde_json::Error) -> Self {
        Self::Serialization {
            message: error.to_string(),
        }
    }
}

/// Convert csv::Error to FileManagerError
impl From<csv::Error> for FileManagerError {
    fn from(error: csv::Error) -> Self {
        Self::Serialization {
            message: format!("CSV error: {error}"),
        }
    }
}


// /// Convert rusqlite::Error to FileManagerError (temporarily disabled)
// impl From<rusqlite::Error> for FileManagerError {
//     fn from(error: rusqlite::Error) -> Self {
//         Self::Database {
//             message: error.to_string(),
//         }
//     }
// }

/// Convert FileManagerError to a string for Tauri commands
impl From<FileManagerError> for String {
    fn from(error: FileManagerError) -> Self {
        error.to_string()
    }
}
