# Test Document - Credit Card Information

This document contains test credit card numbers for privacy detection validation.

**IMPORTANT**: These are FAKE credit card numbers for testing purposes only.

## Test Credit Card Patterns

### Visa Cards (Fake)
- 4111-1111-1111-1111 (Test Visa)
- 4222-2222-2222-2222 (Test Visa)
- 4333-3333-3333-3333 (Test Visa)
- 4444-4444-4444-4448 (Test Visa with valid Luhn)

### Mastercard (Fake)
- 5555-5555-5555-4444 (Test Mastercard)
- 5105-1051-0510-5100 (Test Mastercard)
- 5200-0000-0000-0000 (Test Mastercard)

### American Express (Fake)
- 3782-822463-10005 (Test Amex)
- 3714-496353-98431 (Test Amex)
- 3400-0000-0000-009 (Test Amex)

### Discover (Fake)
- 6011-1111-1111-1117 (Test Discover)
- 6011-0000-0000-0004 (Test Discover)

## Various Formats

### Without Dashes
- ****************
- ****************
- ***************

### With Spaces
- 4111 1111 1111 1111
- 5555 5555 5555 4444
- 3782 822463 10005

### Mixed Formats
- 4111-1111 1111-1111
- 5555.5555.5555.4444
- 3782 822463-10005

## Credit Card with Additional Information

### Customer Records
- Name: John Doe
- Card: 4111-1111-1111-1111
- Exp: 12/25
- CVV: 123

- Name: Jane Smith
- Credit Card Number: 5555-5555-5555-4444
- Expiration Date: 06/26
- Security Code: 456

### Transaction Records
Transaction ID: TXN001
Card Number: 4222-2222-2222-2222
Amount: $150.00
Date: 2024-01-15
Merchant: Test Store

Transaction ID: TXN002
CC#: 5105-1051-0510-5100
Amount: $75.50
Date: 2024-01-16
Merchant: Online Shop

## Payment Information Forms

### Billing Information
Cardholder Name: Alice Johnson
Card Number: 3782-822463-10005
Expiration: 09/27
CVV: 789
Billing Address: 456 Oak Ave, Test City, TC 54321

### Subscription Details
Monthly charge to card ending in 1111
Full card number: 4111-1111-1111-1111
Auto-renewal date: 15th of each month

## Edge Cases and Variations

### Partial Numbers
- Card ending in 1111
- Last 4 digits: 4444
- First 6 digits: 411111

### In Sentences
Please charge $100.00 to credit card 4333-3333-3333-3333.
The payment method on file is 5555-5555-5555-4444.
Update billing for card number 3714-496353-98431.

### Multiple Cards
Primary card: 4111-1111-1111-1111
Backup card: 5555-5555-5555-4444
Business card: 3782-822463-10005

## Expected Detection Results

This document should trigger:
- HIGH RISK classification
- Multiple credit card pattern matches
- Privacy data type: CREDIT_CARD_NUMBER
- Confidence score: >90%
- AI enhancement should identify associated PII

## Test Validation

The privacy detection system should:
1. Identify all credit card patterns (20+ instances)
2. Classify document as HIGH RISK
3. Recognize different card types (Visa, MC, Amex, Discover)
4. Handle various formatting styles
5. Detect associated expiration dates and CVV codes
6. Generate appropriate security recommendations
