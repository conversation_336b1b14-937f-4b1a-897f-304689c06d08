/* Resource Manager Component Styling */

.resource-manager {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin: 24px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header */
.resource-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f1f5f9;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    color: #8b5cf6;
    width: 24px;
    height: 24px;
}

.resource-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.resource-header p {
    margin: 2px 0 0 0;
    color: #64748b;
    font-size: 0.875rem;
}

/* Controls */
.resource-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.resource-controls button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #475569;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.resource-controls button:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #334155;
}

.resource-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.monitor-toggle.active {
    background: #dbeafe;
    border-color: #3b82f6;
    color: #1d4ed8;
}

.gc-button:hover:not(:disabled) {
    background: #fee2e2;
    border-color: #ef4444;
    color: #dc2626;
}

.optimize-button:hover:not(:disabled) {
    background: #f3e8ff;
    border-color: #8b5cf6;
    color: #7c3aed;
}

.autoscaling-toggle {
    background: #f8fafc;
    border-color: #e2e8f0;
    color: #64748b;
}

.autoscaling-toggle.active {
    background: #dcfce7;
    border-color: #16a34a;
    color: #15803d;
}

.autoscaling-toggle:hover:not(:disabled) {
    background: #f0fdf4;
    border-color: #22c55e;
    color: #16a34a;
}

/* Alerts Section */
.alerts-section {
    margin-bottom: 24px;
}

.alerts-section h4 {
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.alert {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-critical {
    background: #fef2f2;
    border-left-color: #ef4444;
}

.alert-high {
    background: #fef3c7;
    border-left-color: #f59e0b;
}

.alert-medium {
    background: #fefce8;
    border-left-color: #eab308;
}

.alert-low {
    background: #f0fdf4;
    border-left-color: #10b981;
}

.alert-icon {
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.alert-icon.critical {
    color: #ef4444;
}

.alert-icon.high {
    color: #f59e0b;
}

.alert-icon.medium {
    color: #eab308;
}

.alert-icon.low {
    color: #10b981;
}

.alert-content {
    flex: 1;
}

.alert-message {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.alert-recommendation {
    font-size: 0.875rem;
    color: #64748b;
}

/* Resource Metrics */
.resource-metrics {
    margin-bottom: 20px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.metric-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
}

.metric-card:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.metric-detail {
    font-size: 0.75rem;
    color: #64748b;
    margin-bottom: 8px;
}

.progress-bar {
    height: 4px;
    border-radius: 2px;
    background: #e5e7eb;
    transition: background 0.3s ease;
}

/* Scaling Configuration */
.scaling-config {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.scaling-config h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.config-section {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 16px;
}

.config-section h5 {
    margin: 0 0 12px 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: #374151;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 12px;
}

.config-item label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.config-item input[type="number"] {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.875rem;
    background: white;
    transition: border-color 0.2s ease;
}

.config-item input[type="number"]:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Scaling Events */
.scaling-events {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.scaling-events h4 {
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    border-left: 4px solid #64748b;
}

.event-item.event-scale_up {
    border-left-color: #16a34a;
    background: #f0fdf4;
}

.event-item.event-scale_down {
    border-left-color: #dc2626;
    background: #fef2f2;
}

.event-item.event-threshold_adjust {
    border-left-color: #f59e0b;
    background: #fefce8;
}

.event-time {
    font-size: 0.75rem;
    color: #64748b;
    min-width: 80px;
}

.event-description {
    flex: 1;
    font-size: 0.875rem;
    color: #1e293b;
}

.event-reason {
    font-size: 0.75rem;
    color: #64748b;
    font-style: italic;
}

/* Auto-cleanup */
.auto-cleanup {
    padding-top: 16px;
    border-top: 1px solid #e2e8f0;
}

.auto-cleanup label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: #64748b;
    cursor: pointer;
}

.auto-cleanup input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #8b5cf6;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .resource-manager {
        padding: 16px;
        margin: 16px 0;
    }
    
    .resource-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .resource-controls {
        flex-wrap: wrap;
        width: 100%;
    }
    
    .resource-controls button {
        flex: 1;
        min-width: 100px;
        justify-content: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .alerts-list {
        gap: 12px;
    }
    
    .alert {
        padding: 16px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .resource-manager {
        background: #1e293b;
        color: #f1f5f9;
    }
    
    .resource-header {
        border-bottom-color: #334155;
    }
    
    .resource-header h3 {
        color: #f1f5f9;
    }
    
    .metric-card {
        background: #334155;
        border-color: #475569;
        color: #f1f5f9;
    }
    
    .metric-card:hover {
        background: #475569;
        border-color: #64748b;
    }
    
    .metric-value {
        color: #f1f5f9;
    }
    
    .alert-critical {
        background: #450a0a;
    }
    
    .alert-high {
        background: #451a03;
    }
    
    .alert-medium {
        background: #422006;
    }
    
    .alert-low {
        background: #052e16;
    }
    
    .auto-cleanup {
        border-top-color: #475569;
    }
}
