use tokio;

use tauri_appprivacy_ai_lib::privacy::{
    NanoModel, NanoModelType, NanoModelConfig, NanoModelManager,
    Privacy<PERSON><PERSON><PERSON><PERSON><PERSON>, FaceDetector<PERSON>ano, TextDetectorNano, NanoModelSuite
};

/// Test module for nano model performance validation
/// 
/// Validates Phase 3 targets:
/// - 80ms total processing time
/// - 2.3MB total model size
/// - 75%+ accuracy for each model
/// - Memory efficiency targets

/// Test data for nano model validation
const TEST_IMAGE_DATA: &[u8] = include_bytes!("fixtures/test_image.jpg");
const TEST_DOCUMENT_DATA: &[u8] = include_bytes!("fixtures/test_document.pdf");
const EMPTY_IMAGE_DATA: &[u8] = &[];

#[cfg(test)]
mod nano_model_tests {
    use super::*;

    #[tokio::test]
    async fn test_privacy_classifier_nano_creation() {
        let config = NanoModelConfig::default();
        let classifier = PrivacyClassifierNano::new(config);

        // Test model size target (0.8MB)
        assert_eq!(classifier.model_size_mb(), 0.8);
        assert!(classifier.model_size_mb() <= 0.8, "Privacy classifier exceeds 0.8MB size limit");

        // Test expected processing time (25ms)
        assert_eq!(classifier.expected_processing_time_ms(), 25);

        // Test accuracy target (78%)
        assert_eq!(classifier.accuracy_percentage(), 78.0);
        assert!(classifier.accuracy_percentage() >= 75.0, "Privacy classifier accuracy below 75%");

        // Test model type
        assert_eq!(classifier.model_type(), NanoModelType::PrivacyClassifier);

        println!("✅ Privacy Classifier: 0.8MB, 25ms target, 78% accuracy");
    }

    #[tokio::test]
    async fn test_face_detector_nano_creation() {
        let config = NanoModelConfig::default();
        let detector = FaceDetectorNano::new(config);

        // Test model size target (0.3MB)
        assert_eq!(detector.model_size_mb(), 0.3);
        assert!(detector.model_size_mb() <= 0.3, "Face detector exceeds 0.3MB size limit");

        // Test expected processing time (15ms)
        assert_eq!(detector.expected_processing_time_ms(), 15);

        // Test accuracy target (85%)
        assert_eq!(detector.accuracy_percentage(), 85.0);
        assert!(detector.accuracy_percentage() >= 75.0, "Face detector accuracy below 75%");

        // Test model type
        assert_eq!(detector.model_type(), NanoModelType::FaceDetector);

        println!("✅ Face Detector: 0.3MB, 15ms target, 85% accuracy");
    }

    #[tokio::test]
    async fn test_text_detector_nano_creation() {
        let config = NanoModelConfig::default();
        let detector = TextDetectorNano::new(config);

        // Test model size target (1.2MB)
        assert_eq!(detector.model_size_mb(), 1.2);
        assert!(detector.model_size_mb() <= 1.2, "Text detector exceeds 1.2MB size limit");

        // Test expected processing time (40ms)
        assert_eq!(detector.expected_processing_time_ms(), 40);

        // Test accuracy target (80%)
        assert_eq!(detector.accuracy_percentage(), 80.0);
        assert!(detector.accuracy_percentage() >= 75.0, "Text detector accuracy below 75%");

        // Test model type
        assert_eq!(detector.model_type(), NanoModelType::TextDetector);

        println!("✅ Text Detector: 1.2MB, 40ms target, 80% accuracy");
    }

    #[tokio::test]
    async fn test_nano_model_suite_creation() {
        let suite = NanoModelSuite::new();

        // Test total size target (2.3MB)
        let total_size = suite.total_size_mb();
        assert!((total_size - 2.3).abs() < 0.01, "Expected ~2.3MB, got {:.6}MB", total_size);
        assert!(total_size <= 2.31, "Nano model suite exceeds 2.3MB total size limit");

        // Test expected total time (80ms)
        let expected_time = suite.expected_total_time_ms();
        assert_eq!(expected_time, 80);

        println!("✅ Nano Model Suite: 2.3MB total, 80ms target, 3 models");
    }

    #[tokio::test]
    async fn test_nano_model_manager_creation() {
        let manager = NanoModelManager::new();

        // Test that manager can be created successfully
        let stats = manager.get_cache_stats();
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.hits, 0);
        assert_eq!(stats.misses, 0);

        println!("✅ NanoModelManager created successfully with empty cache");
    }

    #[tokio::test]
    async fn test_nano_model_config_validation() {
        let config = NanoModelConfig::default();

        // Test that config has reasonable defaults
        assert!(config.max_memory_mb > 0.0);
        assert!(config.timeout_ms > 0);
        assert!(config.cache_size > 0);

        println!("✅ NanoModelConfig validation passed");
    }
}
