//! Duplicate file detection system for FileManager AI
//!
//! This module provides intelligent duplicate file detection using
//! multiple algorithms including size, hash, and content comparison.
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use std::fs;
use std::io::Read;

use serde::{Deserialize, Serialize};
use blake3::Hasher;
use walkdir::WalkDir;
use rayon::prelude::*;
use img_hash::{HasherConfig, HashAlg};
use base64::{Engine as _, engine::general_purpose};

/// Duplicate detection options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DuplicateDetectionOptions {
    /// Include hidden files in detection
    pub include_hidden: bool,
    /// Minimum file size to consider (bytes)
    pub min_file_size: u64,
    /// Maximum file size to consider (bytes, 0 = no limit)
    pub max_file_size: u64,
    /// Use parallel processing
    pub use_parallel: bool,
    /// Compare file content for verification
    pub verify_content: bool,
    /// File extensions to include (empty = all)
    pub include_extensions: Vec<String>,
    /// File extensions to exclude
    pub exclude_extensions: Vec<String>,
    /// Enable perceptual hashing for images
    pub use_perceptual_hash: bool,
    /// Perceptual hash similarity threshold (0.0-1.0)
    pub similarity_threshold: f64,
    /// Enable EXIF metadata comparison
    pub compare_exif: bool,
}

impl Default for DuplicateDetectionOptions {
    fn default() -> Self {
        Self {
            include_hidden: false,
            min_file_size: 1, // At least 1 byte
            max_file_size: 0, // No limit
            use_parallel: true,
            verify_content: true,
            include_extensions: Vec::new(),
            exclude_extensions: vec!["tmp".to_string(), "temp".to_string(), "log".to_string()],
            use_perceptual_hash: true,
            similarity_threshold: 0.95, // 95% similarity for near-duplicates
            compare_exif: true,
        }
    }
}

/// Information about a duplicate file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DuplicateFile {
    /// File path
    pub path: PathBuf,
    /// File size in bytes
    pub size: u64,
    /// File hash (BLAKE3)
    pub hash: String,
    /// Last modified timestamp
    pub modified: u64,
    /// File extension
    pub extension: String,
    /// Perceptual hash for images (if applicable)
    pub perceptual_hash: Option<String>,
    /// Similarity score (0.0-1.0) for near-duplicates
    pub similarity_score: Option<f64>,
    /// EXIF metadata summary (for images)
    pub exif_summary: Option<String>,
}

/// Group of duplicate files
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DuplicateGroup {
    /// Common file size
    pub size: u64,
    /// Common file hash
    pub hash: String,
    /// List of duplicate files
    pub files: Vec<DuplicateFile>,
    /// Total wasted space (size * (count - 1))
    pub wasted_space: u64,
    /// Type of duplicate detection used
    pub detection_type: DuplicateType,
    /// Average similarity score for the group
    pub avg_similarity: Option<f64>,
}

/// Type of duplicate detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DuplicateType {
    /// Exact duplicates (same hash)
    Exact,
    /// Near duplicates (perceptual similarity)
    Similar,
    /// EXIF metadata matches
    ExifMatch,
}

/// Results of duplicate detection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DuplicateDetectionResult {
    /// Groups of duplicate files
    pub duplicate_groups: Vec<DuplicateGroup>,
    /// Total number of duplicate files found
    pub total_duplicates: usize,
    /// Total wasted space in bytes
    pub total_wasted_space: u64,
    /// Number of files scanned
    pub files_scanned: usize,
    /// Time taken for detection (milliseconds)
    pub detection_time_ms: u64,
}

/// Duplicate file detector
pub struct DuplicateDetector {
    options: DuplicateDetectionOptions,
}

impl DuplicateDetector {
    /// Create a new duplicate detector with options
    pub fn new(options: DuplicateDetectionOptions) -> Self {
        Self { options }
    }
    
    /// Create a new duplicate detector with default options
    ///
    /// This method provides a convenient way to create a detector with
    /// sensible defaults and may be used by frontend components.
    #[allow(dead_code)]
    pub fn with_defaults() -> Self {
        Self::new(DuplicateDetectionOptions::default())
    }
    
    /// Detect duplicates in a directory
    pub fn detect_duplicates(&self, directory: &Path) -> Result<DuplicateDetectionResult, Box<dyn std::error::Error>> {
        let start_time = std::time::Instant::now();
        
        // Step 1: Collect all files with their metadata
        let files = self.collect_files(directory)?;
        
        // Step 2: Group files by size (quick pre-filter)
        let size_groups = self.group_by_size(files);
        
        // Step 3: Calculate hashes for files with same size
        let hash_groups = if self.options.use_parallel {
            self.calculate_hashes_parallel(size_groups)?
        } else {
            self.calculate_hashes_sequential(size_groups)?
        };
        
        // Step 4: Verify content if requested
        let verified_groups = if self.options.verify_content {
            self.verify_content_matches(hash_groups)?
        } else {
            hash_groups
        };

        // Step 5: Find perceptual duplicates for images
        let mut all_groups = self.create_duplicate_groups(verified_groups);
        if self.options.use_perceptual_hash {
            let perceptual_groups = self.find_perceptual_duplicates(directory)?;
            all_groups.extend(perceptual_groups);
        }

        // Step 6: Calculate statistics
        let duplicate_groups = all_groups;
        let total_duplicates = duplicate_groups.iter()
            .map(|group| group.files.len().saturating_sub(1))
            .sum();
        let total_wasted_space = duplicate_groups.iter()
            .map(|group| group.wasted_space)
            .sum();
        
        let detection_time_ms = start_time.elapsed().as_millis() as u64;
        
        Ok(DuplicateDetectionResult {
            duplicate_groups,
            total_duplicates,
            total_wasted_space,
            files_scanned: 0, // Will be set by collect_files
            detection_time_ms,
        })
    }
    
    /// Collect all files in directory with metadata
    fn collect_files(&self, directory: &Path) -> Result<Vec<DuplicateFile>, Box<dyn std::error::Error>> {
        let mut files = Vec::new();
        
        for entry in WalkDir::new(directory).follow_links(false) {
            let entry = entry?;
            let path = entry.path();
            
            // Skip directories
            if !path.is_file() {
                continue;
            }
            
            // Skip hidden files if not included
            if !self.options.include_hidden && self.is_hidden_file(path) {
                continue;
            }
            
            let metadata = fs::metadata(path)?;
            let size = metadata.len();
            
            // Apply size filters
            if size < self.options.min_file_size {
                continue;
            }
            if self.options.max_file_size > 0 && size > self.options.max_file_size {
                continue;
            }
            
            // Apply extension filters
            let extension = path.extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
                .to_lowercase();
            
            if !self.options.include_extensions.is_empty() 
                && !self.options.include_extensions.contains(&extension) {
                continue;
            }
            
            if self.options.exclude_extensions.contains(&extension) {
                continue;
            }
            
            let modified = metadata.modified()?
                .duration_since(std::time::UNIX_EPOCH)?
                .as_secs();
            
            // Calculate perceptual hash and EXIF data for images
            let (perceptual_hash, exif_summary) = if self.is_image_file(&extension) {
                let perceptual_hash = if self.options.use_perceptual_hash {
                    self.calculate_perceptual_hash(path).ok()
                } else {
                    None
                };

                let exif_summary = if self.options.compare_exif {
                    self.extract_exif_summary(path).ok()
                } else {
                    None
                };

                (perceptual_hash, exif_summary)
            } else {
                (None, None)
            };

            files.push(DuplicateFile {
                path: path.to_path_buf(),
                size,
                hash: String::new(), // Will be calculated later
                modified,
                extension,
                perceptual_hash,
                similarity_score: None,
                exif_summary,
            });
        }
        
        Ok(files)
    }
    
    /// Group files by size
    fn group_by_size(&self, files: Vec<DuplicateFile>) -> HashMap<u64, Vec<DuplicateFile>> {
        let mut size_groups: HashMap<u64, Vec<DuplicateFile>> = HashMap::new();
        
        for file in files {
            size_groups.entry(file.size).or_default().push(file);
        }
        
        // Only keep groups with multiple files
        size_groups.retain(|_, files| files.len() > 1);
        size_groups
    }
    
    /// Calculate hashes for files (parallel version)
    fn calculate_hashes_parallel(
        &self,
        size_groups: HashMap<u64, Vec<DuplicateFile>>
    ) -> Result<HashMap<String, Vec<DuplicateFile>>, Box<dyn std::error::Error>> {
        let mut hash_groups: HashMap<String, Vec<DuplicateFile>> = HashMap::new();
        
        for (_, mut files) in size_groups {
            // Calculate hashes in parallel
            files.par_iter_mut().for_each(|file| {
                if let Ok(hash) = self.calculate_file_hash(&file.path) {
                    file.hash = hash;
                }
            });
            
            // Group by hash
            for file in files {
                if !file.hash.is_empty() {
                    hash_groups.entry(file.hash.clone()).or_default().push(file);
                }
            }
        }
        
        // Only keep groups with multiple files
        hash_groups.retain(|_, files| files.len() > 1);
        Ok(hash_groups)
    }
    
    /// Calculate hashes for files (sequential version)
    fn calculate_hashes_sequential(
        &self,
        size_groups: HashMap<u64, Vec<DuplicateFile>>
    ) -> Result<HashMap<String, Vec<DuplicateFile>>, Box<dyn std::error::Error>> {
        let mut hash_groups: HashMap<String, Vec<DuplicateFile>> = HashMap::new();
        
        for (_, mut files) in size_groups {
            for file in &mut files {
                file.hash = self.calculate_file_hash(&file.path)?;
            }
            
            // Group by hash
            for file in files {
                hash_groups.entry(file.hash.clone()).or_default().push(file);
            }
        }
        
        // Only keep groups with multiple files
        hash_groups.retain(|_, files| files.len() > 1);
        Ok(hash_groups)
    }
    
    /// Calculate BLAKE3 hash for a file
    fn calculate_file_hash(&self, _path: &Path) -> Result<String, Box<dyn std::error::Error>> {
        let mut file = fs::File::open(_path)?;
        let mut hasher = Hasher::new();
        let mut buffer = [0; 8192]; // 8KB buffer
        
        loop {
            let bytes_read = file.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        Ok(hasher.finalize().to_hex().to_string())
    }
    
    /// Verify content matches (additional verification step)
    fn verify_content_matches(
        &self,
        hash_groups: HashMap<String, Vec<DuplicateFile>>
    ) -> Result<HashMap<String, Vec<DuplicateFile>>, Box<dyn std::error::Error>> {
        // For now, trust the hash - BLAKE3 is cryptographically secure
        // In the future, could add byte-by-byte comparison for extra verification
        Ok(hash_groups)
    }
    
    /// Create duplicate groups from hash groups
    fn create_duplicate_groups(&self, hash_groups: HashMap<String, Vec<DuplicateFile>>) -> Vec<DuplicateGroup> {
        hash_groups.into_iter()
            .map(|(hash, files)| {
                let size = files.first().map(|f| f.size).unwrap_or(0);
                let wasted_space = size * (files.len().saturating_sub(1) as u64);

                DuplicateGroup {
                    size,
                    hash,
                    files,
                    wasted_space,
                    detection_type: DuplicateType::Exact,
                    avg_similarity: Some(1.0), // Exact matches have 100% similarity
                }
            })
            .collect()
    }

    /// Find perceptual duplicates among images
    fn find_perceptual_duplicates(&self, directory: &Path) -> Result<Vec<DuplicateGroup>, Box<dyn std::error::Error>> {
        let mut groups = Vec::new();

        // Collect all image files with perceptual hashes
        let mut image_files = Vec::new();
        for entry in WalkDir::new(directory).follow_links(false) {
            let entry = entry?;
            let path = entry.path();

            if !path.is_file() {
                continue;
            }

            let extension = path.extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
                .to_lowercase();

            if self.is_image_file(&extension) {
                if let Ok(perceptual_hash) = self.calculate_perceptual_hash(path) {
                    let metadata = fs::metadata(path)?;
                    let size = metadata.len();
                    let modified = metadata.modified()?
                        .duration_since(std::time::UNIX_EPOCH)?
                        .as_secs();

                    let exif_summary = if self.options.compare_exif {
                        self.extract_exif_summary(path).ok()
                    } else {
                        None
                    };

                    image_files.push(DuplicateFile {
                        path: path.to_path_buf(),
                        size,
                        hash: String::new(), // Not used for perceptual matching
                        modified,
                        extension,
                        perceptual_hash: Some(perceptual_hash),
                        similarity_score: None,
                        exif_summary,
                    });
                }
            }
        }

        // Compare all pairs of images for similarity
        let mut processed = vec![false; image_files.len()];

        for i in 0..image_files.len() {
            if processed[i] {
                continue;
            }

            let mut group = vec![image_files[i].clone()];
            processed[i] = true;

            if let Some(ref hash1) = image_files[i].perceptual_hash {
                for j in (i + 1)..image_files.len() {
                    if processed[j] {
                        continue;
                    }

                    if let Some(ref hash2) = image_files[j].perceptual_hash {
                        if let Ok(similarity) = self.calculate_hash_similarity(hash1, hash2) {
                            if similarity >= self.options.similarity_threshold {
                                let mut similar_file = image_files[j].clone();
                                similar_file.similarity_score = Some(similarity);
                                group.push(similar_file);
                                processed[j] = true;
                            }
                        }
                    }
                }
            }

            // Only create group if we found similar images
            if group.len() > 1 {
                let avg_similarity = group.iter()
                    .filter_map(|f| f.similarity_score)
                    .sum::<f64>() / (group.len() - 1) as f64; // Exclude the original file

                let size = group.first().map(|f| f.size).unwrap_or(0);
                let wasted_space = size * (group.len().saturating_sub(1) as u64);

                groups.push(DuplicateGroup {
                    size,
                    hash: format!("perceptual_{}", groups.len()), // Unique identifier
                    files: group,
                    wasted_space,
                    detection_type: DuplicateType::Similar,
                    avg_similarity: Some(avg_similarity),
                });
            }
        }

        Ok(groups)
    }
    
    /// Check if a file is hidden
    fn is_hidden_file(&self, _path: &Path) -> bool {
        _path.file_name()
            .and_then(|name| name.to_str())
            .map(|name| name.starts_with('.'))
            .unwrap_or(false)
    }

    /// Check if file extension indicates an image
    fn is_image_file(&self, extension: &str) -> bool {
        matches!(extension.to_lowercase().as_str(),
            "jpg" | "jpeg" | "png" | "gif" | "bmp" | "tiff" | "tif" | "webp" | "ico" | "svg")
    }

    /// Calculate perceptual hash for an image
    fn calculate_perceptual_hash(&self, _path: &Path) -> Result<String, Box<dyn std::error::Error>> {
        // Use img_hash's image loading to ensure compatibility
        let img = img_hash::image::open(_path)?;
        let hasher = HasherConfig::new()
            .hash_alg(HashAlg::Gradient)
            .hash_size(8, 8)
            .to_hasher();

        let hash = hasher.hash_image(&img);
        Ok(hash.to_base64())
    }

    /// Extract EXIF metadata summary (simplified version)
    fn extract_exif_summary(&self, _path: &Path) -> Result<String, Box<dyn std::error::Error>> {
        // For now, return basic file information as EXIF summary
        // TODO: Implement proper EXIF extraction when kamadak-exif dependency is resolved
        let metadata = std::fs::metadata(_path)?;
        let file_name = _path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown");

        Ok(format!("File:{},Size:{}", file_name, metadata.len()))
    }

    /// Calculate similarity between two perceptual hashes
    fn calculate_hash_similarity(&self, hash1: &str, hash2: &str) -> Result<f64, Box<dyn std::error::Error>> {
        // Decode base64 hashes
        let bytes1 = general_purpose::STANDARD.decode(hash1)?;
        let bytes2 = general_purpose::STANDARD.decode(hash2)?;

        if bytes1.len() != bytes2.len() {
            return Ok(0.0);
        }

        // Calculate Hamming distance
        let mut differences = 0;
        let total_bits = bytes1.len() * 8;

        for (b1, b2) in bytes1.iter().zip(bytes2.iter()) {
            differences += (b1 ^ b2).count_ones() as usize;
        }

        // Convert to similarity score (1.0 = identical, 0.0 = completely different)
        let similarity = 1.0 - (differences as f64 / total_bits as f64);
        Ok(similarity)
    }
}
