import React, { useState, useEffect } from 'react';
import * as Progress from '@radix-ui/react-progress';
import * as Dialog from '@radix-ui/react-dialog';
import { 
    Settings, 
    Upload, 
    Search, 
    Shield, 
    CheckCircle, 
    ArrowRight, 
    ArrowLeft,
    X,
    Play,
    FileText,
    BarChart3
} from 'lucide-react';
import EnhancedConfigurationPanel from './EnhancedConfigurationPanel';
import EnhancedFileDropZone from './EnhancedFileDropZone';
import ResultsViewer from './ResultsViewer';
import EnhancedAnalyticsDashboard from './EnhancedAnalyticsDashboard';
import './GuidedWorkflow.css';

interface WorkflowStep {
    id: string;
    title: string;
    description: string;
    icon: React.ReactNode;
    component: React.ReactNode;
    isCompleted: boolean;
    isOptional: boolean;
}

interface GuidedWorkflowProps {
    onWorkflowComplete?: (results: any) => void;
    initialStep?: string;
}

const GuidedWorkflow: React.FC<GuidedWorkflowProps> = ({
    onWorkflowComplete,
    initialStep = 'configure'
}) => {
    const [currentStepIndex, setCurrentStepIndex] = useState(0);
    const [workflowData, setWorkflowData] = useState<any>({
        configuration: null,
        files: [],
        scanResults: [],
        analytics: null
    });
    const [showWelcomeDialog, setShowWelcomeDialog] = useState(
        // Don't show welcome dialog in test environment
        typeof window !== 'undefined' && window.location.hostname === 'localhost' ? false : true
    );

    const steps: WorkflowStep[] = [
        {
            id: 'configure',
            title: 'Configure Settings',
            description: 'Set up your privacy detection preferences and security settings',
            icon: <Settings size={24} />,
            component: (
                <EnhancedConfigurationPanel 
                    onConfigChange={(config) => {
                        setWorkflowData((prev: any) => ({ ...prev, configuration: config }));
                        markStepCompleted('configure');
                    }}
                />
            ),
            isCompleted: false,
            isOptional: false
        },
        {
            id: 'upload',
            title: 'Upload Files',
            description: 'Select or drag & drop files for privacy scanning',
            icon: <Upload size={24} />,
            component: (
                <EnhancedFileDropZone 
                    onFilesProcessed={(results) => {
                        setWorkflowData((prev: any) => ({
                            ...prev, 
                            scanResults: [...prev.scanResults, ...results] 
                        }));
                        markStepCompleted('upload');
                        // Auto-advance to results when processing is complete
                        if (results.length > 0) {
                            setTimeout(() => setCurrentStepIndex(2), 1000);
                        }
                    }}
                    onBatchComplete={(stats) => {
                        console.log('Batch processing completed:', stats);
                    }}
                />
            ),
            isCompleted: false,
            isOptional: false
        },
        {
            id: 'results',
            title: 'Review Results',
            description: 'Examine privacy findings and risk assessments',
            icon: <Search size={24} />,
            component: (
                <ResultsViewer 
                    results={workflowData.scanResults}
                    onResultSelect={(result) => {
                        console.log('Selected result for detailed view:', result);
                    }}
                    onExport={(filteredResults) => {
                        console.log('Exporting filtered results:', filteredResults);
                        markStepCompleted('results');
                    }}
                    loading={false}
                />
            ),
            isCompleted: false,
            isOptional: false
        },
        {
            id: 'analytics',
            title: 'View Analytics',
            description: 'Analyze trends and patterns in your privacy data',
            icon: <BarChart3 size={24} />,
            component: <EnhancedAnalyticsDashboard />,
            isCompleted: false,
            isOptional: true
        },
        {
            id: 'secure',
            title: 'Secure Operations',
            description: 'Securely delete sensitive files if needed',
            icon: <Shield size={24} />,
            component: (
                <div className="secure-operations-placeholder">
                    <Shield size={48} className="placeholder-icon" />
                    <h3>Secure Operations</h3>
                    <p>
                        Based on your scan results, you can now securely delete files containing 
                        sensitive information using DoD 5220.22-M compliant methods.
                    </p>
                    <button 
                        className="action-button primary"
                        onClick={() => markStepCompleted('secure')}
                    >
                        <Shield size={16} />
                        Access Secure Operations
                    </button>
                </div>
            ),
            isCompleted: false,
            isOptional: true
        }
    ];

    const [workflowSteps, setWorkflowSteps] = useState(steps);

    useEffect(() => {
        // Find initial step index
        const initialIndex = steps.findIndex(step => step.id === initialStep);
        if (initialIndex !== -1) {
            setCurrentStepIndex(initialIndex);
        }
    }, [initialStep]);

    const markStepCompleted = (stepId: string) => {
        setWorkflowSteps(prev => prev.map(step => 
            step.id === stepId ? { ...step, isCompleted: true } : step
        ));
    };

    const goToStep = (index: number) => {
        if (index >= 0 && index < workflowSteps.length) {
            setCurrentStepIndex(index);
        }
    };

    const goToNextStep = () => {
        if (currentStepIndex < workflowSteps.length - 1) {
            setCurrentStepIndex(currentStepIndex + 1);
        }
    };

    const goToPreviousStep = () => {
        if (currentStepIndex > 0) {
            setCurrentStepIndex(currentStepIndex - 1);
        }
    };

    const completeWorkflow = () => {
        if (onWorkflowComplete) {
            onWorkflowComplete(workflowData);
        }
    };

    const currentStep = workflowSteps[currentStepIndex];
    const completedSteps = workflowSteps.filter(step => step.isCompleted).length;
    const totalSteps = workflowSteps.length;
    const progressPercentage = (completedSteps / totalSteps) * 100;

    return (
        <div className="guided-workflow">
            {/* Welcome Dialog */}
            <Dialog.Root open={showWelcomeDialog} onOpenChange={setShowWelcomeDialog}>
                <Dialog.Portal>
                    <Dialog.Overlay className="dialog-overlay" />
                    <Dialog.Content className="welcome-dialog">
                        <Dialog.Title className="welcome-title">
                            Welcome to PrivacyAI Guided Workflow
                        </Dialog.Title>
                        <Dialog.Description className="welcome-description">
                            This guided workflow will help you configure settings, scan files for privacy data, 
                            review results, and take appropriate security actions. Each step builds on the previous 
                            one to ensure comprehensive privacy protection.
                        </Dialog.Description>
                        
                        <div className="workflow-preview">
                            <h4>Workflow Steps:</h4>
                            <ol className="steps-preview">
                                {workflowSteps.map((step) => (
                                    <li key={step.id} className={step.isOptional ? 'optional' : ''}>
                                        {step.icon}
                                        <span>{step.title}</span>
                                        {step.isOptional && <span className="optional-badge">Optional</span>}
                                    </li>
                                ))}
                            </ol>
                        </div>
                        
                        <div className="welcome-actions">
                            <Dialog.Close asChild>
                                <button className="action-button primary" onClick={() => setShowWelcomeDialog(false)}>
                                    <Play size={16} />
                                    Start Workflow
                                </button>
                            </Dialog.Close>
                        </div>
                        
                        <Dialog.Close asChild>
                            <button className="dialog-close" aria-label="Close">
                                <X size={16} />
                            </button>
                        </Dialog.Close>
                    </Dialog.Content>
                </Dialog.Portal>
            </Dialog.Root>

            {/* Workflow Header */}
            <div className="workflow-header">
                <div className="workflow-progress">
                    <div className="progress-info">
                        <h2>Privacy Protection Workflow</h2>
                        <p>Step {currentStepIndex + 1} of {totalSteps}: {currentStep.title}</p>
                    </div>
                    
                    <div className="progress-bar-container">
                        <Progress.Root className="progress-root" value={progressPercentage}>
                            <Progress.Indicator 
                                className="progress-indicator" 
                                style={{ transform: `translateX(-${100 - progressPercentage}%)` }}
                            />
                        </Progress.Root>
                        <span className="progress-text">{completedSteps}/{totalSteps} completed</span>
                    </div>
                </div>
            </div>

            {/* Step Navigation */}
            <div className="step-navigation">
                {workflowSteps.map((step, index) => (
                    <button
                        key={step.id}
                        className={`step-button ${index === currentStepIndex ? 'active' : ''} ${step.isCompleted ? 'completed' : ''}`}
                        onClick={() => goToStep(index)}
                    >
                        <div className="step-icon">
                            {step.isCompleted ? <CheckCircle size={20} /> : step.icon}
                        </div>
                        <div className="step-info">
                            <div className="step-title">{step.title}</div>
                            {step.isOptional && <div className="step-optional">Optional</div>}
                        </div>
                    </button>
                ))}
            </div>

            {/* Current Step Content */}
            <div className="step-content">
                <div className="step-header">
                    <div className="step-title-section">
                        {currentStep.icon}
                        <div>
                            <h3>{currentStep.title}</h3>
                            <p>{currentStep.description}</p>
                        </div>
                    </div>
                    
                    {currentStep.isCompleted && (
                        <div className="completion-badge">
                            <CheckCircle size={20} />
                            Completed
                        </div>
                    )}
                </div>
                
                <div className="step-component">
                    {currentStep.component}
                </div>
            </div>

            {/* Navigation Controls */}
            <div className="workflow-controls">
                <button
                    className="control-button secondary"
                    onClick={goToPreviousStep}
                    disabled={currentStepIndex === 0}
                >
                    <ArrowLeft size={16} />
                    Previous
                </button>
                
                <div className="control-center">
                    <button
                        className="control-button outline"
                        onClick={() => setShowWelcomeDialog(true)}
                    >
                        <FileText size={16} />
                        Workflow Guide
                    </button>
                </div>
                
                {currentStepIndex < workflowSteps.length - 1 ? (
                    <button
                        className="control-button primary"
                        onClick={goToNextStep}
                    >
                        Next
                        <ArrowRight size={16} />
                    </button>
                ) : (
                    <button
                        className="control-button success"
                        onClick={completeWorkflow}
                    >
                        <CheckCircle size={16} />
                        Complete Workflow
                    </button>
                )}
            </div>
        </div>
    );
};

export default GuidedWorkflow;
