/// Simple validation test for our enhanced detection capabilities
/// 
/// This test validates that our context-aware detection engine and
/// enhanced cryptocurrency detection are working correctly.

use std::collections::HashMap;

#[cfg(test)]
mod validation_tests {
    use super::*;
    use crate::privacy::context_aware_detector::{
        ContextAwareDetector, DocumentContext, DetectionType
    };
    use crate::privacy::enhanced_crypto_detector::{
        EnhancedCryptoDetector, CryptoType, CryptoFindingType
    };

    #[tokio::test]
    async fn test_context_aware_ssn_detection() {
        let detector = ContextAwareDetector::new();

        let text = "Employee <PERSON>'s social security number is *********** for tax reporting.";
        let context = DocumentContext {
            full_text: text.to_string(),
            document_type: Some("employment".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };

        let findings = detector.detect_with_context(text, &context).await.unwrap();

        // Should detect SSN with high confidence due to context
        assert!(!findings.is_empty(), "Should detect SSN in employment context");

        let ssn_finding = findings.iter()
            .find(|f| matches!(f.detection_type, DetectionType::SocialSecurityNumber))
            .expect("SSN should be detected");

        assert!(ssn_finding.confidence > 0.8, "SSN confidence should be high: {}", ssn_finding.confidence);
        assert!(ssn_finding.is_validated, "SSN should be context validated");

        println!("✅ Context-aware SSN detection working: confidence = {:.2}", ssn_finding.confidence);
    }

    #[tokio::test]
    async fn test_false_positive_reduction() {
        let detector = ContextAwareDetector::new();
        
        let text = "Please call customer service at *********** for assistance.";
        let context = DocumentContext {
            full_text: text.to_string(),
            document_type: Some("customer_service".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };
        
        let findings = detector.detect_with_context(text, &context).await.unwrap();
        
        // Should not detect this as SSN due to lack of SSN context
        let ssn_findings: Vec<_> = findings.iter()
            .filter(|f| matches!(f.detection_type, DetectionType::SocialSecurityNumber))
            .collect();
        
        // Either no SSN findings or very low confidence
        if !ssn_findings.is_empty() {
            assert!(ssn_findings[0].confidence < 0.7, 
                   "Phone number should not be detected as SSN with high confidence: {}", 
                   ssn_findings[0].confidence);
        }
        
        println!("✅ False positive reduction working: {} SSN findings", ssn_findings.len());
    }

    #[test]
    fn test_enhanced_crypto_detection() {
        let detector = EnhancedCryptoDetector::new();
        
        let text = "Send payment to bitcoin address: **********************************";
        
        let findings = detector.detect_crypto_assets(text).unwrap();
        
        assert!(!findings.is_empty(), "Should detect Bitcoin address");
        
        let btc_finding = &findings[0];
        assert_eq!(btc_finding.crypto_type, CryptoType::Bitcoin);
        assert_eq!(btc_finding.finding_type, CryptoFindingType::Address);
        assert!(btc_finding.confidence > 0.7, "Bitcoin detection confidence should be high: {}", btc_finding.confidence);
        
        println!("✅ Enhanced crypto detection working: {} findings", findings.len());
    }

    #[test]
    fn test_ethereum_address_detection() {
        let detector = EnhancedCryptoDetector::new();
        
        let text = "ETH address: ******************************************";
        
        let findings = detector.detect_crypto_assets(text).unwrap();
        
        assert!(!findings.is_empty(), "Should detect Ethereum address");
        
        let eth_finding = &findings[0];
        assert_eq!(eth_finding.crypto_type, CryptoType::Ethereum);
        assert!(eth_finding.confidence > 0.7, "Ethereum detection confidence should be high: {}", eth_finding.confidence);
        
        println!("✅ Ethereum detection working: confidence = {:.2}", eth_finding.confidence);
    }

    #[test]
    fn test_mnemonic_phrase_detection() {
        let detector = EnhancedCryptoDetector::new();
        
        let text = "Recovery phrase: abandon ability able about above absent absorb abstract absurd abuse access accident";
        
        let findings = detector.detect_crypto_assets(text).unwrap();
        
        let mnemonic_findings: Vec<_> = findings.iter()
            .filter(|f| f.crypto_type == CryptoType::BIP39Mnemonic)
            .collect();
        
        assert!(!mnemonic_findings.is_empty(), "Should detect BIP39 mnemonic phrase");
        
        let mnemonic_finding = mnemonic_findings[0];
        assert_eq!(mnemonic_finding.finding_type, CryptoFindingType::RecoveryPhrase);
        assert!(mnemonic_finding.confidence > 0.8, "Mnemonic detection confidence should be high: {}", mnemonic_finding.confidence);
        
        println!("✅ BIP39 mnemonic detection working: confidence = {:.2}", mnemonic_finding.confidence);
    }

    #[tokio::test]
    async fn test_performance_benchmark() {
        let detector = ContextAwareDetector::new();
        
        // Create a moderately large text with embedded SSN
        let mut large_text = String::new();
        for i in 0..100 {
            large_text.push_str(&format!("Line {} with some random content about various topics. ", i));
        }
        large_text.push_str("Employee social security number: *********** for payroll processing.");
        
        let context = DocumentContext {
            full_text: large_text.clone(),
            document_type: Some("employment".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };
        
        let start = std::time::Instant::now();
        let findings = detector.detect_with_context(&large_text, &context).await.unwrap();
        let duration = start.elapsed();
        
        // Should complete within reasonable time (< 500ms for this test)
        assert!(duration.as_millis() < 500, "Detection took too long: {}ms", duration.as_millis());
        
        // Should still find the SSN
        assert!(!findings.is_empty(), "Should detect SSN even in large text");
        
        let ssn_finding = findings.iter()
            .find(|f| matches!(f.detection_type, DetectionType::SocialSecurityNumber))
            .expect("SSN should be detected even in large text");
        
        assert!(ssn_finding.confidence > 0.8, "SSN confidence should remain high in large text");
        
        println!("✅ Performance benchmark passed: {}ms for {} characters", 
                duration.as_millis(), large_text.len());
    }

    #[tokio::test]
    async fn test_integrated_detection() {
        let context_detector = ContextAwareDetector::new();
        let crypto_detector = EnhancedCryptoDetector::new();
        
        let text = "Employee SSN: ***********. Bitcoin wallet: **********************************";
        let context = DocumentContext {
            full_text: text.to_string(),
            document_type: Some("financial".to_string()),
            language: Some("en".to_string()),
            metadata: HashMap::new(),
        };
        
        // Test both detectors
        let privacy_findings = context_detector.detect_with_context(text, &context).await.unwrap();
        let crypto_findings = crypto_detector.detect_crypto_assets(text).unwrap();
        
        // Should detect both SSN and Bitcoin address
        assert!(!privacy_findings.is_empty(), "Should detect privacy data");
        assert!(!crypto_findings.is_empty(), "Should detect crypto data");
        
        // Validate SSN detection
        let ssn_finding = privacy_findings.iter()
            .find(|f| matches!(f.detection_type, DetectionType::SocialSecurityNumber))
            .expect("SSN should be detected");
        assert!(ssn_finding.confidence > 0.7, "SSN confidence should be good: {}", ssn_finding.confidence);
        
        // Validate Bitcoin detection
        let btc_finding = &crypto_findings[0];
        assert_eq!(btc_finding.crypto_type, CryptoType::Bitcoin);
        assert!(btc_finding.confidence > 0.7, "Bitcoin confidence should be good: {}", btc_finding.confidence);
        
        println!("✅ Integrated detection working: {} privacy + {} crypto findings", 
                privacy_findings.len(), crypto_findings.len());
    }

    #[test]
    fn test_detection_accuracy_target() {
        // This test validates that we're on track for our 97% accuracy target
        let detector = ContextAwareDetector::new();
        
        // Test cases with known outcomes
        let test_cases = vec![
            ("Employee SSN: ***********", "employment", true),  // Should detect
            ("Call *********** for help", "customer_service", false), // Should not detect
            ("Tax ID: ***********", "tax_document", true),     // Should detect
            ("Random number: ***********", "personal_letter", false), // Should not detect
        ];
        
        let mut correct_predictions = 0;
        let total_cases = test_cases.len();
        
        for (text, doc_type, should_detect) in test_cases {
            let context = DocumentContext {
                full_text: text.to_string(),
                document_type: Some(doc_type.to_string()),
                language: Some("en".to_string()),
                metadata: HashMap::new(),
            };
            
            // Use blocking version for this test
            let rt = tokio::runtime::Runtime::new().unwrap();
            let findings = rt.block_on(detector.detect_with_context(text, &context)).unwrap();
            
            let has_ssn = findings.iter()
                .any(|f| matches!(f.detection_type, DetectionType::SocialSecurityNumber) && f.confidence > 0.7);

            if has_ssn == should_detect {
                correct_predictions += 1;
            }
        }
        
        let accuracy = (correct_predictions as f32 / total_cases as f32) * 100.0;
        
        // We should achieve at least 75% accuracy in this simple test
        // (targeting 97% in full implementation)
        assert!(accuracy >= 75.0, "Accuracy too low: {:.1}%", accuracy);
        
        println!("✅ Accuracy test passed: {:.1}% ({}/{} correct)", 
                accuracy, correct_predictions, total_cases);
    }
}

/// Run all validation tests
pub fn run_validation_tests() {
    println!("🧪 Running Enhanced Detection Validation Tests...");
    
    // Note: In a real implementation, we would run these tests
    // For now, this serves as documentation of our test coverage
    
    println!("✅ All validation tests defined and ready to run");
    println!("📊 Test Coverage:");
    println!("   - Context-aware SSN detection");
    println!("   - False positive reduction");
    println!("   - Enhanced cryptocurrency detection");
    println!("   - Performance benchmarking");
    println!("   - Integrated detection workflow");
    println!("   - Accuracy target validation");
}
