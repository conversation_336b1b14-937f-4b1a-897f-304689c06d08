import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  FileText,
  Zap,
} from 'lucide-react';

// Type definitions for analytics data
interface AnalyticsDashboard {
  performance_metrics: PerformanceMetrics;
  risk_summary: RiskSummary;
  scan_statistics: ScanStatistics;
  active_alerts: PerformanceAlert[];
  last_updated: string;
}

interface PerformanceMetrics {
  avg_scan_time_ms: number;
  peak_memory_usage_mb: number;
  avg_memory_usage_mb: number;
  throughput_files_per_minute: number;
  cache_hit_rate_percent: number;
  error_rate_percent: number;
}

interface RiskSummary {
  overall_risk_level: 'Critical' | 'High' | 'Medium' | 'Low' | 'Minimal';
  critical_findings: number;
  high_risk_findings: number;
  medium_risk_findings: number;
  low_risk_findings: number;
  urgent_files: string[];
}

interface PerformanceAlert {
  id: string;
  alert_type: string;
  severity: 'Info' | 'Warning' | 'Critical';
  message: string;
  metric: string;
  current_value: number;
  threshold_value: number;
  triggered_at: string;
  recommendation: string;
}

interface ScanStatistics {
  total_scans: number;
  total_files: number;
  total_findings: number;
  avg_scan_time_ms: number;
  most_used_profile: string;
  success_rate_percent: number;
}

const AnalyticsDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<AnalyticsDashboard | null>(null);
  const [selectedTimePeriod, setSelectedTimePeriod] = useState('last_24_hours');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, [selectedTimePeriod]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const data = await invoke<AnalyticsDashboard>('get_analytics_dashboard', {
        timePeriod: selectedTimePeriod
      });
      setDashboardData(data);
      setError(null);
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError(err as string);
    } finally {
      setIsLoading(false);
    }
  };

  const exportData = async (format: 'csv' | 'json') => {
    try {
      const exportedData = await invoke<string>('export_analytics_data', {
        timePeriod: selectedTimePeriod,
        exportFormat: format
      });

      // Create download link
      const blob = new Blob([exportedData], {
        type: format === 'csv' ? 'text/csv' : 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics_${selectedTimePeriod}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to export data:', err);
    }
  };

  const getRiskLevelColor = (level: string): string => {
    switch (level) {
      case 'Critical': return 'text-red-600 bg-red-100 border-red-200';
      case 'High': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'Medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'Low': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'Minimal': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  if (isLoading && !dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="m-4 p-4 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center">
          <AlertTriangle className="h-4 w-4 text-red-400 mr-2" />
          <span className="text-red-800">Failed to load analytics data: {error}</span>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">No analytics data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-gray-600">
            Real-time performance monitoring and insights
          </p>
        </div>
        <div className="flex items-center gap-4">
          <select
            value={selectedTimePeriod}
            onChange={(e) => setSelectedTimePeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md bg-white"
          >
            <option value="last_hour">Last Hour</option>
            <option value="last_24_hours">Last 24 Hours</option>
            <option value="last_week">Last Week</option>
            <option value="last_month">Last Month</option>
          </select>
          <button
            onClick={() => exportData('csv')}
            className="px-4 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50"
          >
            Export CSV
          </button>
          <button
            onClick={() => exportData('json')}
            className="px-4 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50"
          >
            Export JSON
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Scans</p>
              <p className="text-2xl font-bold">{dashboardData.scan_statistics.total_scans.toLocaleString()}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Scan Time</p>
              <p className="text-2xl font-bold">{Math.round(dashboardData.performance_metrics.avg_scan_time_ms)}ms</p>
            </div>
            <Clock className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Throughput</p>
              <p className="text-2xl font-bold">{Math.round(dashboardData.performance_metrics.throughput_files_per_minute)}/min</p>
            </div>
            <Zap className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold">{dashboardData.scan_statistics.success_rate_percent.toFixed(1)}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
      </div>

      {/* Active Alerts */}
      {dashboardData.active_alerts.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Active Performance Alerts
          </h2>
          <div className="space-y-3">
            {dashboardData.active_alerts.map((alert) => (
              <div key={alert.id} className="border-l-4 border-l-orange-500 bg-orange-50 p-4 rounded">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-orange-800">
                      {alert.message}
                    </p>
                    <p className="text-sm text-orange-600 mt-1">
                      {alert.recommendation}
                    </p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    alert.severity === 'Critical' ? 'bg-red-100 text-red-800' :
                    alert.severity === 'Warning' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {alert.severity}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Metrics */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <div className="text-sm text-gray-600 mb-2">Memory Usage</div>
            <div className="text-lg font-semibold">{dashboardData.performance_metrics.avg_memory_usage_mb.toFixed(1)} MB</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${Math.min((dashboardData.performance_metrics.avg_memory_usage_mb / 200) * 100, 100)}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="text-sm text-gray-600 mb-2">Cache Hit Rate</div>
            <div className="text-lg font-semibold">{dashboardData.performance_metrics.cache_hit_rate_percent.toFixed(1)}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-green-600 h-2 rounded-full"
                style={{ width: `${dashboardData.performance_metrics.cache_hit_rate_percent}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="text-sm text-gray-600 mb-2">Error Rate</div>
            <div className="text-lg font-semibold">{dashboardData.performance_metrics.error_rate_percent.toFixed(1)}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className="bg-red-600 h-2 rounded-full"
                style={{ width: `${Math.min(dashboardData.performance_metrics.error_rate_percent, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Assessment */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Risk Assessment</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <div className="text-center mb-6">
              <span className={`inline-block px-4 py-2 rounded-lg text-lg font-medium border ${getRiskLevelColor(dashboardData.risk_summary.overall_risk_level)}`}>
                {dashboardData.risk_summary.overall_risk_level} Risk
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{dashboardData.risk_summary.critical_findings}</p>
                <p className="text-sm text-gray-600">Critical</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">{dashboardData.risk_summary.high_risk_findings}</p>
                <p className="text-sm text-gray-600">High</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-yellow-600">{dashboardData.risk_summary.medium_risk_findings}</p>
                <p className="text-sm text-gray-600">Medium</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{dashboardData.risk_summary.low_risk_findings}</p>
                <p className="text-sm text-gray-600">Low</p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-medium text-gray-900 mb-3">Files Requiring Attention</h3>
            {dashboardData.risk_summary.urgent_files.length > 0 ? (
              <div className="space-y-2">
                {dashboardData.risk_summary.urgent_files.slice(0, 5).map((file, index) => (
                  <div key={index} className="text-sm text-gray-600 truncate">
                    {file.split(/[/\\]/).pop()}
                  </div>
                ))}
                {dashboardData.risk_summary.urgent_files.length > 5 && (
                  <div className="text-sm text-gray-500">
                    +{dashboardData.risk_summary.urgent_files.length - 5} more files
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-sm">No urgent files identified</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
