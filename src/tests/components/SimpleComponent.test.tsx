import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';

// Simple test component
const TestComponent = () => {
  return (
    <div>
      <h1>Test Component</h1>
      <button>Click me</button>
    </div>
  );
};

describe('Simple Component Test', () => {
  it('should render test component', () => {
    render(<TestComponent />);
    
    expect(screen.getByText('Test Component')).toBeInTheDocument();
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('should find button element', () => {
    render(<TestComponent />);
    
    const button = screen.getByRole('button', { name: 'Click me' });
    expect(button).toBeInTheDocument();
  });
});
