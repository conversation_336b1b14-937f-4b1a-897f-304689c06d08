/* PrivacyAI Scanner Styles */

/* Theme System using CSS Custom Properties (proven approach) */
:root {
  /* Light theme (default) */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
  --color-text-primary: #111827;
  --color-text-secondary: #374151;
  --color-text-tertiary: #6b7280;
  --color-text-inverse: #ffffff;
  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #dc2626;
}

[data-theme="dark"] {
  /* Dark theme */
  --color-bg-primary: #111827;
  --color-bg-secondary: #1f2937;
  --color-bg-tertiary: #374151;
  --color-text-primary: #f9fafb;
  --color-text-secondary: #e5e7eb;
  --color-text-tertiary: #9ca3af;
  --color-text-inverse: #111827;
  --color-border: #374151;
  --color-border-light: #4b5563;
  --color-primary: #60a5fa;
  --color-primary-hover: #93c5fd;
  --color-success: #34d399;
  --color-warning: #fbbf24;
  --color-error: #f87171;
}

/* Simplified UI Styles with Theme Support */
.simplified-app {
  min-height: 100vh;
  background: var(--color-bg-secondary);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--color-text-primary);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.simplified-main-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.main-header {
  margin-bottom: 3rem;
}

.app-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.app-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.app-description {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0;
  max-width: 500px;
}

.main-actions {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.primary-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 2rem 3rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  min-width: 200px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.primary-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.primary-action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.scan-button:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-color: #3b82f6;
}

.settings-button:hover {
  background: linear-gradient(135deg, #6b7280 0%, #374151 100%);
  color: white;
  border-color: #6b7280;
}

.error-message {
  margin-top: 2rem;
  padding: 1rem 1.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  color: #dc2626;
  max-width: 500px;
}

.screen-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  color: #64748b;
}

.back-button:hover {
  background: #e2e8f0;
  color: #374151;
}

.screen-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

/* Scan Configuration Screen */
.scan-config-screen {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.scan-config-content {
  max-width: 1200px;
  margin: 0 auto;
}

.config-intro {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-description {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.config-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.select-all-button {
  padding: 0.75rem 1.5rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  color: #374151;
}

.select-all-button:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.selection-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.category-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-card:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.category-card.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.category-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: #f8fafc;
  border-radius: 0.75rem;
}

.category-card.selected .category-icon-wrapper {
  background: white;
}

.category-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.category-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  line-height: 1.5;
}

.config-footer {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.continue-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 0.75rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.continue-button.enabled {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
}

.continue-button.enabled:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.continue-button.disabled {
  background: #f1f5f9;
  color: #94a3b8;
  cursor: not-allowed;
}

.continue-hint {
  margin: 1rem 0 0 0;
  font-size: 0.875rem;
  color: #ef4444;
}

/* Checkbox Styles */
.checkbox-root {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: white;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkbox-root:hover {
  border-color: #3b82f6;
}

.checkbox-root[data-state="checked"] {
  background: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-indicator {
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Folder Selection Screen */
.folder-selection-screen {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.folder-selection-content {
  max-width: 1000px;
  margin: 0 auto;
}

.selection-info {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.selection-description {
  font-size: 1.125rem;
  color: #64748b;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.selection-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.browse-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.browse-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.folder-tree {
  background: white;
  border-radius: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tree-header {
  padding: 1.5rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tree-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.tree-content {
  max-height: 400px;
  overflow-y: auto;
}

.folder-node {
  border-bottom: 1px solid #f1f5f9;
}

.folder-node:last-child {
  border-bottom: none;
}

.folder-item {
  padding: 0.75rem 0;
  transition: background-color 0.2s ease;
}

.folder-item:hover {
  background: #f8fafc;
}

.folder-item.selected {
  background: #eff6ff;
}

.folder-item-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #64748b;
  transition: color 0.2s ease;
}

.expand-button:hover {
  color: #374151;
}

.expand-button:disabled {
  cursor: default;
  color: transparent;
}

.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.folder-name {
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.folder-children {
  border-left: 1px solid #e2e8f0;
  margin-left: 1.5rem;
}

.selection-footer {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: 1rem;
  margin: 2rem auto;
  max-width: 1000px;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Results Display Screen */
.results-screen {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.results-content {
  max-width: 1400px;
  margin: 0 auto;
}

.new-scan-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  margin-left: auto;
}

.new-scan-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.results-summary {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.summary-stats {
  display: flex;
  gap: 3rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.save-log-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  color: #374151;
}

.save-log-button:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.category-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.filter-button:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-button.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.results-table-container {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  padding: 1.5rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.select-all-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.select-all-button:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

.selection-counter {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.results-table {
  max-height: 600px;
  overflow-y: auto;
}

.table-header-row {
  display: grid;
  grid-template-columns: 50px 1fr 80px 100px 120px 150px 100px;
  gap: 1rem;
  padding: 1rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-row {
  display: grid;
  grid-template-columns: 50px 1fr 80px 100px 120px 150px 100px;
  gap: 1rem;
  padding: 1rem 2rem;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
}

.table-row.selected {
  background: #eff6ff;
}

.table-cell {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #374151;
}

.checkbox-cell {
  justify-content: center;
}

.name-cell {
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.type-cell {
  font-family: monospace;
  font-size: 0.75rem;
  color: #64748b;
}

.size-cell {
  font-family: monospace;
  font-size: 0.75rem;
  color: #64748b;
}

.date-cell {
  font-size: 0.75rem;
  color: #64748b;
}

.category-cell {
  font-size: 0.75rem;
  color: #64748b;
}

.risk-cell {
  justify-content: center;
}

.risk-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  background: currentColor;
  color: white;
  opacity: 0.9;
}

.empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-results h3 {
  margin: 1rem 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
}

.empty-results p {
  margin: 0;
  color: #64748b;
}

.scanning-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: white;
  border-radius: 1rem;
  margin: 2rem auto;
  max-width: 600px;
  padding: 3rem;
  text-align: center;
}

.scanning-animation {
  position: relative;
  margin-bottom: 2rem;
}

.scanning-spinner {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 4rem;
  height: 4rem;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.scanning-state h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.scanning-state p {
  margin: 0;
  color: #64748b;
  font-size: 1.125rem;
}

/* Settings Screen */
.settings-screen {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.settings-content {
  max-width: 1000px;
  margin: 0 auto;
}

.settings-actions {
  display: flex;
  gap: 1rem;
  margin-left: auto;
}

.reset-button, .save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.reset-button {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.reset-button:hover {
  background: #e2e8f0;
  color: #374151;
}

.save-button {
  background: #3b82f6;
  color: white;
}

.save-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.settings-tabs {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tabs-list {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.tab-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  color: #64748b;
  border-bottom: 2px solid transparent;
}

.tab-trigger:hover {
  color: #374151;
  background: #f1f5f9;
}

.tab-trigger[data-state="active"] {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: white;
}

.tab-content {
  padding: 2rem;
}

.settings-section {
  margin-bottom: 3rem;
}

.settings-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.section-description {
  color: #64748b;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.performance-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.control-value {
  font-weight: 600;
  color: #1e293b;
  font-family: monospace;
}

/* Slider Styles */
.slider-root {
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
  touch-action: none;
  width: 100%;
  height: 20px;
}

.slider-track {
  background: #e2e8f0;
  position: relative;
  flex-grow: 1;
  border-radius: 9999px;
  height: 6px;
}

.slider-range {
  position: absolute;
  background: #3b82f6;
  border-radius: 9999px;
  height: 100%;
}

.slider-thumb {
  display: block;
  width: 20px;
  height: 20px;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  border: 2px solid #3b82f6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider-thumb:hover {
  transform: scale(1.1);
}

.slider-thumb:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.resource-monitors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.resource-monitor {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.monitor-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.monitor-bar {
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.monitor-fill {
  height: 100%;
  transition: width 0.5s ease;
  border-radius: 4px;
}

.monitor-value {
  font-family: monospace;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.filetype-controls {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.filetype-group {
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
}

.filetype-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.filetype-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.filetype-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.extension-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.extension-input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.extension-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.extension-input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* File Actions Panel */
.file-actions-panel {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.file-actions-panel.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.actions-header {
  margin-bottom: 1.5rem;
}

.actions-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.selection-badge {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  background: white;
}

.action-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.action-button.primary {
  color: #3b82f6;
  border-color: #3b82f6;
}

.action-button.primary:hover:not(:disabled) {
  background: #eff6ff;
  border-color: #2563eb;
}

.action-button.secondary {
  color: #64748b;
  border-color: #e2e8f0;
}

.action-button.secondary:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #374151;
}

.action-button.destructive {
  color: #dc2626;
  border-color: #dc2626;
}

.action-button.destructive:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #b91c1c;
}

.no-selection-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 2rem;
  text-align: center;
  color: #9ca3af;
}

.no-selection-message p {
  margin: 0;
  font-size: 0.875rem;
}

/* Dialog Styles */
.dialog-overlay {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  inset: 0;
  z-index: 50;
  animation: fadeIn 0.2s ease;
}

.dialog-content {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90vw;
  max-width: 450px;
  padding: 2rem;
  z-index: 51;
  animation: slideIn 0.2s ease;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.dialog-description {
  color: #64748b;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.dialog-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.dialog-button.secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.dialog-button.secondary:hover {
  background: #e2e8f0;
  color: #374151;
}

.dialog-button.primary {
  background: #3b82f6;
  color: white;
}

.dialog-button.primary:hover {
  background: #2563eb;
}

.dialog-button.destructive {
  background: #dc2626;
  color: white;
}

.dialog-button.destructive:hover {
  background: #b91c1c;
}

/* Dropdown Styles */
.dropdown-content {
  background: white;
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e2e8f0;
  min-width: 180px;
  z-index: 50;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
  color: #374151;
  outline: none;
}

.dropdown-item:hover {
  background: #f8fafc;
}

.dropdown-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dropdown-separator {
  height: 1px;
  background: #e2e8f0;
  margin: 0.5rem 0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Responsive Design and Accessibility */

/* Mobile-first responsive breakpoints */
@media (max-width: 768px) {
  .simplified-main-screen {
    padding: 1rem;
  }

  .app-title {
    font-size: 2rem;
  }

  .main-actions {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .primary-action-button {
    min-width: 100%;
    padding: 1.5rem 2rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .summary-stats {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .table-header-row,
  .table-row {
    grid-template-columns: 40px 1fr 60px 80px;
    font-size: 0.75rem;
  }

  .date-cell,
  .category-cell,
  .risk-cell {
    display: none;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .action-button {
    padding: 0.75rem;
    font-size: 0.75rem;
  }

  .folder-selection-content,
  .scan-config-content,
  .settings-content,
  .results-content {
    padding: 0;
  }

  .screen-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .settings-actions {
    width: 100%;
    justify-content: space-between;
  }

  .tabs-list {
    flex-direction: column;
  }

  .tab-trigger {
    padding: 0.75rem 1rem;
    justify-content: center;
  }

  .performance-controls {
    gap: 1.5rem;
  }

  .resource-monitors {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .simplified-app {
    font-size: 14px;
  }

  .app-title {
    font-size: 1.75rem;
  }

  .category-card {
    padding: 1rem;
  }

  .dialog-content {
    width: 95vw;
    padding: 1.5rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }
}

/* Accessibility improvements */
.simplified-app {
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    --border-color: #000000;
    --text-color: #000000;
    --bg-color: #ffffff;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Focus indicators for keyboard navigation */
.primary-action-button:focus,
.back-button:focus,
.continue-button:focus,
.action-button:focus,
.tab-trigger:focus,
.filter-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.checkbox-root:focus {
  box-shadow: 0 0 0 2px #3b82f6;
}

.slider-thumb:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Note: Add accessibility attributes in HTML:
   - Loading states: role="status" aria-live="polite"
   - Error states: role="alert" aria-live="assertive"
*/

/* Interactive elements */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Ensure sufficient color contrast */
.text-gray-400 {
  color: #6b7280; /* Improved contrast */
}

.text-gray-500 {
  color: #4b5563; /* Improved contrast */
}

/* Base styles using Tailwind-like utility classes */
.min-h-screen {
  min-height: 100vh;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-white {
  background-color: #ffffff;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.bg-blue-700 {
  background-color: #1d4ed8;
}

.bg-green-600 {
  background-color: #16a34a;
}

.bg-green-700 {
  background-color: #15803d;
}

.bg-red-50 {
  background-color: #fef2f2;
}

.bg-gray-300 {
  background-color: #d1d5db;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-800 {
  color: #1f2937;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}

.text-red-600 {
  color: #dc2626;
}

.text-red-800 {
  color: #991b1b;
}

.text-red-400 {
  color: #f87171;
}

.text-orange-500 {
  color: #f97316;
}

.text-yellow-500 {
  color: #eab308;
}

.text-red-700 {
  color: #b91c1c;
}

/* Layout utilities */
.flex {
  display: flex;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

/* Spacing */
.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

/* Padding and margins */
.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

/* Sizing */
.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-8 {
  width: 2rem;
}

.w-full {
  width: 100%;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-8 {
  height: 2rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Typography */
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

/* Borders and shadows */
.border {
  border-width: 1px;
  border-color: #e5e7eb;
}

.border-b {
  border-bottom-width: 1px;
  border-color: #e5e7eb;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.border-red-200 {
  border-color: #fecaca;
}

.border-transparent {
  border-color: transparent;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

/* Interactive states */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-green-700:hover {
  background-color: #15803d;
}

.hover\:text-gray-600:hover {
  color: #4b5563;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Disabled states */
.disabled\:bg-gray-300:disabled {
  background-color: #d1d5db;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

/* Text utilities */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.break-all {
  word-break: break-all;
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Loading spinner styles */
.loading-spinner {
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

.loading-spinner-large {
  border: 6px solid #f3f4f6;
  border-top: 6px solid #10b981;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
  margin: 30px auto;
}

.scanning-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.scanning-text {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20px;
}

.scanning-progress {
  font-size: 14px;
  color: #6b7280;
  margin-top: 10px;
  max-width: 400px;
  line-height: 1.5;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
