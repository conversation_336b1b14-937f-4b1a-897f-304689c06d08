/* Enhanced Configuration Panel with Radix UI Styling */

.enhanced-config-panel {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

.enhanced-config-panel.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header Styles */
.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 2px solid #f1f5f9;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-icon {
    width: 32px;
    height: 32px;
    color: #3b82f6;
}

.config-header h2 {
    margin: 0;
    font-size: 1.875rem;
    font-weight: 700;
    color: #1e293b;
}

.config-header p {
    margin: 4px 0 0 0;
    color: #64748b;
    font-size: 1rem;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Button Styles */
.reset-button, .save-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reset-button {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.reset-button:hover:not(:disabled) {
    background: #f1f5f9;
    color: #475569;
}

.save-button {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.save-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.save-button:disabled {
    background: #94a3b8;
    cursor: not-allowed;
    transform: none;
}

.save-button.saved {
    background: linear-gradient(135deg, #10b981, #059669);
}

.save-button.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* Tabs Styles */
.config-tabs {
    width: 100%;
}

.tabs-list {
    display: flex;
    gap: 4px;
    margin-bottom: 24px;
    background: #f8fafc;
    padding: 4px;
    border-radius: 12px;
}

.tab-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    justify-content: center;
}

.tab-trigger:hover {
    background: #f1f5f9;
    color: #475569;
}

.tab-trigger[data-state="active"] {
    background: white;
    color: #3b82f6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
    outline: none;
}

/* Configuration Section Styles */
.config-section {
    padding: 24px;
    background: #fafbfc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.config-section h3 {
    margin: 0 0 24px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.config-group {
    margin-bottom: 24px;
}

.config-group:last-child {
    margin-bottom: 0;
}

.config-group h4 {
    margin: 0 0 16px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
}

/* Switch and Checkbox Styles */
.switch-label, .checkbox-item {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px 0;
}

.switch-text {
    font-weight: 500;
    color: #374151;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3b82f6;
    cursor: pointer;
}

/* Slider Styles */
.slider-label {
    display: block;
    margin-bottom: 12px;
    font-weight: 500;
    color: #374151;
}

.slider-root {
    position: relative;
    display: flex;
    align-items: center;
    user-select: none;
    touch-action: none;
    width: 100%;
    height: 20px;
    margin-bottom: 8px;
}

.slider-track {
    background: #e2e8f0;
    position: relative;
    flex-grow: 1;
    border-radius: 9999px;
    height: 6px;
}

.slider-range {
    position: absolute;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 9999px;
    height: 100%;
}

.slider-thumb {
    display: block;
    width: 20px;
    height: 20px;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.slider-thumb:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #64748b;
}

/* Dialog Styles */
.dialog-overlay {
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    inset: 0;
    animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
    z-index: 1000;
}

.dialog-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 38px -10px rgba(22, 23, 24, 0.35), 0 10px 20px -15px rgba(22, 23, 24, 0.2);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 450px;
    max-height: 85vh;
    padding: 24px;
    animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
    z-index: 1001;
}

.dialog-title {
    margin: 0 0 12px 0;
    font-weight: 600;
    color: #1e293b;
    font-size: 1.125rem;
}

.dialog-description {
    margin: 0 0 24px 0;
    color: #64748b;
    font-size: 0.875rem;
    line-height: 1.5;
}

.dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.cancel-button, .confirm-button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.confirm-button {
    background: #ef4444;
    color: white;
    border: none;
}

.confirm-button:hover {
    background: #dc2626;
}

.dialog-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.dialog-close:hover {
    background: #f1f5f9;
}

/* Toast Styles */
.toast-viewport {
    position: fixed;
    bottom: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    padding: 24px;
    gap: 10px;
    width: 390px;
    max-width: 100vw;
    margin: 0;
    list-style: none;
    z-index: 2147483647;
    outline: none;
}

.toast-root {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 38px -10px rgba(22, 23, 24, 0.35), 0 10px 20px -15px rgba(22, 23, 24, 0.2);
    padding: 16px;
    display: grid;
    grid-template-areas: "title action";
    grid-template-columns: auto max-content;
    column-gap: 15px;
    align-items: center;
    animation: slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
}

.toast-title {
    grid-area: title;
    margin: 0;
    color: #1e293b;
    font-weight: 500;
    font-size: 0.875rem;
}

.toast-close {
    grid-area: action;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

@keyframes overlayShow {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes contentShow {
    from { opacity: 0; transform: translate(-50%, -48%) scale(0.96); }
    to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

@keyframes slideIn {
    from { transform: translateX(calc(100% + 24px)); }
    to { transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-config-panel {
        padding: 16px;
        margin: 16px;
    }
    
    .config-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .tabs-list {
        flex-direction: column;
    }
    
    .checkbox-grid {
        grid-template-columns: 1fr;
    }
    
    .dialog-content {
        width: 95vw;
        padding: 16px;
    }
    
    .toast-viewport {
        width: 100vw;
        padding: 16px;
    }
}
