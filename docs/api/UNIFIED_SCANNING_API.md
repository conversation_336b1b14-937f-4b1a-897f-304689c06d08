# 🔍 **Unified Scanning API Documentation**

**Version**: 2.0  
**Created**: July 27, 2025  
**Status**: 🚀 **Proposed Architecture - Phase 4 Implementation**

## 📋 **Overview**

The Unified Scanning API provides a single interface for comprehensive file analysis, combining privacy detection, security scanning, corruption detection, and duplicate identification in one optimized operation.

## 🎯 **Key Benefits**

- **Single Scan Operation**: All detection types in one pass
- **Performance Optimized**: <800ms vs >1400ms fragmented approach
- **Memory Efficient**: 40% reduction through shared caching
- **User Configurable**: Selective output and filtering options
- **Scalable**: Support for 1000+ file directory scanning

## 🚀 **Core Commands**

### **unified_scan_file**

Performs comprehensive analysis of a single file with configurable detection types.

```rust
#[tauri::command]
pub async fn unified_scan_file(
    file_path: String,
    scan_config: UnifiedScanConfig,
    state: State<'_, AppState>,
) -> Result<UnifiedScanResult, String>
```

**Parameters:**
- `file_path`: Path to the file to scan
- `scan_config`: Configuration specifying which detection types to run
- `state`: Application state containing detector instances

**Returns:** `UnifiedScanResult` with findings from all enabled detection types

**Example Usage:**
```typescript
const scanConfig: UnifiedScanConfig = {
  enable_privacy_detection: true,
  enable_cryptocurrency_detection: true,
  enable_corruption_detection: false,
  enable_duplicate_detection: false,
  output_format: "HighRiskOnly",
  performance_mode: "Balanced"
};

const result = await invoke('unified_scan_file', {
  filePath: '/path/to/document.pdf',
  scanConfig: scanConfig
});
```

### **unified_scan_directory**

Performs comprehensive analysis of all files in a directory with batch optimization.

```rust
#[tauri::command]
pub async fn unified_scan_directory(
    directory_path: String,
    scan_config: UnifiedScanConfig,
    progress_callback: Option<String>,
    state: State<'_, AppState>,
) -> Result<Vec<UnifiedScanResult>, String>
```

**Parameters:**
- `directory_path`: Path to the directory to scan
- `scan_config`: Configuration for detection types and output
- `progress_callback`: Optional callback for progress updates
- `state`: Application state

**Returns:** Vector of `UnifiedScanResult` for all processed files

**Example Usage:**
```typescript
const results = await invoke('unified_scan_directory', {
  directoryPath: '/path/to/documents',
  scanConfig: scanConfig,
  progressCallback: 'scan_progress_update'
});
```

### **unified_scan_batch**

Performs analysis on a specific list of files with optimized batch processing.

```rust
#[tauri::command]
pub async fn unified_scan_batch(
    file_paths: Vec<String>,
    scan_config: UnifiedScanConfig,
    state: State<'_, AppState>,
) -> Result<Vec<UnifiedScanResult>, String>
```

## 📊 **Data Structures**

### **UnifiedScanConfig**

Configuration structure for controlling scan behavior and output.

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedScanConfig {
    /// Enable privacy-sensitive data detection
    pub enable_privacy_detection: bool,
    
    /// Enable cryptocurrency and security pattern detection
    pub enable_cryptocurrency_detection: bool,
    
    /// Enable file corruption detection
    pub enable_corruption_detection: bool,
    
    /// Enable duplicate file detection
    pub enable_duplicate_detection: bool,
    
    /// Output format configuration
    pub output_format: OutputFormat,
    
    /// Performance mode selection
    pub performance_mode: PerformanceMode,
    
    /// Maximum file size to scan (in MB)
    pub max_file_size_mb: u64,
    
    /// Timeout per file (in milliseconds)
    pub timeout_ms: u64,
}
```

### **OutputFormat**

Configurable output filtering options.

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OutputFormat {
    /// All findings from all enabled modules
    Full,
    
    /// Only privacy-related findings
    PrivacyOnly,
    
    /// Only security-related findings (cryptocurrency, patterns)
    SecurityOnly,
    
    /// Only critical and high severity findings
    HighRiskOnly,
    
    /// Only corruption and duplicate findings
    FileIntegrityOnly,
    
    /// User-defined custom filtering
    Custom(OutputFilter),
}
```

### **UnifiedScanResult**

Comprehensive result structure combining all detection types.

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UnifiedScanResult {
    /// Path to the scanned file
    pub file_path: PathBuf,
    
    /// Privacy detection findings (if enabled)
    pub privacy_findings: Option<Vec<PrivacyFinding>>,
    
    /// Security detection findings (if enabled)
    pub security_findings: Option<Vec<UnifiedFinding>>,
    
    /// Corruption detection result (if enabled)
    pub corruption_findings: Option<CorruptionDetectionResult>,
    
    /// Duplicate file status (if enabled)
    pub duplicate_status: Option<DuplicateStatus>,
    
    /// Overall risk score (0.0-1.0)
    pub overall_risk_score: f32,
    
    /// Total processing time in milliseconds
    pub processing_time_ms: u64,
    
    /// Scan timestamp
    pub scan_timestamp: String,
    
    /// File metadata
    pub file_metadata: FileMetadata,
    
    /// Any errors encountered during scanning
    pub errors: Vec<String>,
}
```

## ⚡ **Performance Specifications**

### **Timing Targets**

| **Operation** | **Target Time** | **Current Fragmented** | **Improvement** |
|--------------|----------------|------------------------|-----------------|
| **Single File Scan** | <800ms | >1400ms | 43% faster |
| **Directory Scan (100 files)** | <80 seconds | >140 seconds | 43% faster |
| **Batch Processing** | 75+ files/minute | 43 files/minute | 75% faster |

### **Memory Efficiency**

| **Component** | **Unified Architecture** | **Fragmented Architecture** | **Reduction** |
|--------------|-------------------------|----------------------------|---------------|
| **File Cache** | Shared 50MB | 4x separate 25MB each | 50% reduction |
| **Pattern Cache** | Unified 15MB | Separate 40MB total | 62% reduction |
| **Result Storage** | Optimized 10MB | Redundant 25MB | 60% reduction |

### **Scalability Metrics**

- **Small Files** (<1MB): 100+ files/minute
- **Medium Files** (1-10MB): 75+ files/minute  
- **Large Files** (10-100MB): 25+ files/minute
- **Directory Limit**: 10,000+ files with linear scaling
- **Memory Scaling**: O(log n) with intelligent caching

## 🔧 **Configuration Examples**

### **Privacy-Focused Scanning**

```typescript
const privacyConfig: UnifiedScanConfig = {
  enable_privacy_detection: true,
  enable_cryptocurrency_detection: true,
  enable_corruption_detection: false,
  enable_duplicate_detection: false,
  output_format: "PrivacyOnly",
  performance_mode: "Comprehensive",
  max_file_size_mb: 100,
  timeout_ms: 1000
};
```

### **File Integrity Scanning**

```typescript
const integrityConfig: UnifiedScanConfig = {
  enable_privacy_detection: false,
  enable_cryptocurrency_detection: false,
  enable_corruption_detection: true,
  enable_duplicate_detection: true,
  output_format: "FileIntegrityOnly",
  performance_mode: "Fast",
  max_file_size_mb: 500,
  timeout_ms: 500
};
```

### **Comprehensive Security Audit**

```typescript
const auditConfig: UnifiedScanConfig = {
  enable_privacy_detection: true,
  enable_cryptocurrency_detection: true,
  enable_corruption_detection: true,
  enable_duplicate_detection: true,
  output_format: "Full",
  performance_mode: "Comprehensive",
  max_file_size_mb: 100,
  timeout_ms: 2000
};
```

## 🚨 **Error Handling**

### **Error Types**

```rust
#[derive(Debug, Error, Clone, Serialize, Deserialize)]
pub enum UnifiedScanError {
    #[error("File not found: {path}")]
    FileNotFound { path: String },
    
    #[error("File too large: {size_mb}MB exceeds limit of {limit_mb}MB")]
    FileTooLarge { size_mb: u64, limit_mb: u64 },
    
    #[error("Scan timeout: {timeout_ms}ms exceeded")]
    ScanTimeout { timeout_ms: u64 },
    
    #[error("Permission denied: {path}")]
    PermissionDenied { path: String },
    
    #[error("Detector initialization failed: {module}")]
    DetectorInitFailed { module: String },
    
    #[error("Invalid configuration: {message}")]
    InvalidConfig { message: String },
}
```

### **Error Recovery**

- **Partial Results**: Return successful detections even if some modules fail
- **Graceful Degradation**: Continue scanning other files if one fails
- **Detailed Logging**: Comprehensive error reporting for debugging
- **Retry Logic**: Automatic retry for transient failures

## 📈 **Migration Guide**

### **From Fragmented to Unified Scanning**

**Before (Fragmented):**
```typescript
// Multiple separate calls required
const privacyResult = await invoke('scan_file_for_privacy', { filePath });
const cryptoResult = await invoke('detect_cryptocurrency', { filePath });
const corruptionResult = await invoke('detect_corruption', { filePath });
const duplicateResult = await invoke('find_duplicates', { filePath });

// Manual result correlation required
const combinedResult = combineResults(privacyResult, cryptoResult, corruptionResult, duplicateResult);
```

**After (Unified):**
```typescript
// Single call with configurable output
const result = await invoke('unified_scan_file', {
  filePath,
  scanConfig: {
    enable_privacy_detection: true,
    enable_cryptocurrency_detection: true,
    enable_corruption_detection: true,
    enable_duplicate_detection: true,
    output_format: "Full",
    performance_mode: "Balanced"
  }
});
```

## 🔮 **Future Enhancements**

### **Planned Features**

- **Streaming Results**: Real-time result streaming for large files
- **Cloud Integration**: Optional cloud-based AI model inference
- **Custom Workflows**: User-defined scanning pipelines
- **Machine Learning**: Adaptive confidence scoring based on user feedback
- **Distributed Scanning**: Multi-core and multi-machine scanning support

### **API Versioning**

- **v2.0**: Unified scanning architecture (current proposal)
- **v2.1**: Streaming results and cloud integration
- **v2.2**: Custom workflows and ML enhancements
- **v3.0**: Distributed scanning and advanced analytics
