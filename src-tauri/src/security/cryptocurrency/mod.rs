/// Cryptocurrency security detection module
/// 
/// This module provides comprehensive cryptocurrency security detection including:
/// - Private key detection (Bitcoin WIF, Ethereum hex, raw 256-bit)
/// - Cryptocurrency address detection and validation
/// - Exchange API key and secret detection
/// - Wallet file detection
/// - Hardware wallet recovery information detection

pub mod patterns;
pub mod validation;
pub mod detector;
pub mod wallet_detector;

// Re-export main types
pub use detector::{CryptocurrencyDetector, CryptocurrencyFinding, CryptocurrencyType};
pub use validation::{CryptoValidationResult, CryptoValidationError, CryptocurrencyValidator};
pub use patterns::{CRYPTO_PATTERNS, CryptoPatternType};
pub use wallet_detector::{
    WalletDetector, WalletDetectionResult, WalletType, WalletDetectionMethod,
    WalletDetectionError, WALLET_SIGNATURES
};
