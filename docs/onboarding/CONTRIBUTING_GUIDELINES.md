# 🤝 **Contributing Guidelines**

**Version**: 2.0.0-beta  
**Last Updated**: July 27, 2025  
**Scope**: Code contribution standards and development workflow for PrivacyAI

## 🎯 **Overview**

We welcome contributions to PrivacyAI! This guide outlines our development standards, workflow, and best practices to ensure high-quality, maintainable code that aligns with our unified scanning architecture and performance goals.

## 📋 **Before You Start**

### **Required Reading**

1. **[Developer Setup Guide](DEVELOPER_SETUP_GUIDE.md)** - Complete development environment setup
2. **[Code of Conduct](CODE_OF_CONDUCT.md)** - Community standards and expectations
3. **[Architecture Overview](../technical/UNIFIED_SCANNING_ARCHITECTURE.md)** - Understanding the system design
4. **[API Documentation](../api/UNIFIED_SCANNING_API.md)** - API standards and patterns

### **Development Environment**

- ✅ **Node.js v18+** with npm
- ✅ **Rust latest stable** with Cargo
- ✅ **Git** with proper configuration
- ✅ **VS Code** with recommended extensions (optional but recommended)

## 🔄 **Development Workflow**

### **1. Issue Creation and Assignment**

#### **Before Starting Work**

```bash
# Check existing issues
# Visit: https://github.com/LogicPTK/PrivacyAI/issues

# Create new issue if needed
# Use appropriate issue template:
# - Bug Report
# - Feature Request
# - Performance Improvement
# - Documentation Update
```

#### **Issue Labels**

| Label | Purpose | Priority |
|-------|---------|----------|
| `bug` | Bug fixes | High |
| `feature` | New features | Medium |
| `performance` | Performance improvements | High |
| `documentation` | Documentation updates | Low |
| `good-first-issue` | Beginner-friendly | N/A |
| `help-wanted` | Community contributions welcome | N/A |

### **2. Branch Management**

#### **Branch Naming Convention**

```bash
# Feature branches
feature/scan-profile-optimization
feature/new-detection-type
feature/ui-configuration-panel

# Bug fix branches
fix/memory-leak-in-cache
fix/incorrect-confidence-scoring
fix/ui-rendering-issue

# Documentation branches
docs/api-reference-update
docs/user-guide-improvements
docs/architecture-diagrams
```

#### **Branch Creation**

```bash
# Create and switch to new branch
git checkout -b feature/your-feature-name

# Ensure you're starting from the latest main
git checkout main
git pull origin main
git checkout -b feature/your-feature-name
```

### **3. Development Standards**

#### **Code Quality Requirements**

- ✅ **All tests pass**: `npm test && cargo test`
- ✅ **Code formatted**: `npm run format && cargo fmt`
- ✅ **Linting passes**: `npm run lint && cargo clippy`
- ✅ **No compiler warnings**: Clean build output
- ✅ **Documentation updated**: Relevant docs reflect changes

#### **Performance Standards**

- ✅ **Maintain performance targets**: <800ms unified scan time
- ✅ **Memory efficiency**: No memory leaks or excessive usage
- ✅ **Benchmark validation**: Performance tests pass
- ✅ **Scalability**: Linear performance scaling maintained

### **4. Commit Standards**

#### **Commit Message Format**

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

#### **Commit Types**

| Type | Purpose | Example |
|------|---------|---------|
| `feat` | New features | `feat(scan): add cryptocurrency detection profile` |
| `fix` | Bug fixes | `fix(cache): resolve memory leak in shared cache` |
| `perf` | Performance improvements | `perf(ocr): optimize text extraction pipeline` |
| `docs` | Documentation | `docs(api): update unified scanning endpoints` |
| `style` | Code formatting | `style(rust): apply cargo fmt formatting` |
| `refactor` | Code refactoring | `refactor(config): simplify profile management` |
| `test` | Testing | `test(integration): add scan configuration tests` |
| `chore` | Maintenance | `chore(deps): update dependencies` |

#### **Commit Examples**

```bash
# Good commits
git commit -m "feat(scan): implement granular detection type selection"
git commit -m "fix(ui): resolve scan profile selection bug"
git commit -m "perf(cache): reduce memory usage by 40%"
git commit -m "docs(guide): add performance optimization examples"

# Bad commits (avoid these)
git commit -m "fix stuff"
git commit -m "update code"
git commit -m "WIP"
```

### **5. Pull Request Process**

#### **Before Creating PR**

```bash
# Ensure your branch is up to date
git checkout main
git pull origin main
git checkout feature/your-feature-name
git rebase main

# Run full test suite
npm test
cargo test

# Verify build
npm run tauri build

# Check formatting and linting
npm run format
cargo fmt
npm run lint
cargo clippy
```

#### **PR Title and Description**

**Title Format**: `<type>(<scope>): <description>`

**Description Template**:
```markdown
## 📋 Summary
Brief description of changes and motivation.

## 🔄 Changes Made
- [ ] Added new scan profile for financial auditing
- [ ] Implemented performance benchmarking
- [ ] Updated API documentation

## 🧪 Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Performance benchmarks validated
- [ ] Manual testing completed

## 📊 Performance Impact
- Processing time: X ms (Y% improvement)
- Memory usage: X MB (Y% reduction)
- Throughput: X files/minute

## 📚 Documentation
- [ ] API documentation updated
- [ ] User guide updated
- [ ] Code comments added
- [ ] Architecture docs updated

## ✅ Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Tests added for new functionality
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

#### **Review Process**

1. **Automated Checks**: CI/CD pipeline runs tests and linting
2. **Code Review**: At least one team member reviews the code
3. **Performance Review**: Performance impact assessed for core changes
4. **Documentation Review**: Documentation updates verified
5. **Final Approval**: Maintainer approves and merges

## 🧪 **Testing Standards**

### **Frontend Testing (React/TypeScript)**

#### **Unit Tests**
```typescript
// Example: Component testing
import { render, screen } from '@testing-library/react';
import { ScanConfiguration } from '../ScanConfiguration';

describe('ScanConfiguration', () => {
  it('should render scan profiles correctly', () => {
    render(<ScanConfiguration />);
    expect(screen.getByText('Quick Text Scan')).toBeInTheDocument();
  });
});
```

#### **Integration Tests**
```typescript
// Example: API integration testing
import { invoke } from '@tauri-apps/api/tauri';

describe('Scan Configuration API', () => {
  it('should estimate performance correctly', async () => {
    const config = { /* test config */ };
    const estimate = await invoke('estimate_scan_performance', { config });
    expect(estimate.estimated_time_ms).toBeLessThan(1000);
  });
});
```

### **Backend Testing (Rust)**

#### **Unit Tests**
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scan_profile_performance_estimation() {
        let manager = ScanProfileManager::new();
        let config = GranularScanConfig::default();
        let estimate = manager.estimate_performance(&config);
        
        assert!(estimate.estimated_time_ms < 1000);
        assert!(estimate.estimated_memory_mb < 200);
    }
}
```

#### **Integration Tests**
```rust
#[tokio::test]
async fn test_unified_scan_workflow() {
    let orchestrator = UnifiedScanOrchestrator::new();
    let config = GranularScanConfig::default();
    
    let result = orchestrator.scan_file("test.txt", &config).await;
    assert!(result.is_ok());
    assert!(result.unwrap().processing_time_ms < 800);
}
```

### **Performance Testing**

#### **Benchmark Tests**
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_scan_performance(c: &mut Criterion) {
    c.bench_function("unified_scan", |b| {
        b.iter(|| {
            // Benchmark unified scanning performance
            black_box(perform_unified_scan("test_file.txt"))
        })
    });
}

criterion_group!(benches, benchmark_scan_performance);
criterion_main!(benches);
```

## 📝 **Code Style Guidelines**

### **Rust Code Style**

#### **Formatting**
```rust
// Use cargo fmt for automatic formatting
cargo fmt

// Key principles:
// - 4 spaces for indentation
// - 100 character line limit
// - Descriptive variable names
// - Comprehensive error handling
```

#### **Error Handling**
```rust
// Good: Comprehensive error handling
pub fn scan_file(path: &str) -> Result<ScanResult, ScanError> {
    let file_content = std::fs::read_to_string(path)
        .map_err(|e| ScanError::FileRead(e.to_string()))?;
    
    // Process file...
    Ok(result)
}

// Bad: Unwrapping without error handling
pub fn scan_file(path: &str) -> ScanResult {
    let file_content = std::fs::read_to_string(path).unwrap(); // Don't do this
    // Process file...
}
```

#### **Documentation**
```rust
/// Performs unified scanning with granular configuration
/// 
/// # Arguments
/// 
/// * `file_path` - Path to the file to scan
/// * `config` - Granular scan configuration
/// 
/// # Returns
/// 
/// Returns a `UnifiedScanResult` containing findings from all enabled detection types
/// 
/// # Errors
/// 
/// Returns `ScanError` if file cannot be read or processing fails
/// 
/// # Example
/// 
/// ```
/// let config = GranularScanConfig::default();
/// let result = scan_file_unified("document.pdf", &config)?;
/// ```
pub async fn scan_file_unified(
    file_path: &str, 
    config: &GranularScanConfig
) -> Result<UnifiedScanResult, ScanError> {
    // Implementation...
}
```

### **TypeScript/React Code Style**

#### **Component Structure**
```typescript
// Good: Well-structured component
interface ScanConfigurationProps {
  onConfigChange: (config: GranularScanConfig) => void;
  initialConfig?: GranularScanConfig;
}

export const ScanConfiguration: React.FC<ScanConfigurationProps> = ({
  onConfigChange,
  initialConfig
}) => {
  const [config, setConfig] = useState(initialConfig ?? defaultConfig);
  
  // Component logic...
  
  return (
    <div className="scan-configuration">
      {/* JSX content */}
    </div>
  );
};
```

#### **Type Definitions**
```typescript
// Good: Comprehensive type definitions
interface PerformanceEstimate {
  estimated_time_ms: number;
  estimated_memory_mb: number;
  estimated_throughput: number;
  impact_score: number;
}

interface ScanProfile {
  name: string;
  description: string;
  performance_estimate: PerformanceEstimate;
  config: GranularScanConfig;
}
```

## 🚀 **Performance Guidelines**

### **Performance Targets**

| Metric | Target | Measurement |
|--------|--------|-------------|
| **Unified Scan Time** | <800ms | Per file processing |
| **Memory Usage** | <100MB | Peak memory during scan |
| **Throughput** | 75+ files/min | Comprehensive scan mode |
| **Cache Hit Rate** | >80% | Shared file cache efficiency |

### **Performance Best Practices**

#### **Rust Performance**
- Use `Vec::with_capacity()` when size is known
- Prefer `&str` over `String` for read-only operations
- Use `Arc<T>` for shared immutable data
- Implement `Clone` efficiently for frequently cloned types
- Profile with `cargo flamegraph` for bottleneck identification

#### **React Performance**
- Use `React.memo()` for expensive components
- Implement `useMemo()` and `useCallback()` for expensive computations
- Avoid inline object creation in render methods
- Use `React.lazy()` for code splitting large components

## 📚 **Documentation Standards**

### **Code Documentation**

- **Rust**: Use `///` for public APIs, `//` for implementation details
- **TypeScript**: Use JSDoc comments for public interfaces
- **README updates**: Update relevant README sections for new features
- **API documentation**: Update API docs for new endpoints or changes

### **User Documentation**

- **User guides**: Update user guides for new features
- **Configuration guides**: Document new configuration options
- **Performance guides**: Update performance recommendations
- **Troubleshooting**: Add common issues and solutions

## ❌ **Common Mistakes to Avoid**

### **Code Quality Issues**
- ❌ Committing code that doesn't compile
- ❌ Ignoring compiler warnings
- ❌ Not running tests before committing
- ❌ Hardcoding values instead of using configuration
- ❌ Not handling errors properly

### **Performance Issues**
- ❌ Introducing memory leaks
- ❌ Not profiling performance-critical changes
- ❌ Blocking the main thread with expensive operations
- ❌ Not considering scalability implications

### **Process Issues**
- ❌ Working on main branch directly
- ❌ Creating PRs without proper testing
- ❌ Not updating documentation
- ❌ Ignoring code review feedback
- ❌ Not following commit message conventions

## 🆘 **Getting Help**

### **Resources**
- **Documentation**: [Documentation Index](../DOCUMENTATION_INDEX.md)
- **GitHub Issues**: [Create an issue](https://github.com/LogicPTK/PrivacyAI/issues)
- **GitHub Discussions**: [Community discussions](https://github.com/LogicPTK/PrivacyAI/discussions)
- **Code Review**: Request review from team members

### **Contact**
- **Technical Questions**: Create GitHub issue with `question` label
- **Process Questions**: Create GitHub discussion
- **Security Issues**: Email <EMAIL> (if applicable)

**Thank you for contributing to PrivacyAI! Your contributions help make privacy scanning more accessible and effective for everyone.** 🙏
