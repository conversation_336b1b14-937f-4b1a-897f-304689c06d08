/**
 * Tesseract.js Integration for PrivacyAI
 * 
 * Provides OCR capabilities using Tesseract.js with performance optimization
 * and integration with the Rust backend preprocessing pipeline.
 */

import { createWorker } from 'tesseract.js';
import { invoke } from '@tauri-apps/api/tauri';

class TesseractOCREngine {
    constructor() {
        this.worker = null;
        this.isInitialized = false;
        this.supportedLanguages = ['eng', 'spa', 'fra', 'deu', 'ita', 'por', 'rus', 'chi_sim', 'jpn', 'kor'];
        this.performanceMetrics = {
            totalProcessed: 0,
            averageTime: 0,
            successRate: 0,
            errors: []
        };
    }

    /**
     * Initialize Tesseract.js worker
     */
    async initialize(language = 'eng') {
        if (this.isInitialized) {
            return true;
        }

        try {
            console.log('🔧 Initializing Tesseract.js worker...');
            const startTime = performance.now();

            this.worker = await createWorker(language, 1, {
                logger: m => {
                    if (m.status === 'recognizing text') {
                        console.log(`📖 OCR Progress: ${Math.round(m.progress * 100)}%`);
                    }
                }
            });

            // Configure OCR parameters for better accuracy
            await this.worker.setParameters({
                tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?@#$%^&*()-_=+[]{}|;:\'\"<>/\\`~',
                tessedit_pageseg_mode: '6', // Uniform block of text
                preserve_interword_spaces: '1',
            });

            const initTime = performance.now() - startTime;
            console.log(`✅ Tesseract.js initialized in ${initTime.toFixed(2)}ms`);

            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Tesseract.js:', error);
            this.performanceMetrics.errors.push({
                type: 'initialization',
                message: error.message,
                timestamp: new Date().toISOString()
            });
            return false;
        }
    }

    /**
     * Process OCR request with Rust backend preprocessing
     */
    async processOCR(imageData, options = {}) {
        const startTime = performance.now();
        
        try {
            // Default options
            const config = {
                language: options.language || 'eng',
                preprocess: options.preprocess !== false,
                format: options.format || 'png',
                confidence_threshold: options.confidence_threshold || 0.6,
                enable_preprocessing: options.enable_preprocessing !== false,
                max_file_size: options.max_file_size || 10 * 1024 * 1024, // 10MB
                ...options
            };

            console.log('🔍 Starting OCR processing with config:', config);

            // Step 1: Send to Rust backend for preprocessing
            const preprocessResponse = await invoke('process_ocr', {
                request: {
                    image_data: imageData,
                    format: config.format,
                    config: config,
                    preprocess: config.preprocess,
                    language: config.language
                }
            });

            if (!preprocessResponse.success) {
                throw new Error(preprocessResponse.error || 'Preprocessing failed');
            }

            console.log('✅ Preprocessing completed:', {
                time: preprocessResponse.processing_time_ms + 'ms',
                applied: preprocessResponse.preprocessing_applied,
                dimensions: preprocessResponse.performance_metrics.image_dimensions
            });

            // Step 2: Initialize Tesseract.js if needed
            if (!this.isInitialized) {
                const initialized = await this.initialize(config.language);
                if (!initialized) {
                    throw new Error('Failed to initialize OCR engine');
                }
            }

            // Step 3: Perform OCR on preprocessed image
            const ocrStartTime = performance.now();
            
            // Convert base64 back to image for Tesseract.js
            const processedImageData = `data:image/${config.format};base64,${preprocessResponse.text}`;
            
            const { data } = await this.worker.recognize(processedImageData);
            const ocrTime = performance.now() - ocrStartTime;

            // Step 4: Process results
            const totalTime = performance.now() - startTime;
            const wordCount = data.text.trim().split(/\s+/).filter(word => word.length > 0).length;

            // Calculate confidence score
            const confidence = this.calculateConfidence(data);

            // Update performance metrics
            this.updatePerformanceMetrics(totalTime, confidence >= config.confidence_threshold);

            const result = {
                success: true,
                text: data.text.trim(),
                confidence: confidence,
                processing_time_ms: Math.round(totalTime),
                word_count: wordCount,
                detected_language: config.language,
                preprocessing_applied: preprocessResponse.preprocessing_applied,
                error: null,
                performance_metrics: {
                    preprocessing_time_ms: preprocessResponse.processing_time_ms,
                    ocr_time_ms: Math.round(ocrTime),
                    total_time_ms: Math.round(totalTime),
                    image_dimensions: preprocessResponse.performance_metrics.image_dimensions,
                    image_size_bytes: preprocessResponse.performance_metrics.image_size_bytes,
                    memory_usage_bytes: preprocessResponse.performance_metrics.memory_usage_bytes
                },
                tesseract_data: {
                    words: data.words.length,
                    lines: data.lines.length,
                    paragraphs: data.paragraphs.length,
                    blocks: data.blocks.length
                }
            };

            console.log('🎉 OCR completed successfully:', {
                text_length: result.text.length,
                confidence: result.confidence.toFixed(3),
                total_time: result.processing_time_ms + 'ms',
                word_count: result.word_count
            });

            return result;

        } catch (error) {
            const totalTime = performance.now() - startTime;
            
            console.error('❌ OCR processing failed:', error);
            
            this.performanceMetrics.errors.push({
                type: 'processing',
                message: error.message,
                timestamp: new Date().toISOString(),
                processing_time: totalTime
            });

            return {
                success: false,
                text: '',
                confidence: 0.0,
                processing_time_ms: Math.round(totalTime),
                word_count: 0,
                detected_language: options.language || 'eng',
                preprocessing_applied: [],
                error: error.message,
                performance_metrics: {
                    preprocessing_time_ms: 0,
                    ocr_time_ms: 0,
                    total_time_ms: Math.round(totalTime),
                    image_dimensions: [0, 0],
                    image_size_bytes: 0,
                    memory_usage_bytes: 0
                }
            };
        }
    }

    /**
     * Calculate confidence score from Tesseract.js data
     */
    calculateConfidence(data) {
        if (!data.words || data.words.length === 0) {
            return 0.0;
        }

        // Calculate average confidence from word-level confidences
        const wordConfidences = data.words
            .filter(word => word.text.trim().length > 0)
            .map(word => word.confidence);

        if (wordConfidences.length === 0) {
            return 0.0;
        }

        const averageConfidence = wordConfidences.reduce((sum, conf) => sum + conf, 0) / wordConfidences.length;
        
        // Normalize to 0-1 range (Tesseract.js returns 0-100)
        return Math.max(0.0, Math.min(1.0, averageConfidence / 100.0));
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(processingTime, success) {
        this.performanceMetrics.totalProcessed++;
        
        // Update average time
        const currentAvg = this.performanceMetrics.averageTime;
        const count = this.performanceMetrics.totalProcessed;
        this.performanceMetrics.averageTime = (currentAvg * (count - 1) + processingTime) / count;
        
        // Update success rate
        const currentSuccesses = Math.round(this.performanceMetrics.successRate * (count - 1));
        const newSuccesses = currentSuccesses + (success ? 1 : 0);
        this.performanceMetrics.successRate = newSuccesses / count;
    }

    /**
     * Get performance statistics
     */
    getPerformanceStats() {
        return {
            ...this.performanceMetrics,
            isInitialized: this.isInitialized,
            supportedLanguages: this.supportedLanguages
        };
    }

    /**
     * Cleanup resources
     */
    async cleanup() {
        if (this.worker) {
            await this.worker.terminate();
            this.worker = null;
            this.isInitialized = false;
            console.log('🧹 Tesseract.js worker terminated');
        }
    }

    /**
     * Process multiple images in batch
     */
    async processBatch(images, options = {}) {
        const results = [];
        const batchStartTime = performance.now();

        console.log(`📦 Processing batch of ${images.length} images`);

        for (let i = 0; i < images.length; i++) {
            console.log(`📄 Processing image ${i + 1}/${images.length}`);
            
            const result = await this.processOCR(images[i], {
                ...options,
                batchIndex: i,
                batchTotal: images.length
            });
            
            results.push(result);

            // Add small delay between images to prevent overwhelming the system
            if (i < images.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        const batchTime = performance.now() - batchStartTime;
        console.log(`✅ Batch processing completed in ${batchTime.toFixed(2)}ms`);

        return {
            results,
            batch_metrics: {
                total_time_ms: Math.round(batchTime),
                images_processed: images.length,
                average_time_per_image: Math.round(batchTime / images.length),
                success_count: results.filter(r => r.success).length,
                error_count: results.filter(r => !r.success).length
            }
        };
    }
}

// Export singleton instance
export const tesseractEngine = new TesseractOCREngine();

// Export class for testing
export { TesseractOCREngine };

// Export utility functions
export const OCRUtils = {
    /**
     * Convert file to base64
     */
    async fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1]; // Remove data URL prefix
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    },

    /**
     * Validate image file
     */
    validateImageFile(file, maxSize = 10 * 1024 * 1024) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff'];
        
        if (!validTypes.includes(file.type)) {
            throw new Error(`Unsupported file type: ${file.type}. Supported types: ${validTypes.join(', ')}`);
        }
        
        if (file.size > maxSize) {
            throw new Error(`File too large: ${(file.size / 1024 / 1024).toFixed(2)}MB. Maximum size: ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
        }
        
        return true;
    },

    /**
     * Get file format from file type
     */
    getImageFormat(file) {
        const typeMap = {
            'image/jpeg': 'jpeg',
            'image/jpg': 'jpeg',
            'image/png': 'png',
            'image/webp': 'webp',
            'image/bmp': 'bmp',
            'image/tiff': 'tiff'
        };
        
        return typeMap[file.type] || 'png';
    }
};
