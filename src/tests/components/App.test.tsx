import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import userEvent from '@testing-library/user-event';
import App from '../../App';

describe('App Component', () => {
  beforeEach(() => {
    // Setup mock responses
    (global as any).mockInvoke.mockImplementation((command: string, args?: any) => {
      switch (command) {
        case 'get_system_info':
          return Promise.resolve({
            ocr_available: true,
            ai_models_available: false,
            supported_image_formats: ['jpg', 'png'],
            supported_document_formats: ['pdf', 'txt'],
            max_file_size_mb: 100,
          });
        case 'initialize_privacy_engine':
          return Promise.resolve();
        case 'initialize_ocr_engine':
          return Promise.resolve();
        case 'scan_file_for_privacy':
          return Promise.resolve({
            file_path: args?.filePath || 'test.txt',
            risk_score: 0.75,
            findings: [
              {
                data_type: 'EmailAddress',
                severity: 'Medium',
                confidence: 0.9,
                detection_method: 'PatternMatching',
                location: { line: 1, column_start: 10, column_end: 25 }
              }
            ],
            processing_time_ms: 150,
            file_size: 1024,
            errors: []
          });
        case 'quick_privacy_assessment':
          return Promise.resolve({
            risk_level: 2,
            risk_description: 'Medium privacy risk detected',
            confidence: 0.8,
            processing_time_ms: 50
          });
        default:
          return Promise.resolve();
      }
    });
  });

  it('renders without crashing', async () => {
    render(<App />);
    expect(document.body).toBeInTheDocument();
  });

  it('contains the main heading', async () => {
    render(<App />);

    await waitFor(() => {
      const heading = screen.getByRole('heading', { name: /PrivacyAI/i });
      expect(heading).toBeInTheDocument();
    });
  });

  it('shows engine initialization status', async () => {
    render(<App />);

    // Initially should show initializing
    expect(screen.getByText(/Initializing.../i)).toBeInTheDocument();

    // After initialization should show ready
    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });
  });

  it('displays system information correctly', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Should have called the system info command
    expect((global as any).mockInvoke).toHaveBeenCalledWith('get_system_info');
  });

  it('handles file selection', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    // Should update the selected path (mocked behavior)
    // In real implementation, this would open a file dialog
  });

  it('handles directory selection', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    const selectDirButton = screen.getByText(/Select Directory/i);
    await user.click(selectDirButton);

    // Should update the selected path (mocked behavior)
  });

  it('performs file scanning', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // First select a file
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    // Then scan the file
    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Should call the scan command
    await waitFor(() => {
      expect((global as any).mockInvoke).toHaveBeenCalledWith('scan_file_for_privacy',
        expect.objectContaining({ filePath: expect.any(String) })
      );
    });
  });

  it('displays scan results', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Select file and scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Should display results
    await waitFor(() => {
      expect(screen.getByText(/Privacy Findings/i)).toBeInTheDocument();
      expect(screen.getByText(/EmailAddress/i)).toBeInTheDocument();
      // Severity is displayed as part of the finding, not as standalone text
    });
  });

  it('shows error messages', async () => {
    // Mock an error response
    (global as any).mockInvoke.mockImplementationOnce(() => {
      return Promise.reject(new Error('Test error'));
    });

    render(<App />);

    // Should show initialization error
    await waitFor(() => {
      expect(screen.getByText(/Failed to initialize/i)).toBeInTheDocument();
    });
  });

  it('handles scan errors gracefully', async () => {
    const user = userEvent.setup();

    // Mock scan error
    (global as any).mockInvoke.mockImplementation((command: string) => {
      if (command === 'scan_file_for_privacy') {
        return Promise.reject(new Error('Scan failed'));
      }
      return Promise.resolve({
        ocr_available: true,
        ai_models_available: false,
        supported_image_formats: ['jpg', 'png'],
        supported_document_formats: ['pdf', 'txt'],
        max_file_size_mb: 100,
      });
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Select file and attempt scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/Scan failed/i)).toBeInTheDocument();
    });
  });

  it('toggles settings panel', async () => {
    const user = userEvent.setup();
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Find and click settings button (it has no accessible name, so use a different selector)
    const settingsButton = screen.getByRole('button', { name: '' });
    await user.click(settingsButton);

    // Settings panel should be visible (implementation dependent)
    // This test would need to be adjusted based on actual settings implementation
  });

  it('displays loading state during scan', async () => {
    const user = userEvent.setup();

    // Mock a delayed response
    (global as any).mockInvoke.mockImplementation((command: string) => {
      if (command === 'scan_file_for_privacy') {
        return new Promise(resolve => {
          setTimeout(() => resolve({
            file_path: 'test.txt',
            risk_score: 0.5,
            findings: [],
            processing_time_ms: 100,
            file_size: 1024,
            errors: []
          }), 100);
        });
      }
      return Promise.resolve({
        ocr_available: true,
        ai_models_available: false,
        supported_image_formats: ['jpg', 'png'],
        supported_document_formats: ['pdf', 'txt'],
        max_file_size_mb: 100,
      });
    });

    render(<App />);

    await waitFor(() => {
      expect(screen.getByText(/Engine Ready/i)).toBeInTheDocument();
    });

    // Navigate to scanner view
    const scannerButton = screen.getByText(/Scanner/i);
    await user.click(scannerButton);

    // Start scan
    const selectFileButton = screen.getByText(/Select File/i);
    await user.click(selectFileButton);

    const scanButton = screen.getByText(/Scan File/i);
    await user.click(scanButton);

    // Should show loading state (there are multiple buttons with "Scanning..." text)
    expect(screen.getAllByText(/Scanning.../i).length).toBeGreaterThan(0);

    // Wait for completion
    await waitFor(() => {
      expect(screen.queryByText(/Scanning.../i)).not.toBeInTheDocument();
    });
  });
});
