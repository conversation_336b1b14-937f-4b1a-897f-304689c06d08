use tauri_appprivacy_ai_lib::privacy::{
    PrivacyDetector, 
    PrivacyDetectionOptions, 
    PrivacyDataType, 
    PrivacySeverity,
    DetectionMethod
};
use tempfile::{TempDir, NamedTempFile};
use std::fs;
use std::io::Write;

#[tokio::test]
async fn test_privacy_detector_creation() {
    let detector = PrivacyDetector::new();
    assert!(detector.is_ok());
}

#[tokio::test]
async fn test_privacy_detector_with_custom_options() {
    let options = PrivacyDetectionOptions {
        enable_ocr: false,
        enable_pattern_matching: true,
        enable_ai_detection: false,
        confidence_threshold: 0.8,
        max_file_size: 50 * 1024 * 1024, // 50MB
        supported_extensions: vec!["txt".to_string(), "pdf".to_string()],
    };
    
    let detector = PrivacyDetector::with_options(options);
    assert!(detector.is_ok());
}

#[tokio::test]
async fn test_privacy_detector_text_file_scanning() {
    let detector = PrivacyDetector::new().unwrap();
    
    // Create a temporary text file with sensitive data
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Contact: <EMAIL>").unwrap();
    writeln!(temp_file, "SSN: ***********").unwrap();
    writeln!(temp_file, "Credit Card: 4111 1111 1111 1111").unwrap();
    
    let result = detector.scan_file(temp_file.path()).await;
    assert!(result.is_ok());
    
    let scan_result = result.unwrap();
    assert!(scan_result.findings.len() >= 2); // Should find email and potentially others
    assert!(scan_result.risk_score > 0.0);
    assert!(scan_result.processing_time_ms > 0);
}

#[tokio::test]
async fn test_privacy_detector_empty_file() {
    let detector = PrivacyDetector::new().unwrap();
    
    // Create an empty temporary file
    let temp_file = NamedTempFile::new().unwrap();
    
    let result = detector.scan_file(temp_file.path()).await;
    assert!(result.is_ok());
    
    let scan_result = result.unwrap();
    assert_eq!(scan_result.findings.len(), 0);
    assert_eq!(scan_result.risk_score, 0.0);
}

#[tokio::test]
async fn test_privacy_detector_unsupported_file() {
    let detector = PrivacyDetector::new().unwrap();
    
    // Create a file with unsupported extension
    let temp_dir = TempDir::new().unwrap();
    let unsupported_file = temp_dir.path().join("test.xyz");
    fs::write(&unsupported_file, "test content").unwrap();
    
    let result = detector.scan_file(&unsupported_file).await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_privacy_detector_file_too_large() {
    let mut options = PrivacyDetectionOptions::default();
    options.max_file_size = 10; // Very small limit
    
    let detector = PrivacyDetector::with_options(options).unwrap();
    
    // Create a file larger than the limit
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "This content is longer than 10 bytes").unwrap();
    
    let result = detector.scan_file(temp_file.path()).await;
    assert!(result.is_ok());
    
    let scan_result = result.unwrap();
    assert!(!scan_result.errors.is_empty());
    assert!(scan_result.errors[0].contains("too large"));
}

#[tokio::test]
async fn test_privacy_detector_nonexistent_file() {
    let detector = PrivacyDetector::new().unwrap();
    
    let result = detector.scan_file("nonexistent_file.txt").await;
    assert!(result.is_err());
}

#[tokio::test]
async fn test_privacy_detector_risk_score_calculation() {
    let detector = PrivacyDetector::new().unwrap();
    
    // Create files with different levels of sensitive data
    let mut low_risk_file = NamedTempFile::new().unwrap();
    writeln!(low_risk_file, "Contact: <EMAIL>").unwrap();
    
    let mut high_risk_file = NamedTempFile::new().unwrap();
    writeln!(high_risk_file, "SSN: ***********").unwrap();
    writeln!(high_risk_file, "Credit Card: 4111 1111 1111 1111").unwrap();
    writeln!(high_risk_file, "Phone: (*************").unwrap();
    
    let low_result = detector.scan_file(low_risk_file.path()).await.unwrap();
    let high_result = detector.scan_file(high_risk_file.path()).await.unwrap();
    
    assert!(high_result.risk_score > low_result.risk_score);
}

#[tokio::test]
async fn test_privacy_detector_confidence_threshold() {
    let mut options = PrivacyDetectionOptions::default();
    options.confidence_threshold = 0.9; // Very high threshold
    
    let detector = PrivacyDetector::with_options(options).unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Maybe an email: test@example").unwrap(); // Incomplete email
    
    let result = detector.scan_file(temp_file.path()).await.unwrap();
    
    // Should filter out low-confidence matches
    let high_confidence_findings: Vec<_> = result.findings.iter()
        .filter(|f| f.confidence >= 0.9)
        .collect();
    
    assert!(high_confidence_findings.len() <= result.findings.len());
}

#[tokio::test]
async fn test_privacy_detector_pattern_matching_disabled() {
    let mut options = PrivacyDetectionOptions::default();
    options.enable_pattern_matching = false;
    
    let detector = PrivacyDetector::with_options(options).unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Email: <EMAIL>").unwrap();
    
    let result = detector.scan_file(temp_file.path()).await.unwrap();
    
    // Should not find pattern-based matches when disabled
    let pattern_findings: Vec<_> = result.findings.iter()
        .filter(|f| f.detection_method == DetectionMethod::PatternMatching)
        .collect();
    
    assert_eq!(pattern_findings.len(), 0);
}

#[tokio::test]
async fn test_privacy_detector_multiple_data_types() {
    let detector = PrivacyDetector::new().unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Personal Information:").unwrap();
    writeln!(temp_file, "Email: <EMAIL>").unwrap();
    writeln!(temp_file, "Phone: (*************").unwrap();
    writeln!(temp_file, "SSN: ***********").unwrap();
    
    let result = detector.scan_file(temp_file.path()).await.unwrap();
    
    // Should detect multiple types
    let data_types: std::collections::HashSet<_> = result.findings.iter()
        .map(|f| &f.data_type)
        .collect();
    
    assert!(data_types.len() >= 2);
    assert!(data_types.contains(&PrivacyDataType::EmailAddress));
}

#[tokio::test]
async fn test_privacy_detector_finding_locations() {
    let detector = PrivacyDetector::new().unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Line 1: Normal text").unwrap();
    writeln!(temp_file, "Line 2: <NAME_EMAIL> here").unwrap();
    writeln!(temp_file, "Line 3: More text").unwrap();
    
    let result = detector.scan_file(temp_file.path()).await.unwrap();
    
    if let Some(finding) = result.findings.first() {
        assert!(finding.location.line.is_some());
        assert_eq!(finding.location.line.unwrap(), 2);
        assert!(finding.location.column_start.is_some());
        assert!(finding.location.column_end.is_some());
    }
}

#[tokio::test]
async fn test_privacy_detector_severity_levels() {
    let detector = PrivacyDetector::new().unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Email: <EMAIL>").unwrap(); // Typically lower severity
    writeln!(temp_file, "SSN: ***********").unwrap(); // Typically higher severity
    
    let result = detector.scan_file(temp_file.path()).await.unwrap();
    
    // Should have different severity levels
    let severities: std::collections::HashSet<_> = result.findings.iter()
        .map(|f| &f.severity)
        .collect();
    
    assert!(severities.len() >= 1);
    
    // SSN should typically be high or critical severity
    let ssn_findings: Vec<_> = result.findings.iter()
        .filter(|f| matches!(f.data_type, PrivacyDataType::SocialSecurityNumber))
        .collect();
    
    if !ssn_findings.is_empty() {
        assert!(matches!(ssn_findings[0].severity, PrivacySeverity::High | PrivacySeverity::Critical));
    }
}

#[tokio::test]
async fn test_privacy_detector_performance_tracking() {
    let detector = PrivacyDetector::new().unwrap();
    
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "Test <NAME_EMAIL>").unwrap();
    
    let start_time = std::time::Instant::now();
    let result = detector.scan_file(temp_file.path()).await.unwrap();
    let actual_time = start_time.elapsed().as_millis() as u64;
    
    // Processing time should be recorded and reasonable
    assert!(result.processing_time_ms > 0);
    assert!(result.processing_time_ms <= actual_time + 100); // Allow some margin
}
