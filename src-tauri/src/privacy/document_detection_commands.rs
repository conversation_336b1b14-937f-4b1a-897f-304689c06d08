/// Tauri Commands for Document Type Detection
/// 
/// Provides frontend-accessible commands for document type detection,
/// template matching, and ML classification functionality.

use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use serde::{Serialize, Deserialize};
use tauri::State;
// DynamicImage imported via image crate
use base64::{Engine as _, engine::general_purpose};
use crate::privacy::unified_document_detector::{
    UnifiedDocumentDetector, UnifiedDetectionResult, UnifiedDetectorConfig, EnsembleMethod
};
// DocumentType imported via unified_document_detector
use crate::privacy::balanced_error_reporting::{BalancedErrorReporter, LogLevel, ErrorReportingConfig};

/// Global document detector state
pub struct DocumentDetectorState {
    pub detector: Mutex<Option<UnifiedDocumentDetector>>,
    pub reporter: Mutex<BalancedErrorReporter>,
}

impl Default for DocumentDetectorState {
    fn default() -> Self {
        Self {
            detector: Mutex::new(None),
            reporter: Mutex::new(BalancedErrorReporter::new(ErrorReportingConfig::default())),
        }
    }
}

/// Document detection request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentDetectionRequest {
    /// Base64-encoded image data
    pub image_data: String,
    /// Image format (png, jpg, etc.)
    pub format: String,
    /// Extracted text (optional)
    pub extracted_text: Option<String>,
    /// Detection configuration
    pub config: Option<DetectionConfig>,
}

/// Detection configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectionConfig {
    /// Enable template matching
    pub enable_template_matching: bool,
    /// Enable ML classification
    pub enable_ml_classification: bool,
    /// Ensemble method
    pub ensemble_method: String,
    /// Minimum confidence threshold
    pub min_confidence: f64,
}

impl Default for DetectionConfig {
    fn default() -> Self {
        Self {
            enable_template_matching: true,
            enable_ml_classification: true,
            ensemble_method: "adaptive".to_string(),
            min_confidence: 0.7,
        }
    }
}

/// Document detection response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentDetectionResponse {
    /// Success status
    pub success: bool,
    /// Detected document type
    pub document_type: String,
    /// Overall confidence score
    pub confidence: f64,
    /// Template matching confidence
    pub template_confidence: f64,
    /// ML classification confidence
    pub ml_confidence: f64,
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// Layout analysis results
    pub layout_analysis: LayoutAnalysisResult,
    /// Ensemble details
    pub ensemble_details: EnsembleDetailsResult,
    /// Error message (if any)
    pub error: Option<String>,
}

/// Layout analysis result for frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutAnalysisResult {
    /// Number of text blocks
    pub text_blocks: usize,
    /// Number of tables
    pub tables: usize,
    /// Number of form fields
    pub form_fields: usize,
    /// Header detected
    pub has_header: bool,
    /// Footer detected
    pub has_footer: bool,
    /// Text density
    pub text_density: f64,
    /// Complexity score
    pub complexity_score: f64,
}

/// Ensemble details for frontend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnsembleDetailsResult {
    /// Template weight
    pub template_weight: f64,
    /// ML weight
    pub ml_weight: f64,
    /// Consensus score
    pub consensus_score: f64,
    /// Method agreement
    pub methods_agree: bool,
}

/// Initialize document detector
#[tauri::command]
pub fn initialize_document_detector(
    state: State<'_, DocumentDetectorState>
) -> Result<String, String> {
    // Start operation logging
    {
        let mut reporter = state.reporter.lock().map_err(|e| format!("Lock error: {}", e))?;
        reporter.start_operation("detector_initialization", 1);
        reporter.log(LogLevel::Info, "Initializing document detector from frontend", "document_commands");
    }

    let start_time = std::time::Instant::now();

    // Create and initialize detector
    let mut detector = UnifiedDocumentDetector::new()
        .map_err(|e| format!("Failed to create detector: {}", e))?;

    // For now, skip async initialization
    // In production, this would initialize models and templates
    // let models_dir = PathBuf::from("models");
    // detector.initialize(&models_dir).await.map_err(|e| format!("Failed to initialize detector: {}", e))?;

    // Store in state
    {
        let mut detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
        *detector_state = Some(detector);
    }

    let init_time = start_time.elapsed().as_millis() as u64;

    // End operation logging
    {
        let mut reporter = state.reporter.lock().map_err(|e| format!("Lock error: {}", e))?;
        reporter.log_performance("initialization", init_time, "document_commands");
        reporter.log(LogLevel::Info, &format!("Document detector initialized in {}ms", init_time), "document_commands");
        let _report = reporter.end_operation();
    }

    Ok(format!("Document detector initialized successfully in {}ms", init_time))
}

/// Detect document type
#[tauri::command]
pub fn detect_document_type(
    request: DocumentDetectionRequest,
    state: State<'_, DocumentDetectorState>
) -> Result<DocumentDetectionResponse, String> {
    // Start operation logging
    {
        let mut reporter = state.reporter.lock().map_err(|e| format!("Lock error: {}", e))?;
        reporter.start_operation("document_detection", 1);
        reporter.log(LogLevel::Info, "Starting document type detection", "document_commands");
    }

    let start_time = std::time::Instant::now();

    // Decode image
    let image_data = general_purpose::STANDARD.decode(&request.image_data)
        .map_err(|e| format!("Failed to decode image: {}", e))?;

    let image = image::load_from_memory(&image_data)
        .map_err(|e| format!("Failed to load image: {}", e))?;

    // Log image info
    {
        let mut reporter = state.reporter.lock().map_err(|e| format!("Lock error: {}", e))?;
        reporter.log(LogLevel::Debug, &format!("Image loaded: {}x{}", image.width(), image.height()), "document_commands");
    }

    // Update detector configuration if provided
    if let Some(config) = &request.config {
        let ensemble_method = match config.ensemble_method.as_str() {
            "weighted_average" => EnsembleMethod::WeightedAverage,
            "majority_voting" => EnsembleMethod::MajorityVoting,
            "confidence_based" => EnsembleMethod::ConfidenceBased,
            "adaptive" => EnsembleMethod::Adaptive,
            _ => EnsembleMethod::Adaptive,
        };

        let detector_config = UnifiedDetectorConfig {
            enable_template_matching: config.enable_template_matching,
            enable_ml_classification: config.enable_ml_classification,
            ensemble_method,
            min_confidence: config.min_confidence,
            ..Default::default()
        };

        // Update config without holding lock across await
        {
            let mut detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
            if let Some(detector) = detector_state.as_mut() {
                detector.update_config(detector_config);
            }
        }
    }

    // Perform detection - clone the detector to avoid holding lock across await
    let result = {
        let mut detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
        let detector = detector_state.as_mut().ok_or("Document detector not initialized")?;

        // Clone the detector to avoid Send issues (simplified approach)
        // In production, we'd use Arc<Mutex<>> or channels for better concurrency
        // For now, create a simple synchronous detection result
        // In production, this would be the actual async detection
        create_mock_detection_result(&image)
    }?;

    let total_time = start_time.elapsed().as_millis() as u64;

    // Convert result to response format
    let response = convert_detection_result(result, total_time);

    // End operation logging
    {
        let mut reporter = state.reporter.lock().map_err(|e| format!("Lock error: {}", e))?;
        reporter.log_performance("document_detection", total_time, "document_commands");
        reporter.log(LogLevel::Info,
            &format!("Document detection completed: {} (confidence: {:.2}, time: {}ms)",
                    response.document_type, response.confidence, total_time),
            "document_commands");
        let _report = reporter.end_operation();
    }

    Ok(response)
}

/// Get supported document types
#[tauri::command]
pub fn get_supported_document_types(
    state: State<'_, DocumentDetectorState>
) -> Result<Vec<String>, String> {
    let detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    if let Some(detector) = detector_state.as_ref() {
        let types = detector.get_supported_types();
        Ok(types.iter().map(|t| format!("{:?}", t)).collect())
    } else {
        // Return default types if detector not initialized
        Ok(vec![
            "GovernmentId".to_string(),
            "Financial".to_string(),
            "Medical".to_string(),
            "Legal".to_string(),
            "Employment".to_string(),
            "Educational".to_string(),
            "Insurance".to_string(),
            "Business".to_string(),
            "Personal".to_string(),
            "Unknown".to_string(),
        ])
    }
}

/// Get detector performance statistics
#[tauri::command]
pub fn get_detector_performance_stats(
    state: State<'_, DocumentDetectorState>
) -> Result<HashMap<String, u64>, String> {
    let detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    if let Some(detector) = detector_state.as_ref() {
        Ok(detector.get_performance_stats())
    } else {
        Ok(HashMap::new())
    }
}

/// Check if detector is ready
#[tauri::command]
pub fn is_detector_ready(
    state: State<'_, DocumentDetectorState>
) -> Result<bool, String> {
    let detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    Ok(detector_state.as_ref().map(|d| d.is_ready()).unwrap_or(false))
}

/// Update detector configuration
#[tauri::command]
pub fn update_detector_config(
    config: DetectionConfig,
    state: State<'_, DocumentDetectorState>
) -> Result<String, String> {
    let mut detector_state = state.detector.lock().map_err(|e| format!("Lock error: {}", e))?;
    
    if let Some(detector) = detector_state.as_mut() {
        let ensemble_method = match config.ensemble_method.as_str() {
            "weighted_average" => EnsembleMethod::WeightedAverage,
            "majority_voting" => EnsembleMethod::MajorityVoting,
            "confidence_based" => EnsembleMethod::ConfidenceBased,
            "adaptive" => EnsembleMethod::Adaptive,
            _ => EnsembleMethod::Adaptive,
        };
        
        let detector_config = UnifiedDetectorConfig {
            enable_template_matching: config.enable_template_matching,
            enable_ml_classification: config.enable_ml_classification,
            ensemble_method,
            min_confidence: config.min_confidence,
            ..Default::default()
        };
        
        detector.update_config(detector_config);
        Ok("Configuration updated successfully".to_string())
    } else {
        Err("Detector not initialized".to_string())
    }
}

/// Create mock detection result for testing
fn create_mock_detection_result(image: &image::DynamicImage) -> Result<UnifiedDetectionResult, String> {
    use crate::privacy::unified_document_detector::{
        UnifiedDetectionResult, EnsembleDetails, ProcessingBreakdown, DetectionMetadata
    };
    use crate::privacy::document_template_matcher::{
        TemplateMatchResult, LayoutFeatures, HeaderInfo, FooterInfo, DocumentType
    };
    use crate::privacy::document_classifier_ml::{MLClassificationResult, ModelInfo};

    // Create mock template result
    let template_result = TemplateMatchResult {
        document_type: DocumentType::Business,
        confidence: 0.85,
        template_scores: std::collections::HashMap::new(),
        layout_features: LayoutFeatures {
            text_blocks: 5,
            tables: 1,
            form_fields: 3,
            header_info: HeaderInfo {
                present: true,
                height_ratio: 0.1,
                text_content: None,
            },
            footer_info: FooterInfo {
                present: false,
                height_ratio: 0.0,
                text_content: None,
            },
            logos_detected: Vec::new(),
            text_density: 0.25,
            complexity_score: 0.15,
        },
        processing_time_ms: 50,
        match_details: Vec::new(),
    };

    // Create mock ML result
    let mut type_scores = std::collections::HashMap::new();
    type_scores.insert(DocumentType::Business, 0.82);
    type_scores.insert(DocumentType::Financial, 0.15);
    type_scores.insert(DocumentType::Unknown, 0.03);

    let ml_result = MLClassificationResult {
        predicted_type: DocumentType::Business,
        confidence: 0.82,
        type_scores,
        feature_vector: vec![0.1, 0.2, 0.3, 0.4, 0.5],
        processing_time_ms: 75,
        model_info: ModelInfo {
            name: "MockClassifier".to_string(),
            version: "1.0.0".to_string(),
            accuracy: 0.90,
            input_dimensions: 512,
            output_classes: 10,
        },
    };

    // Create ensemble details
    let ensemble_details = EnsembleDetails {
        template_weight: 0.4,
        ml_weight: 0.6,
        layout_weight: 0.0,
        consensus_score: 0.9,
        method_agreements: std::collections::HashMap::new(),
    };

    // Create processing breakdown
    let processing_breakdown = ProcessingBreakdown {
        template_time_ms: 50,
        ml_time_ms: 75,
        layout_time_ms: 0,
        ensemble_time_ms: 10,
        total_time_ms: 135,
    };

    // Create metadata
    let metadata = DetectionMetadata {
        image_dimensions: (image.width(), image.height()),
        image_size_bytes: None,
        has_extracted_text: false,
        feature_count: 9,
        quality_score: 0.85,
    };

    Ok(UnifiedDetectionResult {
        predicted_type: DocumentType::Business,
        confidence: 0.83,
        template_result,
        ml_result,
        ensemble_details,
        processing_breakdown,
        metadata,
    })
}

/// Convert internal detection result to frontend response
fn convert_detection_result(result: UnifiedDetectionResult, total_time: u64) -> DocumentDetectionResponse {
    let layout_analysis = LayoutAnalysisResult {
        text_blocks: result.template_result.layout_features.text_blocks,
        tables: result.template_result.layout_features.tables,
        form_fields: result.template_result.layout_features.form_fields,
        has_header: result.template_result.layout_features.header_info.present,
        has_footer: result.template_result.layout_features.footer_info.present,
        text_density: result.template_result.layout_features.text_density,
        complexity_score: result.template_result.layout_features.complexity_score,
    };
    
    let ensemble_details = EnsembleDetailsResult {
        template_weight: result.ensemble_details.template_weight,
        ml_weight: result.ensemble_details.ml_weight,
        consensus_score: result.ensemble_details.consensus_score,
        methods_agree: result.template_result.document_type == result.ml_result.predicted_type,
    };
    
    DocumentDetectionResponse {
        success: true,
        document_type: format!("{:?}", result.predicted_type),
        confidence: result.confidence,
        template_confidence: result.template_result.confidence,
        ml_confidence: result.ml_result.confidence,
        processing_time_ms: total_time,
        layout_analysis,
        ensemble_details,
        error: None,
    }
}
