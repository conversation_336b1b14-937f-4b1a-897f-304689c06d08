//! PrivacyAI - AI-Powered Privacy Scanner
//!
//! This application provides comprehensive privacy detection capabilities
//! including OCR text extraction, pattern matching, and AI-based visual detection.

// Core modules
pub mod core;
pub mod security;
pub mod privacy;
pub mod analytics;
pub mod unified;
pub mod commands;

use commands::AppState;
use analytics::commands::AnalyticsState;
use security::{SecureFileOperations, PrivacyWorkflow, SecureOperationsConfig, SensitiveDataDetector};
use unified::commands::ScanConfigState;
use tauri::Manager;
use security::secure_operations_commands::{SecureOperationsState, PrivacyWorkflowState};
use std::sync::Arc;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .setup(|app| {
            // Initialize ScanConfigState with app handle
            let scan_config_state = ScanConfigState::new(app.handle().clone());
            app.manage(scan_config_state);
            Ok(())
        })
        .manage(AppState::default())
        .manage(analytics::commands::AnalyticsState::default())
        .manage(privacy::document_detection_commands::DocumentDetectorState::default())
        .manage(Arc::new(SecureFileOperations::new(SecureOperationsConfig::default()).unwrap()) as SecureOperationsState)
        .manage(Arc::new(PrivacyWorkflow::new(SecureOperationsConfig::default(), SensitiveDataDetector::default()).unwrap()) as PrivacyWorkflowState)
        .invoke_handler(tauri::generate_handler![
            commands::greet,
            commands::initialize_privacy_engine,
            commands::initialize_ocr_engine,
            commands::extract_text_from_image,
            commands::extract_text_from_pdf,
            // OCR Bridge commands
            privacy::ocr_bridge::process_ocr,
            privacy::ocr_bridge::configure_ocr,
            privacy::ocr_bridge::get_ocr_performance_metrics,
            commands::scan_file_for_privacy,
            commands::scan_directory_for_privacy,
            commands::get_privacy_detection_config,
            commands::update_privacy_detection_config,
            commands::is_privacy_engine_initialized,
            commands::is_ocr_engine_initialized,
            commands::get_ocr_availability_details,
            commands::get_system_info,
            commands::test_pattern_detection,
            commands::quick_privacy_assessment,
            commands::progressive_privacy_assessment,
            commands::select_file,
            commands::select_files,
            commands::select_directory,
            // Analytics commands
            analytics::commands::get_analytics_dashboard,
            analytics::commands::record_scan_result,
            analytics::commands::get_performance_metrics,
            analytics::commands::get_performance_alerts,
            analytics::commands::get_performance_state,
            analytics::commands::get_risk_summary,
            analytics::commands::get_compliance_status,
            analytics::commands::get_scan_statistics,
            analytics::commands::get_performance_trends,
            analytics::commands::update_analytics_config,
            analytics::commands::export_analytics_data,
            analytics::commands::clear_analytics_data,
            // Auto-scaling commands
            analytics::commands::enable_auto_scaling,
            analytics::commands::disable_auto_scaling,
            analytics::commands::update_scaling_config,
            analytics::commands::adjust_thread_pool_size,
            analytics::commands::get_resource_metrics,
            // OCR commands
            privacy::ocr_bridge::process_ocr,
            privacy::ocr_bridge::configure_ocr,
            // Document type detection commands
            privacy::document_detection_commands::initialize_document_detector,
            privacy::document_detection_commands::detect_document_type,
            privacy::document_detection_commands::get_supported_document_types,
            privacy::document_detection_commands::get_detector_performance_stats,
            privacy::document_detection_commands::is_detector_ready,
            privacy::document_detection_commands::update_detector_config,
            // Secure operations commands
            security::secure_operations_commands::initialize_secure_operations,
            security::secure_operations_commands::create_secure_archive,
            security::secure_operations_commands::secure_delete_files,
            security::secure_operations_commands::get_privacy_summary,
            security::secure_operations_commands::scan_and_secure_archive,
            security::secure_operations_commands::scan_and_secure_delete,
            security::secure_operations_commands::get_default_secure_config,
            security::secure_operations_commands::validate_secure_config,
            security::secure_operations_commands::initialize_privacy_workflow,
            // Unified configuration commands
            unified::commands::update_scan_config,
            unified::commands::get_current_scan_config,
            unified::commands::save_custom_config,
            unified::commands::load_custom_config,
            unified::commands::get_custom_config_names,
            unified::commands::delete_custom_config,
            unified::commands::export_config_json,
            unified::commands::import_config_json,
            // Enhanced scanning command (97% accuracy mode)
            unified::commands::scan_file_enhanced,
            // File preview commands
            commands::get_file_preview_info,
            commands::get_file_preview_content,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
