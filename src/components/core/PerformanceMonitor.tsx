/**
 * Performance Monitor Component
 * Real-time performance tracking and optimization recommendations
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Monitor, Zap, AlertTriangle, CheckCircle, TrendingUp, TrendingDown, Activity, Database, Cpu, MemoryStick } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { performanceOptimizer } from '../../utils/performanceOptimization';
import useMobileOptimization from '../../hooks/useMobileOptimization';
import useAccessibility from '../../hooks/useAccessibility';
import './PerformanceMonitor.css';

interface PerformanceMetrics {
    loadTime: number;
    renderTime: number;
    bundleSize: number;
    memoryUsage: number;
    cacheHitRate: number;
    fps: number;
    networkLatency: number;
    // Backend metrics
    avgScanTime: number;
    peakMemoryUsage: number;
    throughput: number;
    errorRate: number;
    cpuUsage: number;
}

interface BackendPerformanceData {
    avg_scan_time_ms: number;
    peak_memory_usage_mb: number;
    avg_memory_usage_mb: number;
    throughput_files_per_minute: number;
    cache_hit_rate_percent: number;
    error_rate_percent: number;
    performance_trend: string;
    bottlenecks: Array<{
        component: string;
        impact_score: number;
        description: string;
        recommendation: string;
    }>;
}

interface OptimizationSuggestion {
    type: 'critical' | 'warning' | 'info';
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    action: string;
}

const PerformanceMonitor: React.FC = () => {
    const [metrics, setMetrics] = useState<PerformanceMetrics>({
        loadTime: 0,
        renderTime: 0,
        bundleSize: 0,
        memoryUsage: 0,
        cacheHitRate: 0,
        fps: 60,
        networkLatency: 0,
        // Backend metrics
        avgScanTime: 0,
        peakMemoryUsage: 0,
        throughput: 0,
        errorRate: 0,
        cpuUsage: 0,
    });

    const [backendMetrics, setBackendMetrics] = useState<BackendPerformanceData | null>(null);
    const [performanceHistory, setPerformanceHistory] = useState<PerformanceMetrics[]>([]);
    const performanceHistoryRef = useRef<PerformanceMetrics[]>([]);

    const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
    const [isMonitoring, setIsMonitoring] = useState(false);
    const [showDetails, setShowDetails] = useState(false);

    const { isMobile, hasLimitedResources, adaptiveConfig } = useMobileOptimization();
    const { announce } = useAccessibility();

    // Performance monitoring
    useEffect(() => {
        if (!isMonitoring) return;

        const monitoringInterval = setInterval(() => {
            updateMetrics();
            fetchBackendMetrics();
            generateSuggestions();
        }, 2000);

        return () => clearInterval(monitoringInterval);
    }, [isMonitoring]);

    // Fetch backend performance metrics
    const fetchBackendMetrics = useCallback(async () => {
        try {
            const backendData = await invoke<BackendPerformanceData>('get_performance_metrics');
            setBackendMetrics(backendData);
        } catch (error) {
            console.warn('Failed to fetch backend performance metrics:', error);
        }
    }, []);

    // Update performance metrics
    const updateMetrics = useCallback(() => {
        const performanceData = performanceOptimizer.getPerformanceMetrics();

        // Get memory usage if available
        const memoryInfo = (performance as any).memory;
        const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : 0;

        // Calculate FPS
        const fps = calculateFPS();

        // Estimate network latency
        const networkLatency = estimateNetworkLatency();

        // Estimate CPU usage (simplified)
        const cpuUsage = estimateCpuUsage();

        const newMetrics: PerformanceMetrics = {
            loadTime: performanceData.averageLoadTime,
            renderTime: performanceOptimizer.measureRenderTime('PerformanceMonitor', () => 0),
            bundleSize: performanceData.totalBundleSize,
            memoryUsage,
            cacheHitRate: performanceData.cacheHitRate * 100,
            fps,
            networkLatency,
            // Backend metrics (will be updated from backend data)
            avgScanTime: backendMetrics?.avg_scan_time_ms || 0,
            peakMemoryUsage: backendMetrics?.peak_memory_usage_mb || 0,
            throughput: backendMetrics?.throughput_files_per_minute || 0,
            errorRate: backendMetrics?.error_rate_percent || 0,
            cpuUsage,
        };

        setMetrics(newMetrics);

        // Update performance history for trend analysis
        performanceHistoryRef.current = [...performanceHistoryRef.current, newMetrics].slice(-20); // Keep last 20 samples
        setPerformanceHistory(performanceHistoryRef.current);
    }, [backendMetrics]);

    // Calculate FPS using requestAnimationFrame
    const calculateFPS = useCallback(() => {
        let frames = 0;
        let lastTime = performance.now();
        
        const countFrames = () => {
            frames++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                lastTime = currentTime;
                frames = 0;
                return fps;
            }
            
            requestAnimationFrame(countFrames);
            return 60; // Default assumption
        };
        
        return countFrames();
    }, []);

    // Estimate network latency
    const estimateNetworkLatency = useCallback(() => {
        const connection = (navigator as any).connection;
        if (connection) {
            return connection.rtt || 0;
        }
        return 0;
    }, []);

    // Estimate CPU usage (simplified)
    const estimateCpuUsage = useCallback(() => {
        // Simplified CPU usage estimation based on frame rate and processing
        const baseUsage = Math.max(0, 100 - metrics.fps); // Lower FPS = higher CPU usage
        const memoryFactor = (metrics.memoryUsage / 100) * 20; // Memory pressure affects CPU
        return Math.min(Math.max(baseUsage + memoryFactor, 0), 100);
    }, [metrics.fps, metrics.memoryUsage]);

    // Generate optimization suggestions
    const generateSuggestions = useCallback(() => {
        const newSuggestions: OptimizationSuggestion[] = [];

        // Bundle size suggestions
        if (metrics.bundleSize > 1000) {
            newSuggestions.push({
                type: 'warning',
                title: 'Large Bundle Size',
                description: `Bundle size is ${metrics.bundleSize.toFixed(1)}KB. Consider code splitting.`,
                impact: 'high',
                action: 'Implement lazy loading for non-critical components'
            });
        }

        // Backend performance suggestions
        if (backendMetrics?.bottlenecks) {
            backendMetrics.bottlenecks.forEach(bottleneck => {
                newSuggestions.push({
                    type: bottleneck.impact_score > 0.7 ? 'critical' : 'warning',
                    title: `Backend Bottleneck: ${bottleneck.component}`,
                    description: bottleneck.description,
                    impact: bottleneck.impact_score > 0.7 ? 'high' : 'medium',
                    action: bottleneck.recommendation
                });
            });
        }

        // Scan performance suggestions
        if (metrics.avgScanTime > 1000) {
            newSuggestions.push({
                type: 'warning',
                title: 'Slow Scan Performance',
                description: `Average scan time is ${metrics.avgScanTime}ms. Consider optimizing scan profiles.`,
                impact: 'medium',
                action: 'Use faster scan profiles for routine checks'
            });
        }

        // Memory usage suggestions
        if (metrics.memoryUsage > 100) {
            newSuggestions.push({
                type: 'critical',
                title: 'High Memory Usage',
                description: `Memory usage is ${metrics.memoryUsage.toFixed(1)}MB. Risk of performance degradation.`,
                impact: 'high',
                action: 'Clear caches and optimize component lifecycle'
            });
        }

        // FPS suggestions
        if (metrics.fps < 30) {
            newSuggestions.push({
                type: 'critical',
                title: 'Low Frame Rate',
                description: `Frame rate is ${metrics.fps}fps. User experience may be affected.`,
                impact: 'high',
                action: 'Reduce animation complexity or disable animations'
            });
        }

        // Cache hit rate suggestions
        if (metrics.cacheHitRate < 50) {
            newSuggestions.push({
                type: 'info',
                title: 'Low Cache Hit Rate',
                description: `Cache hit rate is ${metrics.cacheHitRate.toFixed(1)}%. Performance could be improved.`,
                impact: 'medium',
                action: 'Enable caching for frequently accessed resources'
            });
        }

        // Mobile-specific suggestions
        if (isMobile && hasLimitedResources) {
            newSuggestions.push({
                type: 'info',
                title: 'Mobile Optimization',
                description: 'Device has limited resources. Mobile optimizations are active.',
                impact: 'medium',
                action: 'Consider reducing concurrent operations'
            });
        }

        // Network latency suggestions
        if (metrics.networkLatency > 500) {
            newSuggestions.push({
                type: 'warning',
                title: 'High Network Latency',
                description: `Network latency is ${metrics.networkLatency}ms. Consider offline-first approach.`,
                impact: 'medium',
                action: 'Enable aggressive caching and offline functionality'
            });
        }

        setSuggestions(newSuggestions);
    }, [metrics, isMobile, hasLimitedResources]);

    // Toggle monitoring
    const toggleMonitoring = useCallback(() => {
        setIsMonitoring(prev => {
            const newState = !prev;
            announce(newState ? 'Performance monitoring started' : 'Performance monitoring stopped', { priority: 'polite' });
            return newState;
        });
    }, [announce]);

    // Get metric status
    const getMetricStatus = (value: number, thresholds: { good: number; warning: number }) => {
        if (value <= thresholds.good) return 'good';
        if (value <= thresholds.warning) return 'warning';
        return 'critical';
    };

    // Format metric value
    const formatMetric = (value: number, unit: string) => {
        if (value < 1 && unit !== 'fps' && unit !== '%') {
            return `${(value * 1000).toFixed(0)}m${unit}`;
        }
        return `${value.toFixed(1)}${unit}`;
    };

    const MetricCard: React.FC<{
        title: string;
        value: number;
        unit: string;
        thresholds: { good: number; warning: number };
        icon: React.ReactNode;
    }> = ({ title, value, unit, thresholds, icon }) => {
        const status = getMetricStatus(value, thresholds);
        
        return (
            <div className={`metric-card ${status}`}>
                <div className="metric-header">
                    <div className="metric-icon">{icon}</div>
                    <div className="metric-title">{title}</div>
                </div>
                <div className="metric-value">
                    {formatMetric(value, unit)}
                </div>
                <div className="metric-status">
                    {status === 'good' && <CheckCircle size={16} />}
                    {status === 'warning' && <AlertTriangle size={16} />}
                    {status === 'critical' && <TrendingDown size={16} />}
                    <span className="status-text">{status}</span>
                </div>
            </div>
        );
    };

    return (
        <div className={`performance-monitor ${isMobile ? 'mobile' : ''}`}>
            <div className="monitor-header">
                <div className="header-content">
                    <Monitor className="header-icon" />
                    <div>
                        <h3>Performance Monitor</h3>
                        <p>Real-time performance tracking and optimization ({performanceHistory.length} data points)</p>
                    </div>
                </div>
                
                <div className="monitor-controls">
                    <button
                        className={`monitor-toggle ${isMonitoring ? 'active' : ''}`}
                        onClick={toggleMonitoring}
                        aria-label={isMonitoring ? 'Stop monitoring' : 'Start monitoring'}
                    >
                        {isMonitoring ? 'Stop' : 'Start'} Monitoring
                    </button>
                    
                    <button
                        className="details-toggle"
                        onClick={() => setShowDetails(!showDetails)}
                        aria-label={showDetails ? 'Hide details' : 'Show details'}
                    >
                        {showDetails ? 'Hide' : 'Show'} Details
                    </button>
                </div>
            </div>

            {isMonitoring && (
                <>
                    <div className="metrics-grid">
                        <MetricCard
                            title="Load Time"
                            value={metrics.loadTime}
                            unit="ms"
                            thresholds={{ good: 1000, warning: 3000 }}
                            icon={<Zap size={20} />}
                        />
                        
                        <MetricCard
                            title="Bundle Size"
                            value={metrics.bundleSize}
                            unit="KB"
                            thresholds={{ good: 500, warning: 1000 }}
                            icon={<TrendingUp size={20} />}
                        />
                        
                        <MetricCard
                            title="Memory Usage"
                            value={metrics.memoryUsage}
                            unit="MB"
                            thresholds={{ good: 50, warning: 100 }}
                            icon={<Monitor size={20} />}
                        />
                        
                        <MetricCard
                            title="Frame Rate"
                            value={metrics.fps}
                            unit="fps"
                            thresholds={{ good: 55, warning: 30 }}
                            icon={<TrendingUp size={20} />}
                        />
                        
                        <MetricCard
                            title="Cache Hit Rate"
                            value={metrics.cacheHitRate}
                            unit="%"
                            thresholds={{ good: 80, warning: 50 }}
                            icon={<CheckCircle size={20} />}
                        />
                        
                        {metrics.networkLatency > 0 && (
                            <MetricCard
                                title="Network Latency"
                                value={metrics.networkLatency}
                                unit="ms"
                                thresholds={{ good: 100, warning: 500 }}
                                icon={<TrendingDown size={20} />}
                            />
                        )}
                    </div>

                    {/* Backend Performance Metrics */}
                    {backendMetrics && (
                        <div className="backend-metrics-section">
                            <h4>Backend Performance</h4>
                            <div className="metrics-grid">
                                <MetricCard
                                    title="Avg Scan Time"
                                    value={metrics.avgScanTime}
                                    unit="ms"
                                    thresholds={{ good: 500, warning: 1000 }}
                                    icon={<Activity size={20} />}
                                />

                                <MetricCard
                                    title="Peak Memory"
                                    value={metrics.peakMemoryUsage}
                                    unit="MB"
                                    thresholds={{ good: 100, warning: 200 }}
                                    icon={<MemoryStick size={20} />}
                                />

                                <MetricCard
                                    title="Throughput"
                                    value={metrics.throughput}
                                    unit="files/min"
                                    thresholds={{ good: 10, warning: 5 }}
                                    icon={<TrendingUp size={20} />}
                                />

                                <MetricCard
                                    title="Error Rate"
                                    value={metrics.errorRate}
                                    unit="%"
                                    thresholds={{ good: 1, warning: 5 }}
                                    icon={<AlertTriangle size={20} />}
                                />

                                <MetricCard
                                    title="CPU Usage"
                                    value={metrics.cpuUsage}
                                    unit="%"
                                    thresholds={{ good: 50, warning: 80 }}
                                    icon={<Cpu size={20} />}
                                />

                                <MetricCard
                                    title="Backend Cache"
                                    value={backendMetrics.cache_hit_rate_percent}
                                    unit="%"
                                    thresholds={{ good: 90, warning: 70 }}
                                    icon={<Database size={20} />}
                                />
                            </div>
                        </div>
                    )}

                    {suggestions.length > 0 && (
                        <div className="suggestions-section">
                            <h4>Optimization Suggestions</h4>
                            <div className="suggestions-list">
                                {suggestions.map((suggestion, index) => (
                                    <div key={index} className={`suggestion ${suggestion.type}`}>
                                        <div className="suggestion-header">
                                            <div className="suggestion-title">{suggestion.title}</div>
                                            <div className={`suggestion-impact ${suggestion.impact}`}>
                                                {suggestion.impact} impact
                                            </div>
                                        </div>
                                        <div className="suggestion-description">
                                            {suggestion.description}
                                        </div>
                                        <div className="suggestion-action">
                                            <strong>Action:</strong> {suggestion.action}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {showDetails && (
                        <div className="details-section">
                            <h4>Detailed Metrics</h4>
                            <div className="details-grid">
                                <div className="detail-item">
                                    <span>Device Type:</span>
                                    <span>{isMobile ? 'Mobile' : 'Desktop'}</span>
                                </div>
                                <div className="detail-item">
                                    <span>Limited Resources:</span>
                                    <span>{hasLimitedResources ? 'Yes' : 'No'}</span>
                                </div>
                                <div className="detail-item">
                                    <span>Max Concurrent Files:</span>
                                    <span>{adaptiveConfig.maxConcurrentFiles}</span>
                                </div>
                                <div className="detail-item">
                                    <span>Animations Enabled:</span>
                                    <span>{adaptiveConfig.enableAnimations ? 'Yes' : 'No'}</span>
                                </div>
                                <div className="detail-item">
                                    <span>Image Quality:</span>
                                    <span>{adaptiveConfig.imageQuality}</span>
                                </div>
                                <div className="detail-item">
                                    <span>Lazy Loading:</span>
                                    <span>{adaptiveConfig.enableLazyLoading ? 'Enabled' : 'Disabled'}</span>
                                </div>
                            </div>
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default PerformanceMonitor;
