import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import * as Progress from '@radix-ui/react-progress';
import { Upload, File, X, Play, Pause, RotateCcw, CheckCircle, AlertCircle } from 'lucide-react';
import './EnhancedFileDropZone.css';

interface FileWithProgress {
    file: File;
    id: string;
    progress: number;
    status: 'pending' | 'processing' | 'completed' | 'error';
    results?: any;
    error?: string;
}

interface BatchProcessingStats {
    total: number;
    completed: number;
    failed: number;
    inProgress: number;
    pending: number;
}

interface EnhancedFileDropZoneProps {
    onFilesProcessed?: (results: any[]) => void;
    onBatchComplete?: (stats: BatchProcessingStats) => void;
    maxFiles?: number;
    maxFileSize?: number; // in bytes
    acceptedFileTypes?: string[];
}

const EnhancedFileDropZone: React.FC<EnhancedFileDropZoneProps> = ({
    onFilesProcessed,
    onBatchComplete,
    maxFiles = 50,
    maxFileSize = 100 * 1024 * 1024, // 100MB
    acceptedFileTypes = ['.pdf', '.docx', '.txt', '.xlsx', '.png', '.jpg', '.jpeg']
}) => {
    const [files, setFiles] = useState<FileWithProgress[]>([]);
    const [isProcessing, setIsProcessing] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [batchStats, setBatchStats] = useState<BatchProcessingStats>({
        total: 0,
        completed: 0,
        failed: 0,
        inProgress: 0,
        pending: 0
    });

    const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
        // Handle rejected files
        if (rejectedFiles.length > 0) {
            console.warn('Some files were rejected:', rejectedFiles);
        }

        // Add accepted files to the queue
        const newFiles: FileWithProgress[] = acceptedFiles.map(file => ({
            file,
            id: `${file.name}-${Date.now()}-${Math.random()}`,
            progress: 0,
            status: 'pending'
        }));

        setFiles(prev => {
            const combined = [...prev, ...newFiles];
            if (combined.length > maxFiles) {
                return combined.slice(0, maxFiles);
            }
            return combined;
        });

        updateBatchStats([...files, ...newFiles]);
    }, [files, maxFiles]);

    const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
        onDrop,
        accept: {
            'application/pdf': ['.pdf'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
            'text/plain': ['.txt'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'image/png': ['.png'],
            'image/jpeg': ['.jpg', '.jpeg']
        },
        maxSize: maxFileSize,
        maxFiles: maxFiles,
        disabled: isProcessing
    });

    const updateBatchStats = (fileList: FileWithProgress[]) => {
        const stats: BatchProcessingStats = {
            total: fileList.length,
            completed: fileList.filter(f => f.status === 'completed').length,
            failed: fileList.filter(f => f.status === 'error').length,
            inProgress: fileList.filter(f => f.status === 'processing').length,
            pending: fileList.filter(f => f.status === 'pending').length
        };
        setBatchStats(stats);
    };

    const simulateFileProcessing = async (fileItem: FileWithProgress): Promise<any> => {
        // Simulate processing time based on file size
        const processingTime = Math.min(Math.max(fileItem.file.size / (1024 * 1024) * 1000, 1000), 10000);
        const steps = 20;
        const stepTime = processingTime / steps;

        for (let i = 0; i <= steps; i++) {
            if (isPaused) {
                await new Promise(resolve => {
                    const checkPause = () => {
                        if (!isPaused) resolve(undefined);
                        else setTimeout(checkPause, 100);
                    };
                    checkPause();
                });
            }

            await new Promise(resolve => setTimeout(resolve, stepTime));
            
            setFiles(prev => prev.map(f => 
                f.id === fileItem.id 
                    ? { ...f, progress: (i / steps) * 100 }
                    : f
            ));
        }

        // Simulate random results
        const hasFindings = Math.random() > 0.3;
        return {
            file_path: fileItem.file.name,
            risk_score: hasFindings ? Math.random() * 10 : Math.random() * 3,
            findings: hasFindings ? [
                {
                    data_type: 'email',
                    confidence: 0.95,
                    value: '<EMAIL>'
                }
            ] : [],
            processing_time_ms: processingTime,
            file_size: fileItem.file.size
        };
    };

    const processFiles = async () => {
        setIsProcessing(true);
        setIsPaused(false);

        const pendingFiles = files.filter(f => f.status === 'pending');
        const results: any[] = [];

        for (const fileItem of pendingFiles) {
            if (isPaused) break;

            // Update status to processing
            setFiles(prev => prev.map(f => 
                f.id === fileItem.id 
                    ? { ...f, status: 'processing' }
                    : f
            ));
            updateBatchStats(files);

            try {
                const result = await simulateFileProcessing(fileItem);
                
                // Update status to completed
                setFiles(prev => prev.map(f => 
                    f.id === fileItem.id 
                        ? { ...f, status: 'completed', results: result }
                        : f
                ));
                
                results.push(result);
            } catch (error) {
                // Update status to error
                setFiles(prev => prev.map(f => 
                    f.id === fileItem.id 
                        ? { ...f, status: 'error', error: error instanceof Error ? error.message : 'Processing failed' }
                        : f
                ));
            }

            updateBatchStats(files);
        }

        setIsProcessing(false);
        
        if (onFilesProcessed) {
            onFilesProcessed(results);
        }
        
        if (onBatchComplete) {
            onBatchComplete(batchStats);
        }
    };

    const pauseProcessing = () => {
        setIsPaused(true);
    };

    const resumeProcessing = () => {
        setIsPaused(false);
    };

    const removeFile = (id: string) => {
        if (isProcessing) return;
        
        setFiles(prev => {
            const updated = prev.filter(f => f.id !== id);
            updateBatchStats(updated);
            return updated;
        });
    };

    const clearAll = () => {
        if (isProcessing) return;
        setFiles([]);
        setBatchStats({
            total: 0,
            completed: 0,
            failed: 0,
            inProgress: 0,
            pending: 0
        });
    };

    const retryFailed = () => {
        setFiles(prev => prev.map(f => 
            f.status === 'error' 
                ? { ...f, status: 'pending', progress: 0, error: undefined }
                : f
        ));
    };

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="status-icon success" size={16} />;
            case 'error':
                return <AlertCircle className="status-icon error" size={16} />;
            case 'processing':
                return <div className="status-spinner" />;
            default:
                return <File className="status-icon pending" size={16} />;
        }
    };

    return (
        <div className="enhanced-file-dropzone">
            <div className="dropzone-header">
                <h3>Batch File Processing</h3>
                <p>Drop files here or click to select multiple files for privacy scanning</p>
            </div>

            <div 
                {...getRootProps()} 
                className={`dropzone-area ${isDragActive ? 'drag-active' : ''} ${isDragReject ? 'drag-reject' : ''} ${isProcessing ? 'disabled' : ''}`}
            >
                <input {...getInputProps()} />
                <Upload className="dropzone-icon" size={48} />
                
                {isDragActive ? (
                    <p className="dropzone-text">Drop the files here...</p>
                ) : (
                    <div className="dropzone-content">
                        <p className="dropzone-text">
                            Drag & drop files here, or <span className="dropzone-link">click to select files</span>
                        </p>
                        <p className="dropzone-info">
                            Supports: {acceptedFileTypes.join(', ')} • Max {maxFiles} files • Max {formatFileSize(maxFileSize)} per file
                        </p>
                    </div>
                )}
            </div>

            {files.length > 0 && (
                <div className="batch-controls">
                    <div className="batch-stats">
                        <div className="stat-item">
                            <span className="stat-value">{batchStats.total}</span>
                            <span className="stat-label">Total</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{batchStats.completed}</span>
                            <span className="stat-label">Completed</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{batchStats.failed}</span>
                            <span className="stat-label">Failed</span>
                        </div>
                        <div className="stat-item">
                            <span className="stat-value">{batchStats.pending}</span>
                            <span className="stat-label">Pending</span>
                        </div>
                    </div>

                    <div className="batch-actions">
                        {!isProcessing ? (
                            <button 
                                className="action-button primary"
                                onClick={processFiles}
                                disabled={batchStats.pending === 0}
                            >
                                <Play size={16} />
                                Start Processing
                            </button>
                        ) : (
                            <button 
                                className="action-button secondary"
                                onClick={isPaused ? resumeProcessing : pauseProcessing}
                            >
                                {isPaused ? <Play size={16} /> : <Pause size={16} />}
                                {isPaused ? 'Resume' : 'Pause'}
                            </button>
                        )}
                        
                        {batchStats.failed > 0 && (
                            <button 
                                className="action-button warning"
                                onClick={retryFailed}
                                disabled={isProcessing}
                            >
                                <RotateCcw size={16} />
                                Retry Failed
                            </button>
                        )}
                        
                        <button 
                            className="action-button danger"
                            onClick={clearAll}
                            disabled={isProcessing}
                        >
                            <X size={16} />
                            Clear All
                        </button>
                    </div>
                </div>
            )}

            {files.length > 0 && (
                <div className="file-list">
                    {files.map((fileItem) => (
                        <div key={fileItem.id} className={`file-item ${fileItem.status}`}>
                            <div className="file-info">
                                {getStatusIcon(fileItem.status)}
                                <div className="file-details">
                                    <div className="file-name">{fileItem.file.name}</div>
                                    <div className="file-meta">
                                        {formatFileSize(fileItem.file.size)} • {fileItem.file.type || 'Unknown type'}
                                    </div>
                                </div>
                            </div>

                            <div className="file-progress">
                                {fileItem.status === 'processing' && (
                                    <Progress.Root className="progress-root" value={fileItem.progress}>
                                        <Progress.Indicator 
                                            className="progress-indicator" 
                                            style={{ transform: `translateX(-${100 - fileItem.progress}%)` }}
                                        />
                                    </Progress.Root>
                                )}
                                
                                {fileItem.status === 'completed' && fileItem.results && (
                                    <div className="file-results">
                                        Risk Score: {fileItem.results.risk_score.toFixed(1)} • 
                                        Findings: {fileItem.results.findings.length}
                                    </div>
                                )}
                                
                                {fileItem.status === 'error' && (
                                    <div className="file-error">
                                        {fileItem.error || 'Processing failed'}
                                    </div>
                                )}
                            </div>

                            {!isProcessing && (
                                <button 
                                    className="remove-file"
                                    onClick={() => removeFile(fileItem.id)}
                                    aria-label="Remove file"
                                >
                                    <X size={16} />
                                </button>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default EnhancedFileDropZone;
