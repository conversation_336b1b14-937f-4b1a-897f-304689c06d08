use std::collections::HashMap;
use regex::Regex;
use serde::{Serialize, Deserialize};
use once_cell::sync::Lazy;
use tracing::{info, debug};

/// Enhanced cryptocurrency detection supporting 20+ cryptocurrency types
/// including hardware wallet recovery phrases and exchange API keys
#[derive(Debug, <PERSON>lone)]
pub struct EnhancedCryptoDetector {
    /// Address patterns for different cryptocurrency types
    address_patterns: HashMap<CryptoType, AddressPattern>,
    /// BIP39 mnemonic word list for recovery phrase detection
    bip39_wordlist: Vec<String>,
    /// Exchange API key patterns
    exchange_patterns: HashMap<ExchangeType, Regex>,
}

/// Comprehensive cryptocurrency types supported
#[derive(Debug, <PERSON>lone, Hash, Eq, PartialEq, Serialize, Deserialize)]
pub enum CryptoType {
    // Major cryptocurrencies
    Bitcoin,
    Ethereum,
    Litecoin,
    Monero,
    Cardano,
    Solana,
    Polygon,
    BinanceSmartChain,
    
    // Stablecoins
    Tether,
    USDCoin,
    BinanceUSD,
    DAI,
    
    // DeFi tokens
    Chainlink,
    <PERSON>iswap,
    <PERSON>ave,
    Compound,
    
    // Privacy coins
    Zcash,
    Dash,
    
    // Hardware wallet recovery
    BIP39Mnemonic,
    
    // Exchange credentials
    ExchangeApiKey(ExchangeType),
}

/// Supported cryptocurrency exchanges
#[derive(Debug, Clone, Hash, Eq, PartialEq, Serialize, Deserialize)]
pub enum ExchangeType {
    Binance,
    Coinbase,
    Kraken,
    Bitfinex,
    Huobi,
    KuCoin,
    FTX,
    Gemini,
    Bitstamp,
    CoinbasePro,
}

/// Address pattern definition
#[derive(Debug, Clone)]
pub struct AddressPattern {
    /// Regex pattern for address format
    pub pattern: Regex,
    /// Address length constraints
    pub min_length: usize,
    pub max_length: usize,
    /// Valid prefixes for the address
    pub valid_prefixes: Vec<String>,
    /// Whether to perform checksum validation
    pub has_checksum: bool,
}

/// Cryptocurrency finding result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CryptoFinding {
    /// Type of cryptocurrency detected
    pub crypto_type: CryptoType,
    /// Type of finding (address, recovery phrase, API key)
    pub finding_type: CryptoFindingType,
    /// Detected content
    pub content: String,
    /// Location in text
    pub location: TextLocation,
    /// Risk level assessment
    pub risk_level: CryptoRiskLevel,
    /// Confidence score
    pub confidence: f32,
    /// Additional validation info
    pub validation_info: Option<String>,
}

/// Types of cryptocurrency findings
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum CryptoFindingType {
    Address,
    RecoveryPhrase,
    ApiKey,
    PrivateKey,
    WalletSeed,
}

/// Cryptocurrency-specific risk levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum CryptoRiskLevel {
    Critical,  // Recovery phrases, private keys
    High,      // API keys, wallet addresses
    Medium,    // Public addresses in certain contexts
    Low,       // Public addresses in safe contexts
}

/// Text location for findings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextLocation {
    pub start: usize,
    pub end: usize,
    pub line: Option<usize>,
    pub column: Option<usize>,
}

/// Address candidate for validation
#[derive(Debug, Clone)]
pub struct AddressCandidate {
    pub content: String,
    pub location: TextLocation,
    pub crypto_type: CryptoType,
}

impl EnhancedCryptoDetector {
    /// Create a new enhanced cryptocurrency detector
    pub fn new() -> Self {
        let address_patterns = Self::load_address_patterns();
        let bip39_wordlist = Self::load_bip39_wordlist();
        let exchange_patterns = Self::load_exchange_patterns();
        
        info!("Enhanced crypto detector initialized with {} crypto types", address_patterns.len());
        
        Self {
            address_patterns,
            bip39_wordlist,
            exchange_patterns,
        }
    }
    
    /// Detect all cryptocurrency-related content in text
    pub fn detect_crypto_assets(&self, text: &str) -> Result<Vec<CryptoFinding>, Box<dyn std::error::Error + Send + Sync>> {
        debug!("Starting enhanced crypto detection on {} characters", text.len());
        
        let mut findings = Vec::new();
        
        // Detect cryptocurrency addresses
        findings.extend(self.detect_addresses(text)?);
        
        // Detect BIP39 mnemonic phrases
        findings.extend(self.detect_mnemonic_phrases(text)?);
        
        // Detect exchange API keys
        findings.extend(self.detect_api_keys(text)?);
        
        info!("Enhanced crypto detection completed: {} findings", findings.len());
        Ok(findings)
    }
    
    /// Detect cryptocurrency addresses
    fn detect_addresses(&self, text: &str) -> Result<Vec<CryptoFinding>, Box<dyn std::error::Error + Send + Sync>> {
        let mut findings = Vec::new();

        // Define a specific order for pattern matching to avoid conflicts
        let pattern_order = vec![
            CryptoType::Bitcoin,
            CryptoType::Ethereum,
            CryptoType::Cardano,
            CryptoType::Litecoin,
            CryptoType::Monero,
            CryptoType::Solana,  // Check Solana last to avoid conflicts with Bitcoin
        ];

        for crypto_type in pattern_order {
            if let Some(pattern) = self.address_patterns.get(&crypto_type) {
            for mat in pattern.pattern.find_iter(text) {
                let candidate = AddressCandidate {
                    content: mat.as_str().to_string(),
                    location: TextLocation {
                        start: mat.start(),
                        end: mat.end(),
                        line: None,
                        column: None,
                    },
                    crypto_type: crypto_type.clone(),
                };
                
                if self.validate_address(&candidate, pattern)? {
                    let risk_level = self.assess_address_risk(&candidate);
                    let confidence = self.calculate_address_confidence(&candidate, pattern);
                    
                    findings.push(CryptoFinding {
                        crypto_type: candidate.crypto_type,
                        finding_type: CryptoFindingType::Address,
                        content: candidate.content,
                        location: candidate.location,
                        risk_level,
                        confidence,
                        validation_info: Some("Address format validated".to_string()),
                    });
                }
            }
            }
        }
        
        Ok(findings)
    }
    
    /// Detect BIP39 mnemonic recovery phrases
    fn detect_mnemonic_phrases(&self, text: &str) -> Result<Vec<CryptoFinding>, Box<dyn std::error::Error + Send + Sync>> {
        let mut findings = Vec::new();
        
        // Split text into potential word sequences
        let words: Vec<&str> = text.split_whitespace().collect();
        
        // Check for sequences of 12, 15, 18, 21, or 24 words
        let valid_lengths = [12, 15, 18, 21, 24];
        
        for &length in &valid_lengths {
            for window in words.windows(length) {
                let valid_words = window.iter()
                    .filter(|word| self.bip39_wordlist.contains(&word.to_lowercase()))
                    .count();
                
                // If 80% or more words are valid BIP39 words, consider it a mnemonic
                if valid_words as f32 / length as f32 >= 0.8 {
                    let phrase = window.join(" ");
                    let start_pos = text.find(&phrase).unwrap_or(0);
                    
                    findings.push(CryptoFinding {
                        crypto_type: CryptoType::BIP39Mnemonic,
                        finding_type: CryptoFindingType::RecoveryPhrase,
                        content: phrase.clone(),
                        location: TextLocation {
                            start: start_pos,
                            end: start_pos + phrase.len(),
                            line: None,
                            column: None,
                        },
                        risk_level: CryptoRiskLevel::Critical,
                        confidence: valid_words as f32 / length as f32,
                        validation_info: Some(format!("{}/{} words valid", valid_words, length)),
                    });
                }
            }
        }
        
        Ok(findings)
    }
    
    /// Detect exchange API keys
    fn detect_api_keys(&self, text: &str) -> Result<Vec<CryptoFinding>, Box<dyn std::error::Error + Send + Sync>> {
        let mut findings = Vec::new();
        
        for (exchange_type, pattern) in &self.exchange_patterns {
            for mat in pattern.find_iter(text) {
                findings.push(CryptoFinding {
                    crypto_type: CryptoType::ExchangeApiKey(exchange_type.clone()),
                    finding_type: CryptoFindingType::ApiKey,
                    content: mat.as_str().to_string(),
                    location: TextLocation {
                        start: mat.start(),
                        end: mat.end(),
                        line: None,
                        column: None,
                    },
                    risk_level: CryptoRiskLevel::High,
                    confidence: 0.9,
                    validation_info: Some(format!("{:?} API key pattern", exchange_type)),
                });
            }
        }
        
        Ok(findings)
    }
    
    /// Validate cryptocurrency address format
    fn validate_address(&self, candidate: &AddressCandidate, pattern: &AddressPattern) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        let content = &candidate.content;
        
        // Check length constraints
        if content.len() < pattern.min_length || content.len() > pattern.max_length {
            return Ok(false);
        }
        
        // Check valid prefixes
        if !pattern.valid_prefixes.is_empty() {
            let has_valid_prefix = pattern.valid_prefixes.iter()
                .any(|prefix| content.starts_with(prefix));
            if !has_valid_prefix {
                return Ok(false);
            }
        }
        
        // Perform cryptocurrency-specific validation
        match candidate.crypto_type {
            CryptoType::Bitcoin => self.validate_bitcoin_address(content),
            CryptoType::Ethereum => self.validate_ethereum_address(content),
            CryptoType::Cardano => self.validate_cardano_address(content),
            CryptoType::Solana => self.validate_solana_address(content),
            _ => Ok(true), // Basic validation for other types
        }
    }
    
    /// Validate Bitcoin address using base58 or bech32 checksum
    fn validate_bitcoin_address(&self, address: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        if address.starts_with("bc1") || address.starts_with("tb1") {
            // Bech32 validation (simplified)
            Ok(address.len() >= 14 && address.len() <= 74)
        } else if address.starts_with('1') || address.starts_with('3') {
            // Base58 validation (simplified)
            Ok(address.len() >= 26 && address.len() <= 35)
        } else {
            Ok(false)
        }
    }
    
    /// Validate Ethereum address format
    fn validate_ethereum_address(&self, address: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Ethereum addresses are 42 characters long and start with 0x
        Ok(address.len() == 42 && address.starts_with("0x") && 
           address[2..].chars().all(|c| c.is_ascii_hexdigit()))
    }
    
    /// Validate Cardano address format
    fn validate_cardano_address(&self, address: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Cardano addresses start with 'addr' and are bech32 encoded
        Ok(address.starts_with("addr") && address.len() >= 50 && address.len() <= 120)
    }
    
    /// Validate Solana address format
    fn validate_solana_address(&self, address: &str) -> Result<bool, Box<dyn std::error::Error + Send + Sync>> {
        // Solana addresses are base58 encoded and 32-44 characters long
        Ok(address.len() >= 32 && address.len() <= 44 && 
           address.chars().all(|c| "**********************************************************".contains(c)))
    }
    
    /// Assess risk level for cryptocurrency address
    fn assess_address_risk(&self, candidate: &AddressCandidate) -> CryptoRiskLevel {
        match candidate.crypto_type {
            CryptoType::BIP39Mnemonic => CryptoRiskLevel::Critical,
            CryptoType::ExchangeApiKey(_) => CryptoRiskLevel::High,
            CryptoType::Bitcoin | CryptoType::Ethereum => CryptoRiskLevel::High,
            _ => CryptoRiskLevel::Medium,
        }
    }
    
    /// Calculate confidence score for address detection
    fn calculate_address_confidence(&self, candidate: &AddressCandidate, pattern: &AddressPattern) -> f32 {
        let mut confidence = 0.7; // Base confidence
        
        // Adjust based on address format quality
        if pattern.has_checksum {
            confidence += 0.2;
        }
        
        // Adjust based on length (closer to expected length = higher confidence)
        let expected_length = (pattern.min_length + pattern.max_length) / 2;
        let length_diff = (candidate.content.len() as i32 - expected_length as i32).abs() as f32;
        let length_penalty = (length_diff / expected_length as f32) * 0.1;
        confidence -= length_penalty;
        
        confidence.clamp(0.0, 1.0)
    }
    
    /// Load address patterns for all supported cryptocurrencies
    fn load_address_patterns() -> HashMap<CryptoType, AddressPattern> {
        let mut patterns = HashMap::new();
        
        // Bitcoin patterns
        patterns.insert(CryptoType::Bitcoin, AddressPattern {
            pattern: Regex::new(r"\b(bc1|[13])[a-zA-HJ-NP-Z0-9]{25,62}\b").unwrap(),
            min_length: 26,
            max_length: 74,
            valid_prefixes: vec!["1".to_string(), "3".to_string(), "bc1".to_string()],
            has_checksum: true,
        });
        
        // Ethereum patterns
        patterns.insert(CryptoType::Ethereum, AddressPattern {
            pattern: Regex::new(r"\b0x[a-fA-F0-9]{40}\b").unwrap(),
            min_length: 42,
            max_length: 42,
            valid_prefixes: vec!["0x".to_string()],
            has_checksum: true,
        });
        
        // Cardano patterns
        patterns.insert(CryptoType::Cardano, AddressPattern {
            pattern: Regex::new(r"\baddr1[a-z0-9]{50,120}\b").unwrap(),
            min_length: 50,
            max_length: 120,
            valid_prefixes: vec!["addr1".to_string()],
            has_checksum: true,
        });
        
        // Solana patterns
        patterns.insert(CryptoType::Solana, AddressPattern {
            pattern: Regex::new(r"\b[1-9A-HJ-NP-Za-km-z]{32,44}\b").unwrap(),
            min_length: 32,
            max_length: 44,
            valid_prefixes: vec![],
            has_checksum: false,
        });
        
        // Add more patterns for other cryptocurrencies...
        
        patterns
    }
    
    /// Load BIP39 wordlist for mnemonic detection
    fn load_bip39_wordlist() -> Vec<String> {
        // Simplified BIP39 wordlist (first 100 words for demonstration)
        vec![
            "abandon", "ability", "able", "about", "above", "absent", "absorb", "abstract",
            "absurd", "abuse", "access", "accident", "account", "accuse", "achieve", "acid",
            "acoustic", "acquire", "across", "act", "action", "actor", "actress", "actual",
            "adapt", "add", "addict", "address", "adjust", "admit", "adult", "advance",
            "advice", "aerobic", "affair", "afford", "afraid", "again", "age", "agent",
            "agree", "ahead", "aim", "air", "airport", "aisle", "alarm", "album",
            "alcohol", "alert", "alien", "all", "alley", "allow", "almost", "alone",
            "alpha", "already", "also", "alter", "always", "amateur", "amazing", "among",
            "amount", "amused", "analyst", "anchor", "ancient", "anger", "angle", "angry",
            "animal", "ankle", "announce", "annual", "another", "answer", "antenna", "antique",
            "anxiety", "any", "apart", "apology", "appear", "apple", "approve", "april",
            "arch", "arctic", "area", "arena", "argue", "arm", "armed", "armor",
            "army", "around", "arrange", "arrest", "arrive", "arrow", "art", "article",
        ].into_iter().map(|s| s.to_string()).collect()
    }
    
    /// Load exchange API key patterns
    fn load_exchange_patterns() -> HashMap<ExchangeType, Regex> {
        let mut patterns = HashMap::new();
        
        // Binance API keys
        patterns.insert(ExchangeType::Binance, 
            Regex::new(r"\b[A-Za-z0-9]{64}\b").unwrap());
        
        // Coinbase API keys
        patterns.insert(ExchangeType::Coinbase, 
            Regex::new(r"\b[a-f0-9]{32}\b").unwrap());
        
        // Add more exchange patterns...
        
        patterns
    }
}

/// Global instance for performance
static ENHANCED_CRYPTO_DETECTOR: Lazy<EnhancedCryptoDetector> = Lazy::new(|| {
    EnhancedCryptoDetector::new()
});

/// Get the global enhanced crypto detector instance
pub fn get_enhanced_crypto_detector() -> &'static EnhancedCryptoDetector {
    &ENHANCED_CRYPTO_DETECTOR
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_bitcoin_address_detection() {
        let detector = EnhancedCryptoDetector::new();
        let text = "Send payment to bitcoin address: **********************************";
        
        let findings = detector.detect_crypto_assets(text).unwrap();
        
        assert!(!findings.is_empty());
        assert_eq!(findings[0].crypto_type, CryptoType::Bitcoin);
        assert_eq!(findings[0].finding_type, CryptoFindingType::Address);
    }
    
    #[test]
    fn test_ethereum_address_detection() {
        let detector = EnhancedCryptoDetector::new();
        let text = "ETH address: ******************************************";
        
        let findings = detector.detect_crypto_assets(text).unwrap();
        
        assert!(!findings.is_empty());
        assert_eq!(findings[0].crypto_type, CryptoType::Ethereum);
    }
    
    #[test]
    fn test_mnemonic_phrase_detection() {
        let detector = EnhancedCryptoDetector::new();
        let text = "Recovery phrase: abandon ability able about above absent absorb abstract absurd abuse access accident";
        
        let findings = detector.detect_crypto_assets(text).unwrap();
        
        let mnemonic_findings: Vec<_> = findings.iter()
            .filter(|f| f.crypto_type == CryptoType::BIP39Mnemonic)
            .collect();
        
        assert!(!mnemonic_findings.is_empty());
        assert_eq!(mnemonic_findings[0].risk_level, CryptoRiskLevel::Critical);
    }
}
