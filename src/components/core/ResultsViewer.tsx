import React, { useState, useMemo } from 'react';
import './ResultsViewer.css';

// Types for scan results - aligned with App.tsx
interface PrivacyScanResult {
    file_path: string;
    risk_score: number;
    findings: PrivacyFinding[];
    ocr_result?: OCRResult;
    processing_time_ms: number;
    file_size: number;
    errors: string[];
}

interface PrivacyFinding {
    data_type: string;
    confidence: number;
    location?: {
        line?: number;
        column_start?: number;
        column_end?: number;
        page?: number;
    };
    context?: string;
    severity: 'Low' | 'Medium' | 'High' | 'Critical';
    detection_method: string;
}

interface OCRResult {
    text: string;
    confidence: number;
    processing_time_ms: number;
    word_count: number;
    detected_language?: string;
}

interface ResultsViewerProps {
    results: PrivacyScanResult[];
    onResultSelect?: (result: PrivacyScanResult) => void;
    onExport?: (filteredResults: PrivacyScanResult[]) => void;
    loading?: boolean;
}

type ViewMode = 'list' | 'grid' | 'table';
type SortBy = 'risk' | 'file' | 'findings' | 'size';
type FilterBy = 'all' | 'critical' | 'high' | 'medium' | 'low';

const ResultsViewer: React.FC<ResultsViewerProps> = ({
    results,
    onResultSelect,
    onExport,
    loading = false
}) => {
    const [viewMode, setViewMode] = useState<ViewMode>('list');
    const [sortBy, setSortBy] = useState<SortBy>('risk');
    const [filterBy, setFilterBy] = useState<FilterBy>('all');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedResult, setSelectedResult] = useState<PrivacyScanResult | null>(null);

    // Filter and sort results
    const filteredAndSortedResults = useMemo(() => {
        let filtered = results.filter(result => {
            // Search filter
            if (searchTerm) {
                const searchLower = searchTerm.toLowerCase();
                const matchesFile = result.file_path.toLowerCase().includes(searchLower);
                const matchesDetection = result.findings.some(f =>
                    f.data_type.toLowerCase().includes(searchLower) ||
                    (f.context && f.context.toLowerCase().includes(searchLower))
                );
                if (!matchesFile && !matchesDetection) return false;
            }

            // Risk filter
            if (filterBy !== 'all') {
                const hasRiskLevel = result.findings.some(f => f.severity.toLowerCase() === filterBy);
                if (!hasRiskLevel) return false;
            }

            return true;
        });

        // Sort results
        filtered.sort((a, b) => {
            switch (sortBy) {
                case 'risk':
                    return b.risk_score - a.risk_score;
                case 'file':
                    return a.file_path.localeCompare(b.file_path);
                case 'findings':
                    return b.findings.length - a.findings.length;
                case 'size':
                    return b.file_size - a.file_size;
                default:
                    return 0;
            }
        });

        return filtered;
    }, [results, searchTerm, filterBy, sortBy]);

    const getRiskColor = (riskLevel: string): string => {
        switch (riskLevel) {
            case 'critical': return '#dc2626';
            case 'high': return '#ea580c';
            case 'medium': return '#d97706';
            case 'low': return '#65a30d';
            default: return '#6b7280';
        }
    };

    const getRiskIcon = (riskLevel: string): string => {
        switch (riskLevel) {
            case 'critical': return '🚨';
            case 'high': return '⚠️';
            case 'medium': return '⚡';
            case 'low': return '💡';
            default: return '📄';
        }
    };

    const formatFileSize = (bytes: number): string => {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    };

    const getHighestRiskLevel = (findings: PrivacyFinding[]): string => {
        const riskLevels = ['Critical', 'High', 'Medium', 'Low'];
        for (const level of riskLevels) {
            if (findings.some(f => f.severity === level)) {
                return level.toLowerCase();
            }
        }
        return 'low';
    };

    const handleResultClick = (result: PrivacyScanResult) => {
        setSelectedResult(result);
        if (onResultSelect) {
            onResultSelect(result);
        }
    };

    const handleExport = () => {
        if (onExport) {
            onExport(filteredAndSortedResults);
        }
    };

    const renderListView = () => (
        <div className="results-list">
            {filteredAndSortedResults.map((result, index) => {
                const highestRisk = getHighestRiskLevel(result.findings);
                return (
                    <div
                        key={index}
                        className={`result-item ${selectedResult === result ? 'selected' : ''}`}
                        onClick={() => handleResultClick(result)}
                        style={{ borderLeftColor: getRiskColor(highestRisk) }}
                    >
                        <div className="result-header">
                            <div className="file-info">
                                <span className="risk-icon">{getRiskIcon(highestRisk)}</span>
                                <span className="file-path">{result.file_path}</span>
                                <span className="file-size">({formatFileSize(result.file_size)})</span>
                            </div>
                            <div className="risk-score">
                                <span className="score-value">{(result.risk_score * 100).toFixed(0)}%</span>
                                <span className="score-label">Risk</span>
                            </div>
                        </div>
                        
                        <div className="result-details">
                            <div className="detection-summary">
                                <span className="detection-count">{result.findings.length} findings</span>
                                <span className="confidence">Processing: {result.processing_time_ms}ms</span>
                            </div>
                            
                            <div className="detection-types">
                                {result.findings.slice(0, 3).map((finding: PrivacyFinding, idx: number) => (
                                    <span
                                        key={idx}
                                        className="detection-tag"
                                        style={{ backgroundColor: getRiskColor(finding.severity.toLowerCase()) }}
                                    >
                                        {finding.data_type}
                                    </span>
                                ))}
                                {result.findings.length > 3 && (
                                    <span className="more-detections">
                                        +{result.findings.length - 3} more
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );

    const renderGridView = () => (
        <div className="results-grid">
            {filteredAndSortedResults.map((result, index) => {
                const highestRisk = getHighestRiskLevel(result.findings);
                return (
                    <div
                        key={index}
                        className={`result-card ${selectedResult === result ? 'selected' : ''}`}
                        onClick={() => handleResultClick(result)}
                    >
                        <div className="card-header" style={{ backgroundColor: getRiskColor(highestRisk) }}>
                            <span className="risk-icon">{getRiskIcon(highestRisk)}</span>
                            <span className="risk-level">{highestRisk.toUpperCase()}</span>
                        </div>
                        
                        <div className="card-content">
                            <div className="file-name">{result.file_path.split('/').pop()}</div>
                            <div className="file-path-small">{result.file_path}</div>
                            
                            <div className="card-stats">
                                <div className="stat">
                                    <span className="stat-value">{result.findings.length}</span>
                                    <span className="stat-label">Findings</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-value">{(result.risk_score * 100).toFixed(0)}%</span>
                                    <span className="stat-label">Risk</span>
                                </div>
                                <div className="stat">
                                    <span className="stat-value">{formatFileSize(result.file_size)}</span>
                                    <span className="stat-label">Size</span>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );

    const renderTableView = () => (
        <div className="results-table-container">
            <table className="results-table">
                <thead>
                    <tr>
                        <th>Risk</th>
                        <th>File</th>
                        <th>Findings</th>
                        <th>Size</th>
                        <th>Risk Score</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    {filteredAndSortedResults.map((result, index) => {
                        const highestRisk = getHighestRiskLevel(result.findings);
                        return (
                            <tr
                                key={index}
                                className={`table-row ${selectedResult === result ? 'selected' : ''}`}
                                onClick={() => handleResultClick(result)}
                            >
                                <td>
                                    <div className="risk-cell" style={{ color: getRiskColor(highestRisk) }}>
                                        <span className="risk-icon">{getRiskIcon(highestRisk)}</span>
                                        <span>{(result.risk_score * 100).toFixed(0)}%</span>
                                    </div>
                                </td>
                                <td className="file-cell">
                                    <div className="file-name">{result.file_path.split('/').pop()}</div>
                                    <div className="file-path">{result.file_path}</div>
                                </td>
                                <td>
                                    <span className="detection-count">{result.findings.length}</span>
                                </td>
                                <td>{formatFileSize(result.file_size)}</td>
                                <td>{(result.risk_score * 100).toFixed(0)}%</td>
                                <td>{result.processing_time_ms}ms</td>
                            </tr>
                        );
                    })}
                </tbody>
            </table>
        </div>
    );

    if (loading) {
        return (
            <div className="results-viewer loading">
                <div className="loading-spinner"></div>
                <p>Processing scan results...</p>
            </div>
        );
    }

    return (
        <div className="results-viewer">
            <div className="results-header">
                <div className="results-title">
                    <h2>📊 Scan Results</h2>
                    <span className="results-count">
                        {filteredAndSortedResults.length} of {results.length} files
                    </span>
                </div>
                
                <div className="results-controls">
                    <div className="search-container">
                        <input
                            type="text"
                            placeholder="Search files or findings..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                        />
                    </div>
                    
                    <div className="filter-controls">
                        <select
                            value={filterBy}
                            onChange={(e) => setFilterBy(e.target.value as FilterBy)}
                            className="filter-select"
                        >
                            <option value="all">All Risk Levels</option>
                            <option value="critical">Critical Only</option>
                            <option value="high">High Only</option>
                            <option value="medium">Medium Only</option>
                            <option value="low">Low Only</option>
                        </select>
                        
                        <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value as SortBy)}
                            className="sort-select"
                        >
                            <option value="risk">Sort by Risk</option>
                            <option value="file">Sort by File Name</option>
                            <option value="findings">Sort by Findings</option>
                            <option value="size">Sort by File Size</option>
                        </select>
                    </div>
                    
                    <div className="view-controls">
                        <button
                            className={`view-button ${viewMode === 'list' ? 'active' : ''}`}
                            onClick={() => setViewMode('list')}
                            title="List View"
                        >
                            📋
                        </button>
                        <button
                            className={`view-button ${viewMode === 'grid' ? 'active' : ''}`}
                            onClick={() => setViewMode('grid')}
                            title="Grid View"
                        >
                            ⊞
                        </button>
                        <button
                            className={`view-button ${viewMode === 'table' ? 'active' : ''}`}
                            onClick={() => setViewMode('table')}
                            title="Table View"
                        >
                            📊
                        </button>
                    </div>
                    
                    <button
                        className="export-button"
                        onClick={handleExport}
                        disabled={filteredAndSortedResults.length === 0}
                    >
                        📤 Export
                    </button>
                </div>
            </div>
            
            <div className="results-content">
                {filteredAndSortedResults.length === 0 ? (
                    <div className="no-results">
                        <p>No results match your current filters.</p>
                        <button onClick={() => { setSearchTerm(''); setFilterBy('all'); }}>
                            Clear Filters
                        </button>
                    </div>
                ) : (
                    <>
                        {viewMode === 'list' && renderListView()}
                        {viewMode === 'grid' && renderGridView()}
                        {viewMode === 'table' && renderTableView()}
                    </>
                )}
            </div>
            
            {selectedResult && (
                <div className="result-details-panel">
                    <div className="details-header">
                        <h3>📄 {selectedResult.file_path.split('/').pop()}</h3>
                        <button
                            className="close-details"
                            onClick={() => setSelectedResult(null)}
                        >
                            ✕
                        </button>
                    </div>
                    
                    <div className="details-content">
                        <div className="detail-section">
                            <h4>File Information</h4>
                            <p><strong>Path:</strong> {selectedResult.file_path}</p>
                            <p><strong>Size:</strong> {formatFileSize(selectedResult.file_size)}</p>
                            <p><strong>Risk Score:</strong> {(selectedResult.risk_score * 100).toFixed(0)}%</p>
                            <p><strong>Processing Time:</strong> {selectedResult.processing_time_ms}ms</p>
                        </div>
                        
                        <div className="detail-section">
                            <h4>Findings ({selectedResult.findings.length})</h4>
                            <div className="detections-list">
                                {selectedResult.findings.map((finding: PrivacyFinding, idx: number) => (
                                    <div key={idx} className="detection-detail">
                                        <div className="detection-header">
                                            <span
                                                className="detection-type"
                                                style={{ color: getRiskColor(finding.severity.toLowerCase()) }}
                                            >
                                                {getRiskIcon(finding.severity.toLowerCase())} {finding.data_type}
                                            </span>
                                            <span className="detection-confidence">
                                                {(finding.confidence * 100).toFixed(0)}%
                                            </span>
                                        </div>
                                        <div className="detection-value">Severity: {finding.severity}</div>
                                        {finding.context && (
                                            <div className="detection-context">
                                                Context: {finding.context}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ResultsViewer;

// CSS file will be created separately
