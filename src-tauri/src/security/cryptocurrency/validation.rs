/// Cryptocurrency validation algorithms
/// 
/// This module provides validation algorithms for cryptocurrency addresses,
/// private keys, and other crypto-related data to reduce false positives.

use serde::{Serialize, Deserialize};
use thiserror::Error;
use super::patterns::CryptoPatternType;

/// Cryptocurrency validation errors
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Ser<PERSON><PERSON>, Deserialize)]
pub enum CryptoValidationError {
    #[error("Invalid format: {message}")]
    InvalidFormat { message: String },
    
    #[error("Invalid checksum")]
    InvalidChecksum,
    
    #[error("Invalid length: expected {expected}, got {actual}")]
    InvalidLength { expected: usize, actual: usize },
    
    #[error("Unsupported pattern type: {pattern_type:?}")]
    UnsupportedType { pattern_type: CryptoPatternType },
    
    #[error("Validation failed: {reason}")]
    ValidationFailed { reason: String },
}

/// Cryptocurrency validation result
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct CryptoValidationResult {
    pub is_valid: bool,
    pub confidence: f32,
    pub pattern_type: CryptoPatternType,
    pub validation_details: Option<String>,
    pub error: Option<CryptoValidationError>,
}

/// Cryptocurrency validator
pub struct CryptocurrencyValidator;

impl CryptocurrencyValidator {
    /// Create a new cryptocurrency validator
    pub fn new() -> Self {
        Self
    }
    
    /// Validate a cryptocurrency pattern match
    pub fn validate(&self, text: &str, pattern_type: CryptoPatternType) -> CryptoValidationResult {
        match pattern_type {
            // Private Key Validation
            CryptoPatternType::BitcoinWIF | CryptoPatternType::BitcoinWIFCompressed => {
                self.validate_bitcoin_wif(text, pattern_type)
            }
            CryptoPatternType::EthereumPrivateKey => {
                self.validate_ethereum_private_key(text)
            }
            CryptoPatternType::RawPrivateKey => {
                self.validate_raw_private_key(text)
            }
            
            // Address Validation
            CryptoPatternType::BitcoinLegacy | CryptoPatternType::BitcoinSegWit => {
                self.validate_bitcoin_address(text, pattern_type)
            }
            CryptoPatternType::BitcoinBech32 => {
                self.validate_bitcoin_bech32(text)
            }
            CryptoPatternType::EthereumAddress => {
                self.validate_ethereum_address(text)
            }
            CryptoPatternType::LitecoinAddress => {
                self.validate_litecoin_address(text)
            }
            CryptoPatternType::CardanoAddress => {
                self.validate_cardano_address(text)
            }

            // Exchange credentials and other patterns don't need complex validation
            _ => CryptoValidationResult {
                is_valid: true,
                confidence: 0.8,
                pattern_type,
                validation_details: Some("Pattern match only".to_string()),
                error: None,
            }
        }
    }
    
    /// Validate Bitcoin WIF private key
    fn validate_bitcoin_wif(&self, wif: &str, pattern_type: CryptoPatternType) -> CryptoValidationResult {
        match self.validate_base58_checksum(wif) {
            Ok(is_valid) => {
                let expected_len = match &pattern_type {
                    CryptoPatternType::BitcoinWIF => 51,
                    CryptoPatternType::BitcoinWIFCompressed => 52,
                    _ => return self.error_result(pattern_type.clone(), CryptoValidationError::UnsupportedType { pattern_type }),
                };
                
                if wif.len() == expected_len && is_valid {
                    CryptoValidationResult {
                        is_valid: true,
                        confidence: 0.95,
                        pattern_type,
                        validation_details: Some("Valid WIF checksum".to_string()),
                        error: None,
                    }
                } else {
                    CryptoValidationResult {
                        is_valid: false,
                        confidence: 0.3,
                        pattern_type,
                        validation_details: Some("Invalid WIF format or checksum".to_string()),
                        error: Some(CryptoValidationError::InvalidChecksum),
                    }
                }
            }
            Err(error) => self.error_result(pattern_type, error),
        }
    }
    
    /// Validate Ethereum private key
    fn validate_ethereum_private_key(&self, key: &str) -> CryptoValidationResult {
        let pattern_type = CryptoPatternType::EthereumPrivateKey;
        
        // Remove 0x prefix if present
        let key_hex = if key.starts_with("0x") {
            &key[2..]
        } else {
            key
        };
        
        // Check length (64 hex characters = 32 bytes)
        if key_hex.len() != 64 {
            return self.error_result(
                pattern_type,
                CryptoValidationError::InvalidLength { expected: 64, actual: key_hex.len() }
            );
        }
        
        // Check if all characters are valid hex
        if key_hex.chars().all(|c| c.is_ascii_hexdigit()) {
            // Additional check: private key should not be zero
            if key_hex != "0000000000000000000000000000000000000000000000000000000000000000" {
                CryptoValidationResult {
                    is_valid: true,
                    confidence: 0.9,
                    pattern_type,
                    validation_details: Some("Valid Ethereum private key format".to_string()),
                    error: None,
                }
            } else {
                CryptoValidationResult {
                    is_valid: false,
                    confidence: 0.1,
                    pattern_type,
                    validation_details: Some("Private key cannot be zero".to_string()),
                    error: Some(CryptoValidationError::ValidationFailed { 
                        reason: "Zero private key".to_string() 
                    }),
                }
            }
        } else {
            self.error_result(
                pattern_type,
                CryptoValidationError::InvalidFormat { message: "Invalid hex characters".to_string() }
            )
        }
    }
    
    /// Validate raw 256-bit private key
    fn validate_raw_private_key(&self, key: &str) -> CryptoValidationResult {
        let pattern_type = CryptoPatternType::RawPrivateKey;
        
        // Check length (64 hex characters = 32 bytes)
        if key.len() != 64 {
            return self.error_result(
                pattern_type,
                CryptoValidationError::InvalidLength { expected: 64, actual: key.len() }
            );
        }
        
        // Check if all characters are valid hex
        if key.chars().all(|c| c.is_ascii_hexdigit()) {
            CryptoValidationResult {
                is_valid: true,
                confidence: 0.8, // Lower confidence as it could be any hex data
                pattern_type,
                validation_details: Some("Valid hex format".to_string()),
                error: None,
            }
        } else {
            self.error_result(
                pattern_type,
                CryptoValidationError::InvalidFormat { message: "Invalid hex characters".to_string() }
            )
        }
    }
    
    /// Validate Bitcoin address (Legacy or SegWit)
    fn validate_bitcoin_address(&self, address: &str, pattern_type: CryptoPatternType) -> CryptoValidationResult {
        match self.validate_base58_checksum(address) {
            Ok(is_valid) => {
                if is_valid {
                    CryptoValidationResult {
                        is_valid: true,
                        confidence: 0.95,
                        pattern_type,
                        validation_details: Some("Valid Base58Check address".to_string()),
                        error: None,
                    }
                } else {
                    CryptoValidationResult {
                        is_valid: false,
                        confidence: 0.3,
                        pattern_type,
                        validation_details: Some("Invalid Base58Check checksum".to_string()),
                        error: Some(CryptoValidationError::InvalidChecksum),
                    }
                }
            }
            Err(error) => self.error_result(pattern_type, error),
        }
    }
    
    /// Validate Bitcoin Bech32 address
    fn validate_bitcoin_bech32(&self, address: &str) -> CryptoValidationResult {
        let pattern_type = CryptoPatternType::BitcoinBech32;
        
        // Basic Bech32 validation (simplified)
        if address.starts_with("bc1") && address.len() >= 42 && address.len() <= 62 {
            // Check if all characters are valid bech32
            let valid_chars = "qpzry9x8gf2tvdw0s3jn54khce6mua7l";
            let address_part = &address[3..]; // Skip "bc1"
            
            if address_part.chars().all(|c| valid_chars.contains(c)) {
                CryptoValidationResult {
                    is_valid: true,
                    confidence: 0.9,
                    pattern_type,
                    validation_details: Some("Valid Bech32 format".to_string()),
                    error: None,
                }
            } else {
                self.error_result(
                    pattern_type,
                    CryptoValidationError::InvalidFormat { message: "Invalid Bech32 characters".to_string() }
                )
            }
        } else {
            self.error_result(
                pattern_type,
                CryptoValidationError::InvalidFormat { message: "Invalid Bech32 format".to_string() }
            )
        }
    }
    
    /// Validate Ethereum address with EIP-55 checksum
    fn validate_ethereum_address(&self, address: &str) -> CryptoValidationResult {
        let pattern_type = CryptoPatternType::EthereumAddress;
        
        // Remove 0x prefix if present
        let addr_hex = if address.starts_with("0x") {
            &address[2..]
        } else {
            address
        };
        
        // Check length (40 hex characters = 20 bytes)
        if addr_hex.len() != 40 {
            return self.error_result(
                pattern_type,
                CryptoValidationError::InvalidLength { expected: 40, actual: addr_hex.len() }
            );
        }
        
        // Check if all characters are valid hex
        if addr_hex.chars().all(|c| c.is_ascii_hexdigit()) {
            // For now, we'll accept any valid hex format
            // Full EIP-55 checksum validation would require keccak256 hashing
            CryptoValidationResult {
                is_valid: true,
                confidence: 0.85,
                pattern_type,
                validation_details: Some("Valid Ethereum address format".to_string()),
                error: None,
            }
        } else {
            self.error_result(
                pattern_type,
                CryptoValidationError::InvalidFormat { message: "Invalid hex characters".to_string() }
            )
        }
    }
    
    /// Validate Litecoin address
    fn validate_litecoin_address(&self, address: &str) -> CryptoValidationResult {
        let pattern_type = CryptoPatternType::LitecoinAddress;

        // Litecoin uses Base58Check like Bitcoin
        match self.validate_base58_checksum(address) {
            Ok(is_valid) => {
                if is_valid {
                    CryptoValidationResult {
                        is_valid: true,
                        confidence: 0.9,
                        pattern_type,
                        validation_details: Some("Valid Litecoin address".to_string()),
                        error: None,
                    }
                } else {
                    CryptoValidationResult {
                        is_valid: false,
                        confidence: 0.3,
                        pattern_type,
                        validation_details: Some("Invalid checksum".to_string()),
                        error: Some(CryptoValidationError::InvalidChecksum),
                    }
                }
            }
            Err(error) => self.error_result(pattern_type, error),
        }
    }

    /// Validate Cardano address (Bech32 format)
    fn validate_cardano_address(&self, address: &str) -> CryptoValidationResult {
        let pattern_type = CryptoPatternType::CardanoAddress;

        // Cardano addresses use Bech32 encoding and start with "addr1"
        if !address.starts_with("addr1") {
            return self.error_result(
                pattern_type,
                CryptoValidationError::InvalidFormat {
                    message: "Cardano address must start with 'addr1'".to_string()
                }
            );
        }

        // Check length (Cardano addresses are typically 103 characters, but allow some variation)
        if address.len() < 90 || address.len() > 120 {
            return self.error_result(
                pattern_type,
                CryptoValidationError::InvalidLength {
                    expected: 103,
                    actual: address.len()
                }
            );
        }

        // Check if all characters after "addr1" are alphanumeric (simplified validation)
        let address_part = &address[5..]; // Skip "addr1"

        if address_part.chars().all(|c| c.is_ascii_alphanumeric()) {
            CryptoValidationResult {
                is_valid: true,
                confidence: 0.9,
                pattern_type,
                validation_details: Some("Valid Cardano address format".to_string()),
                error: None,
            }
        } else {
            self.error_result(
                pattern_type,
                CryptoValidationError::InvalidFormat {
                    message: "Invalid characters in Cardano address".to_string()
                }
            )
        }
    }
    
    /// Validate Base58Check checksum (simplified implementation)
    fn validate_base58_checksum(&self, input: &str) -> Result<bool, CryptoValidationError> {
        // This is a simplified validation - in a production system,
        // you would use a proper Base58 decoding library
        
        // Check if all characters are valid Base58
        let base58_chars = "**********************************************************";
        
        if input.chars().all(|c| base58_chars.contains(c)) {
            // For now, we'll assume valid Base58 characters indicate a valid address
            // In production, you would decode and verify the actual checksum
            Ok(true)
        } else {
            Err(CryptoValidationError::InvalidFormat { 
                message: "Invalid Base58 characters".to_string() 
            })
        }
    }
    
    /// Create an error result
    fn error_result(&self, pattern_type: CryptoPatternType, error: CryptoValidationError) -> CryptoValidationResult {
        CryptoValidationResult {
            is_valid: false,
            confidence: 0.0,
            pattern_type,
            validation_details: None,
            error: Some(error),
        }
    }
}

impl Default for CryptocurrencyValidator {
    fn default() -> Self {
        Self::new()
    }
}
