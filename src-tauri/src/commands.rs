use serde::{Serialize, Deserialize};
use tauri::State;
use tauri_plugin_dialog::DialogExt;
use tokio::sync::Mutex;
use std::path::{Path, PathBuf};
use std::fs;

use crate::privacy::{
    PrivacyDetector, 
    PrivacyDetectionOptions, 
    PrivacyScanResult,
    OCREngine,
    OCRResult,
    OCRConfig,
};

/// Application state for managing privacy detection
pub struct AppState {
    /// Privacy detector instance
    pub privacy_detector: Mutex<Option<PrivacyDetector>>,
    /// OCR engine instance
    pub ocr_engine: Mutex<Option<OCREngine>>,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            privacy_detector: Mutex::new(None),
            ocr_engine: Mutex::new(None),
        }
    }
}

/// Initialize the privacy detection engine
#[tauri::command]
pub async fn initialize_privacy_engine(
    options: Option<PrivacyDetectionOptions>,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let detection_options = options.unwrap_or_default();
    
    match PrivacyDetector::with_options(detection_options) {
        Ok(detector) => {
            let mut privacy_detector = state.privacy_detector.lock().await;
            *privacy_detector = Some(detector);
            Ok("Privacy detection engine initialized successfully".to_string())
        }
        Err(e) => Err(format!("Failed to initialize privacy engine: {}", e)),
    }
}

/// Initialize the OCR engine
#[tauri::command]
pub async fn initialize_ocr_engine(
    config: Option<OCRConfig>,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let ocr_config = config.unwrap_or_default();
    let engine = OCREngine::with_config(ocr_config);
    
    let mut ocr_engine = state.ocr_engine.lock().await;
    *ocr_engine = Some(engine);
    
    Ok("OCR engine initialized successfully".to_string())
}

/// Extract text from an image file using OCR
#[tauri::command]
pub async fn extract_text_from_image(
    imagePath: String,
    state: State<'_, AppState>,
) -> Result<OCRResult, String> {
    let ocr_engine = state.ocr_engine.lock().await;

    match ocr_engine.as_ref() {
        Some(engine) => {
            match engine.extract_text_from_image(&imagePath).await {
                Ok(result) => Ok(result),
                Err(e) => Err(format!("OCR extraction failed: {}", e)),
            }
        }
        None => Err("OCR engine not initialized. Call initialize_ocr_engine first.".to_string()),
    }
}

/// Extract text from a PDF file using OCR
#[tauri::command]
pub async fn extract_text_from_pdf(
    pdfPath: String,
    state: State<'_, AppState>,
) -> Result<OCRResult, String> {
    let ocr_engine = state.ocr_engine.lock().await;

    match ocr_engine.as_ref() {
        Some(engine) => {
            match engine.extract_text_from_pdf(&pdfPath).await {
                Ok(result) => Ok(result),
                Err(e) => Err(format!("PDF OCR extraction failed: {}", e)),
            }
        }
        None => Err("OCR engine not initialized. Call initialize_ocr_engine first.".to_string()),
    }
}

/// Scan a single file for privacy content
#[tauri::command]
pub async fn scan_file_for_privacy(
    file_path: String,
    state: State<'_, AppState>,
) -> Result<PrivacyScanResult, String> {
    let privacy_detector = state.privacy_detector.lock().await;
    
    match privacy_detector.as_ref() {
        Some(detector) => {
            match detector.scan_file(&file_path).await {
                Ok(result) => Ok(result),
                Err(e) => Err(format!("Privacy scan failed: {}", e)),
            }
        }
        None => Err("Privacy detector not initialized. Call initialize_privacy_engine first.".to_string()),
    }
}

/// Scan multiple files in a directory for privacy content
#[tauri::command]
pub async fn scan_directory_for_privacy(
    directory_path: String,
    state: State<'_, AppState>,
) -> Result<Vec<PrivacyScanResult>, String> {
    let privacy_detector = state.privacy_detector.lock().await;
    
    match privacy_detector.as_ref() {
        Some(detector) => {
            let results = detector.scan_directory(&directory_path).await;
            Ok(results)
        }
        None => Err("Privacy detector not initialized. Call initialize_privacy_engine first.".to_string()),
    }
}

/// Get the current privacy detection configuration
#[tauri::command]
pub async fn get_privacy_detection_config(
    state: State<'_, AppState>,
) -> Result<Option<PrivacyDetectionOptions>, String> {
    let privacy_detector = state.privacy_detector.lock().await;
    
    match privacy_detector.as_ref() {
        Some(detector) => Ok(Some(detector.options.clone())),
        None => Ok(None),
    }
}

/// Update privacy detection configuration
#[tauri::command]
pub async fn update_privacy_detection_config(
    new_options: PrivacyDetectionOptions,
    state: State<'_, AppState>,
) -> Result<String, String> {
    match PrivacyDetector::with_options(new_options) {
        Ok(new_detector) => {
            let mut privacy_detector = state.privacy_detector.lock().await;
            *privacy_detector = Some(new_detector);
            Ok("Privacy detection configuration updated successfully".to_string())
        }
        Err(e) => Err(format!("Failed to update configuration: {}", e)),
    }
}

/// Check if privacy detection engine is initialized
#[tauri::command]
pub async fn is_privacy_engine_initialized(
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let privacy_detector = state.privacy_detector.lock().await;
    Ok(privacy_detector.is_some())
}

/// Check if OCR engine is initialized
#[tauri::command]
pub async fn is_ocr_engine_initialized(
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let ocr_engine = state.ocr_engine.lock().await;
    Ok(ocr_engine.is_some())
}

/// Get detailed OCR availability and readiness information
#[tauri::command]
pub async fn get_ocr_availability_details() -> Result<OCRAvailabilityInfo, String> {
    let bridge_available = check_ocr_bridge_availability();
    let image_processing = check_image_processing_libraries();
    let pdf_processing = check_pdf_processing_libraries();
    let engine_dependencies = check_ocr_engine_dependencies();

    let overall_available = bridge_available && image_processing && pdf_processing && engine_dependencies;

    Ok(OCRAvailabilityInfo {
        overall_available,
        bridge_available,
        image_processing_available: image_processing,
        pdf_processing_available: pdf_processing,
        engine_dependencies_available: engine_dependencies,
        supported_image_formats: get_supported_image_formats(),
        supported_languages: get_supported_ocr_languages(),
        max_file_size_mb: calculate_max_file_size() as usize,
        tesseract_js_required: true,
        hybrid_architecture: true,
    })
}

#[derive(serde::Serialize)]
pub struct OCRAvailabilityInfo {
    pub overall_available: bool,
    pub bridge_available: bool,
    pub image_processing_available: bool,
    pub pdf_processing_available: bool,
    pub engine_dependencies_available: bool,
    pub supported_image_formats: Vec<String>,
    pub supported_languages: Vec<String>,
    pub max_file_size_mb: usize,
    pub tesseract_js_required: bool,
    pub hybrid_architecture: bool,
}

/// Get supported OCR languages
fn get_supported_ocr_languages() -> Vec<String> {
    vec![
        "eng".to_string(),    // English
        "spa".to_string(),    // Spanish
        "fra".to_string(),    // French
        "deu".to_string(),    // German
        "ita".to_string(),    // Italian
        "por".to_string(),    // Portuguese
        "rus".to_string(),    // Russian
        "chi_sim".to_string(), // Chinese Simplified
        "chi_tra".to_string(), // Chinese Traditional
        "jpn".to_string(),    // Japanese
        "kor".to_string(),    // Korean
        "ara".to_string(),    // Arabic
        "hin".to_string(),    // Hindi
        "tha".to_string(),    // Thai
        "vie".to_string(),    // Vietnamese
    ]
}

/// Get system information for privacy scanning capabilities
#[tauri::command]
pub async fn test_pattern_detection(content: String) -> Result<serde_json::Value, String> {
    use crate::security::pattern_matcher::PatternMatcher;
    use serde_json::json;

    println!("Testing pattern detection with content: {}", content);

    // Create a pattern matcher
    let pattern_matcher = PatternMatcher::new()
        .map_err(|e| format!("Failed to create pattern matcher: {}", e))?;

    // Test pattern detection using the scan_content method
    let detection_results = pattern_matcher.scan_content(&content, "test_content.txt");

    let mut detections = Vec::new();
    for result in detection_results {
        detections.push(json!({
            "type": format!("{:?}", result.data_type),
            "confidence": result.confidence,
            "severity": format!("{:?}", result.severity),
            "context": result.context_hint,
            "line_number": result.line_number,
            "column_start": result.column_start,
            "column_end": result.column_end
        }));
    }

    println!("Pattern detection found {} matches", detections.len());

    Ok(json!({
        "detections": detections,
        "total_found": detections.len(),
        "content_length": content.len()
    }))
}

#[tauri::command]
pub async fn get_system_info() -> Result<SystemInfo, String> {
    // Detect OCR availability by checking if Tesseract.js is available in frontend
    let ocr_available = detect_ocr_availability();

    // Detect AI models availability by checking ONNX Runtime
    let ai_models_available = detect_ai_models_availability();

    // Get supported image formats based on image crate capabilities
    let supported_image_formats = get_supported_image_formats();

    // Get supported document formats
    let supported_document_formats = get_supported_document_formats();

    // Calculate max file size based on available memory
    let max_file_size_mb = calculate_max_file_size();

    Ok(SystemInfo {
        ocr_available,
        ai_models_available,
        supported_image_formats,
        supported_document_formats,
        max_file_size_mb: max_file_size_mb as usize,
    })
}

/// Detect if OCR functionality is available
fn detect_ocr_availability() -> bool {
    // Comprehensive OCR availability check
    check_ocr_bridge_availability() &&
    check_image_processing_libraries() &&
    check_pdf_processing_libraries() &&
    check_ocr_engine_dependencies()
}

/// Check if OCR bridge components are available
fn check_ocr_bridge_availability() -> bool {
    use crate::privacy::ocr_bridge::OCRBridge;

    // Try to create OCR bridge instance (OCRBridge::new() doesn't take parameters)
    let test_result = std::panic::catch_unwind(|| {
        let _bridge = OCRBridge::new();
        true
    });

    match test_result {
        Ok(_) => {
            println!("✅ OCR Bridge: Available");
            true
        }
        Err(_) => {
            println!("❌ OCR Bridge: Failed to initialize");
            false
        }
    }
}

/// Check if image processing libraries are available
fn check_image_processing_libraries() -> bool {
    // Test image crate functionality
    let test_result = std::panic::catch_unwind(|| {
        use image::{ImageBuffer, Rgb};

        // Create a small test image
        let _img: ImageBuffer<Rgb<u8>, Vec<u8>> = ImageBuffer::new(10, 10);
        true
    });

    match test_result {
        Ok(_) => {
            println!("✅ Image Processing: Available (image crate)");
            true
        }
        Err(_) => {
            println!("❌ Image Processing: Failed to access image crate");
            false
        }
    }
}

/// Check if PDF processing libraries are available
fn check_pdf_processing_libraries() -> bool {
    // Test lopdf functionality
    let lopdf_available = std::panic::catch_unwind(|| {
        use lopdf::Document;
        // Just test if we can access the Document type
        let _test = std::mem::size_of::<Document>();
        true
    }).is_ok();

    // Test pdfium-render functionality
    let pdfium_available = std::panic::catch_unwind(|| {
        use pdfium_render::prelude::*;
        // Test if we can access Pdfium types
        let _test = std::mem::size_of::<PdfRenderConfig>();
        true
    }).is_ok();

    if lopdf_available && pdfium_available {
        println!("✅ PDF Processing: Available (lopdf + pdfium-render)");
        true
    } else {
        println!("❌ PDF Processing: Missing libraries (lopdf: {}, pdfium: {})",
                lopdf_available, pdfium_available);
        false
    }
}

/// Check OCR engine dependencies and configuration
fn check_ocr_engine_dependencies() -> bool {
    use crate::privacy::ocr_engine::{OCREngine, OCRConfig};

    // Test OCR engine creation
    let engine_test = std::panic::catch_unwind(|| {
        let config = OCRConfig {
            language: "eng".to_string(),
            confidence_threshold: 0.7,
            max_file_size: 10 * 1024 * 1024, // 10MB for test
            enable_preprocessing: true,
        };

        // Try to create OCR engine
        let _engine = OCREngine::with_config(config);
        true
    });

    match engine_test {
        Ok(_) => {
            println!("✅ OCR Engine: Available and configurable");
            true
        }
        Err(_) => {
            println!("❌ OCR Engine: Failed to create engine instance");
            false
        }
    }
}

/// Detect if AI models are available
fn detect_ai_models_availability() -> bool {
    // Check for both external ONNX models and built-in AI capabilities
    check_for_model_files() || check_builtin_ai_capabilities()
}

/// Check for available AI model files
fn check_for_model_files() -> bool {
    use std::path::Path;

    // Check common model directories
    let model_paths = [
        "models/",
        "assets/models/",
        "../models/",
        "./models/",
    ];

    for path in &model_paths {
        if Path::new(path).exists() {
            if let Ok(entries) = std::fs::read_dir(path) {
                for entry in entries.flatten() {
                    if let Some(ext) = entry.path().extension() {
                        if ext == "onnx" {
                            return true;
                        }
                    }
                }
            }
        }
    }

    false
}

/// Check for built-in AI capabilities that don't require external model files
fn check_builtin_ai_capabilities() -> bool {
    // The PrivacyAI application has several built-in AI capabilities:
    // 1. Nano Models - Lightweight AI models with simulated inference
    // 2. AI Enhanced Detection - AI-powered privacy detection
    // 3. Document Classification ML - Machine learning document classification
    // 4. AI Context Analysis - AI-driven context analysis

    // These are always available as they're built into the application
    true
}

/// Get supported image formats based on image crate capabilities
fn get_supported_image_formats() -> Vec<String> {
    // These are the formats supported by the image crate
    vec![
        "jpg".to_string(),
        "jpeg".to_string(),
        "png".to_string(),
        "bmp".to_string(),
        "tiff".to_string(),
        "tif".to_string(),
        "webp".to_string(),
        "gif".to_string(),
        "ico".to_string(),
    ]
}

/// Get supported document formats
fn get_supported_document_formats() -> Vec<String> {
    vec![
        "pdf".to_string(),
        "txt".to_string(),
        "rtf".to_string(),
        "doc".to_string(),
        "docx".to_string(),
    ]
}

/// Calculate maximum file size based on available system memory
fn calculate_max_file_size() -> u64 {
    // Get system memory information
    let available_memory_mb = get_available_memory_mb();

    // Use a conservative approach: max file size is 10% of available memory
    // but not less than 50MB and not more than 500MB
    let calculated_size = (available_memory_mb as f64 * 0.1) as u64;

    calculated_size.max(50).min(500)
}

/// Get available system memory in MB
fn get_available_memory_mb() -> u64 {
    // Try to get system memory information
    // This is a simplified implementation

    #[cfg(target_os = "windows")]
    {
        get_windows_memory_mb()
    }

    #[cfg(not(target_os = "windows"))]
    {
        // Default fallback for other platforms
        4096 // Assume 4GB as default
    }
}

#[cfg(target_os = "windows")]
fn get_windows_memory_mb() -> u64 {
    // Windows-specific memory detection
    // This is a simplified version - in production you'd use proper Windows APIs

    // For now, use a heuristic based on environment variables or default
    if let Ok(memory_str) = std::env::var("PROCESSOR_LEVEL") {
        // This is a rough heuristic - not accurate but better than hardcoded
        match memory_str.parse::<u64>() {
            Ok(level) => (level * 1024).max(2048).min(32768), // Scale based on processor level
            Err(_) => 4096, // Default to 4GB
        }
    } else {
        4096 // Default to 4GB
    }
}

/// System information for privacy scanning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemInfo {
    /// Whether OCR is available
    pub ocr_available: bool,
    /// Whether AI models are available
    pub ai_models_available: bool,
    /// Supported image formats
    pub supported_image_formats: Vec<String>,
    /// Supported document formats
    pub supported_document_formats: Vec<String>,
    /// Maximum file size in MB
    pub max_file_size_mb: usize,
}

/// Quick privacy risk assessment result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacyRiskIndicator {
    /// Risk level assessment
    pub risk_level: String, // 'low', 'medium', 'high', 'critical'
    /// Confidence in the assessment (0.0-1.0)
    pub confidence: f32,
    /// Preview of potential findings
    pub preview_findings: Vec<String>,
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// File size in bytes
    pub file_size: u64,
}

/// Test command to verify Tauri integration
#[tauri::command]
pub fn greet(name: &str) -> String {
    format!("Hello, {}! PrivacyAI is ready for privacy scanning.", name)
}

/// Quick privacy risk assessment for immediate user feedback
#[tauri::command]
pub async fn quick_privacy_assessment(file_path: String) -> Result<PrivacyRiskIndicator, String> {
    use std::time::Instant;
    use std::path::Path;

    let start_time = Instant::now();
    let path = Path::new(&file_path);

    // Check if file exists
    if !path.exists() {
        return Err(format!("File not found: {}", file_path));
    }

    // Get file size
    let file_size = std::fs::metadata(path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?
        .len();

    // Quick pattern-based assessment using existing pattern matcher
    let mut preview_findings = Vec::new();
    let mut risk_score = 0.0f32;

    // Check filename for privacy indicators
    let filename = path.file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_lowercase();

    let privacy_keywords = [
        "personal", "private", "confidential", "ssn", "tax", "medical",
        "bank", "passport", "license", "id", "credit", "card"
    ];

    for keyword in &privacy_keywords {
        if filename.contains(keyword) {
            preview_findings.push(format!("Filename contains privacy keyword: {}", keyword));
            risk_score += 0.3;
        }
    }

    // Check file extension for high-risk types
    if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
        match ext.to_lowercase().as_str() {
            "pdf" | "doc" | "docx" => {
                preview_findings.push("Document file - may contain text data".to_string());
                risk_score += 0.2;
            }
            "jpg" | "jpeg" | "png" | "bmp" | "tiff" => {
                preview_findings.push("Image file - may contain faces or text".to_string());
                risk_score += 0.1;
            }
            _ => {}
        }
    }

    // Check file size (larger files more likely to contain privacy data)
    if file_size > 1_000_000 { // > 1MB
        preview_findings.push("Large file - higher probability of privacy content".to_string());
        risk_score += 0.1;
    }

    // Determine risk level
    let risk_level = if risk_score >= 0.7 {
        "critical"
    } else if risk_score >= 0.5 {
        "high"
    } else if risk_score >= 0.3 {
        "medium"
    } else {
        "low"
    };

    let processing_time = start_time.elapsed().as_millis() as u64;

    Ok(PrivacyRiskIndicator {
        risk_level: risk_level.to_string(),
        confidence: 0.8, // Pattern matching confidence
        preview_findings,
        processing_time_ms: processing_time,
        file_size,
    })
}

/// Progressive privacy assessment with streaming results
#[tauri::command]
pub async fn progressive_privacy_assessment(
    file_path: String,
    state: State<'_, AppState>,
) -> Result<Vec<crate::privacy::ProgressiveResult>, String> {
    use crate::privacy::{ProgressiveProcessor, ProgressiveProcessingError};

    // Create a progressive processor
    let mut processor = ProgressiveProcessor::new()
        .map_err(|e| format!("Failed to create progressive processor: {}", e))?;

    // Get privacy detector from state if available
    let privacy_detector = state.privacy_detector.lock().await;
    if let Some(_detector) = privacy_detector.as_ref() {
        // Clone the detector for the processor
        // Note: In a real implementation, we'd need to make PrivacyDetector cloneable
        // or use a different approach for sharing
        drop(privacy_detector); // Release the lock

        // For now, process without the full detector
        // In a complete implementation, we'd set the detector here
        // processor.set_privacy_detector(detector.clone());
    }

    // Process the file progressively
    processor.process_file_progressive(&file_path).await
        .map_err(|e| match e {
            ProgressiveProcessingError::NanoModelError(nano_err) => {
                format!("Nano model error: {}", nano_err)
            }
            ProgressiveProcessingError::PrivacyDetectionError(privacy_err) => {
                format!("Privacy detection error: {}", privacy_err)
            }
            ProgressiveProcessingError::PatternMatchingError { message } => {
                format!("Pattern matching error: {}", message)
            }
            ProgressiveProcessingError::FileProcessingError { message } => {
                format!("File processing error: {}", message)
            }
            ProgressiveProcessingError::PermissionDenied { path } => {
                format!("Access denied: Cannot read file '{}'. Please check file permissions or try running as administrator.", path)
            }
            ProgressiveProcessingError::FileNotFound { path } => {
                format!("File not found: '{}'", path)
            }
            ProgressiveProcessingError::StageTimeout { stage, expected_ms, actual_ms } => {
                format!("Stage '{}' timeout: took {}ms, expected <{}ms", stage, actual_ms, expected_ms)
            }
        })
}

/// Open file dialog to select a single file
#[tauri::command]
pub async fn select_file(app: tauri::AppHandle) -> Result<Option<String>, String> {
    let file_path = app.dialog()
        .file()
        .add_filter("All Supported", &["txt", "pdf", "jpg", "jpeg", "png", "bmp", "tiff", "webp"])
        .add_filter("Text Files", &["txt"])
        .add_filter("PDF Files", &["pdf"])
        .add_filter("Image Files", &["jpg", "jpeg", "png", "bmp", "tiff", "webp"])
        .add_filter("All Files", &["*"])
        .blocking_pick_file();

    match file_path {
        Some(path) => Ok(Some(path.to_string())),
        None => Ok(None),
    }
}

/// Open file dialog to select multiple files
#[tauri::command]
pub async fn select_files(app: tauri::AppHandle) -> Result<Vec<String>, String> {
    let file_paths = app.dialog()
        .file()
        .add_filter("All Supported", &["txt", "pdf", "jpg", "jpeg", "png", "bmp", "tiff", "webp"])
        .add_filter("Text Files", &["txt"])
        .add_filter("PDF Files", &["pdf"])
        .add_filter("Image Files", &["jpg", "jpeg", "png", "bmp", "tiff", "webp"])
        .add_filter("All Files", &["*"])
        .blocking_pick_files();

    match file_paths {
        Some(paths) => Ok(paths.into_iter().map(|path| path.to_string()).collect()),
        None => Ok(Vec::new()),
    }
}

/// Open directory dialog to select a folder
#[tauri::command]
pub async fn select_directory(app: tauri::AppHandle) -> Result<Option<String>, String> {
    let dir_path = app.dialog()
        .file()
        .blocking_pick_folder();

    match dir_path {
        Some(path) => Ok(Some(path.to_string())),
        None => Ok(None),
    }
}

// File Preview Data Structures and Commands

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilePreviewInfo {
    pub file_path: String,
    pub file_name: String,
    pub file_size: u64,
    pub file_type: String,
    pub is_text_file: bool,
    pub is_pdf_file: bool,
    pub can_preview: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilePreviewContent {
    pub file_info: FilePreviewInfo,
    pub content_type: String,
    pub content: String,
    pub is_truncated: bool,
    pub total_size: u64,
    pub preview_size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilePreviewError {
    pub error_type: String,
    pub message: String,
    pub file_path: String,
}

/// Get file preview information without reading content
#[tauri::command]
pub async fn get_file_preview_info(file_path: String) -> Result<FilePreviewInfo, String> {
    let path = Path::new(&file_path);

    if !path.exists() {
        return Err(format!("File does not exist: {}", file_path));
    }

    if !path.is_file() {
        return Err(format!("Path is not a file: {}", file_path));
    }

    let metadata = fs::metadata(path)
        .map_err(|e| format!("Failed to read file metadata: {}", e))?;

    let file_name = path.file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("unknown")
        .to_string();

    let file_extension = path.extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("")
        .to_lowercase();

    let is_text_file = matches!(file_extension.as_str(),
        "txt" | "md" | "log" | "csv" | "json" | "xml" | "html" | "css" | "js" | "ts" |
        "py" | "rs" | "java" | "cpp" | "c" | "h" | "hpp" | "yaml" | "yml" | "toml" | "ini"
    );

    let is_pdf_file = file_extension == "pdf";
    let can_preview = is_text_file || is_pdf_file;

    Ok(FilePreviewInfo {
        file_path: file_path.clone(),
        file_name,
        file_size: metadata.len(),
        file_type: file_extension,
        is_text_file,
        is_pdf_file,
        can_preview,
    })
}

/// Read file content for preview (with size limits for security)
#[tauri::command]
pub async fn get_file_preview_content(
    file_path: String,
    max_size: Option<u64>
) -> Result<FilePreviewContent, String> {
    let path = Path::new(&file_path);

    if !path.exists() {
        return Err(format!("File does not exist: {}", file_path));
    }

    if !path.is_file() {
        return Err(format!("Path is not a file: {}", file_path));
    }

    // Get file info first
    let file_info = get_file_preview_info(file_path.clone()).await?;

    if !file_info.can_preview {
        return Err(format!("File type '{}' is not supported for preview", file_info.file_type));
    }

    // Set reasonable size limits for security
    let max_preview_size = max_size.unwrap_or(1024 * 1024); // 1MB default limit
    let total_size = file_info.file_size;
    let is_truncated = total_size > max_preview_size;
    let read_size = if is_truncated { max_preview_size } else { total_size };

    // For PDF files, we'll return the file path for PDF.js to handle
    if file_info.is_pdf_file {
        return Ok(FilePreviewContent {
            file_info,
            content_type: "application/pdf".to_string(),
            content: file_path, // PDF.js will load this directly
            is_truncated: false,
            total_size,
            preview_size: total_size,
        });
    }

    // For text files, read the content
    if file_info.is_text_file {
        let content = if is_truncated {
            // Read only the first part of the file
            let mut buffer = vec![0; read_size as usize];
            let mut file = fs::File::open(path)
                .map_err(|e| format!("Failed to open file: {}", e))?;

            use std::io::Read;
            let bytes_read = file.read(&mut buffer)
                .map_err(|e| format!("Failed to read file: {}", e))?;

            buffer.truncate(bytes_read);
            String::from_utf8_lossy(&buffer).to_string()
        } else {
            // Read the entire file
            fs::read_to_string(path)
                .map_err(|e| format!("Failed to read file as text: {}", e))?
        };

        return Ok(FilePreviewContent {
            file_info,
            content_type: "text/plain".to_string(),
            content,
            is_truncated,
            total_size,
            preview_size: read_size,
        });
    }

    Err(format!("Unsupported file type for preview: {}", file_info.file_type))
}
