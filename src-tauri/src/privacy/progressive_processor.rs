use std::time::Instant;
use serde::{Serialize, Deserialize};
use thiserror::Error;
use crate::privacy::{
    Nano<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NanoModelError,
    PrivacyDetector, PrivacyScanResult, PrivacyDetectionError,
};
use crate::security::PatternMatcher;

/// Progressive processing pipeline for streaming privacy analysis results
/// 
/// This processor implements a 3-stage pipeline:
/// 1. Preview (80ms): Nano-models for instant feedback
/// 2. Patterns (50ms): Pattern matching for structured data
/// 3. Complete (550ms): Full AI analysis with lightweight models
#[derive(Debug)]
pub struct ProgressiveProcessor {
    /// Nano model manager for instant preview
    nano_models: NanoModelManager,
    /// Pattern matcher for structured data detection
    pattern_matcher: PatternMatcher,
    /// Privacy detector for full analysis
    privacy_detector: Option<PrivacyDetector>,
}

/// Progressive processing result stages
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
#[serde(tag = "stage")]
pub enum ProgressiveResult {
    /// Stage 1: Instant preview from nano models (80ms)
    Preview {
        /// Nano model results
        nano_results: Vec<NanoResult>,
        /// Overall risk level (0-3)
        risk_level: u8,
        /// Processing time in milliseconds
        processing_time_ms: u64,
        /// Confidence score (0.0-1.0)
        confidence: f32,
    },
    /// Stage 2: Pattern matching results (50ms)
    Patterns {
        /// Number of patterns detected
        patterns_detected: usize,
        /// Pattern types found
        pattern_types: Vec<String>,
        /// Updated risk level
        risk_level: u8,
        /// Processing time in milliseconds
        processing_time_ms: u64,
        /// Confidence score (0.0-1.0)
        confidence: f32,
    },
    /// Stage 3: Complete analysis (550ms)
    Complete {
        /// Full privacy scan result
        scan_result: PrivacyScanResult,
        /// Final risk level
        risk_level: u8,
        /// Total processing time in milliseconds
        total_processing_time_ms: u64,
        /// Final confidence score (0.0-1.0)
        confidence: f32,
    },
}

/// Errors that can occur during progressive processing
#[derive(Debug, Error)]
pub enum ProgressiveProcessingError {
    #[error("Nano model error: {0}")]
    NanoModelError(#[from] NanoModelError),
    
    #[error("Privacy detection error: {0}")]
    PrivacyDetectionError(#[from] PrivacyDetectionError),
    
    #[error("Pattern matching error: {message}")]
    PatternMatchingError { message: String },
    
    #[error("File processing error: {message}")]
    FileProcessingError { message: String },

    #[error("Permission denied: {path}")]
    PermissionDenied { path: String },

    #[error("File not found: {path}")]
    FileNotFound { path: String },
    
    #[error("Stage timeout: stage {stage} took {actual_ms}ms, expected <{expected_ms}ms")]
    StageTimeout {
        stage: String,
        expected_ms: u64,
        actual_ms: u64,
    },
}

impl ProgressiveProcessor {
    /// Create a new progressive processor
    pub fn new() -> Result<Self, ProgressiveProcessingError> {
        let nano_models = NanoModelManager::new();
        let pattern_matcher = PatternMatcher::new()
            .map_err(|e| ProgressiveProcessingError::PatternMatchingError {
                message: format!("Failed to initialize pattern matcher: {}", e)
            })?;
        
        Ok(Self {
            nano_models,
            pattern_matcher,
            privacy_detector: None,
        })
    }
    
    /// Set the privacy detector for full analysis
    pub fn set_privacy_detector(&mut self, detector: PrivacyDetector) {
        self.privacy_detector = Some(detector);
    }
    
    /// Process a file progressively through all stages
    pub async fn process_file_progressive(&mut self, file_path: &str) -> Result<Vec<ProgressiveResult>, ProgressiveProcessingError> {
        let mut results = Vec::new();
        let overall_start = Instant::now();
        
        // Validate file access before processing
        if !std::path::Path::new(file_path).exists() {
            return Err(ProgressiveProcessingError::FileNotFound {
                path: file_path.to_string()
            });
        }

        // Read file data for processing with improved error handling
        let file_data = tokio::fs::read(file_path).await
            .map_err(|e| match e.kind() {
                std::io::ErrorKind::PermissionDenied => {
                    ProgressiveProcessingError::PermissionDenied {
                        path: file_path.to_string()
                    }
                }
                std::io::ErrorKind::NotFound => {
                    ProgressiveProcessingError::FileNotFound {
                        path: file_path.to_string()
                    }
                }
                _ => ProgressiveProcessingError::FileProcessingError {
                    message: format!("Failed to read file {}: {}", file_path, e)
                }
            })?;
        
        // Stage 1: Nano model preview (80ms target)
        let preview_result = self.stage1_preview(&file_data).await?;
        results.push(preview_result);
        
        // Stage 2: Pattern matching (50ms target)
        let pattern_result = self.stage2_patterns(&file_data).await?;
        results.push(pattern_result);
        
        // Stage 3: Complete analysis (550ms target)
        if let Some(ref detector) = self.privacy_detector {
            let complete_result = self.stage3_complete(detector, file_path, overall_start).await?;
            results.push(complete_result);
        }
        
        Ok(results)
    }
    
    /// Stage 1: Instant preview using nano models (80ms target)
    async fn stage1_preview(&mut self, file_data: &[u8]) -> Result<ProgressiveResult, ProgressiveProcessingError> {
        let start_time = Instant::now();
        
        // Generate a simple hash for caching
        let file_hash = blake3::hash(file_data).to_hex().to_string();
        
        // Use nano models for instant preview
        let nano_results = self.nano_models.cached_inference(file_hash, file_data).await?;
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        // Check timeout
        if processing_time > 80 {
            return Err(ProgressiveProcessingError::StageTimeout {
                stage: "Preview".to_string(),
                expected_ms: 80,
                actual_ms: processing_time,
            });
        }
        
        // Calculate overall risk level and confidence
        let risk_level = nano_results.iter().map(|r| r.risk_level).max().unwrap_or(0);
        let confidence = nano_results.iter().map(|r| r.confidence).fold(0.0f32, |acc, c| acc.max(c));
        
        Ok(ProgressiveResult::Preview {
            nano_results,
            risk_level,
            processing_time_ms: processing_time,
            confidence,
        })
    }
    
    /// Stage 2: Pattern matching for structured data (50ms target)
    async fn stage2_patterns(&self, file_data: &[u8]) -> Result<ProgressiveResult, ProgressiveProcessingError> {
        let start_time = Instant::now();
        
        // Convert file data to string for pattern matching
        let content = match std::str::from_utf8(file_data) {
            Ok(content) => content,
            Err(_) => {
                // For binary files, return empty pattern result
                return Ok(ProgressiveResult::Patterns {
                    patterns_detected: 0,
                    pattern_types: vec![],
                    risk_level: 0,
                    processing_time_ms: start_time.elapsed().as_millis() as u64,
                    confidence: 0.0,
                });
            }
        };
        
        // Run pattern matching
        let detection_results = self.pattern_matcher.scan_content(content, "progressive_scan");
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        
        // Check timeout
        if processing_time > 50 {
            return Err(ProgressiveProcessingError::StageTimeout {
                stage: "Patterns".to_string(),
                expected_ms: 50,
                actual_ms: processing_time,
            });
        }
        
        // Extract pattern information
        let patterns_detected = detection_results.len();
        let pattern_types: Vec<String> = detection_results
            .iter()
            .map(|r| format!("{:?}", r.data_type))
            .collect();
        
        // Calculate risk level based on patterns found
        let risk_level = if patterns_detected > 5 { 3 }
                        else if patterns_detected > 2 { 2 }
                        else if patterns_detected > 0 { 1 }
                        else { 0 };
        
        let confidence = if patterns_detected > 0 { 0.85 } else { 0.0 };
        
        Ok(ProgressiveResult::Patterns {
            patterns_detected,
            pattern_types,
            risk_level,
            processing_time_ms: processing_time,
            confidence,
        })
    }
    
    /// Stage 3: Complete analysis using full privacy detector (550ms target)
    async fn stage3_complete(
        &self, 
        detector: &PrivacyDetector, 
        file_path: &str,
        overall_start: Instant
    ) -> Result<ProgressiveResult, ProgressiveProcessingError> {
        let start_time = Instant::now();
        
        // Run full privacy scan
        let scan_result = detector.scan_file(file_path).await?;
        
        let processing_time = start_time.elapsed().as_millis() as u64;
        let total_processing_time = overall_start.elapsed().as_millis() as u64;
        
        // Check timeout
        if processing_time > 550 {
            return Err(ProgressiveProcessingError::StageTimeout {
                stage: "Complete".to_string(),
                expected_ms: 550,
                actual_ms: processing_time,
            });
        }
        
        // Calculate final risk level and confidence
        let risk_level = if scan_result.risk_score >= 0.8 { 3 }
                        else if scan_result.risk_score >= 0.6 { 2 }
                        else if scan_result.risk_score >= 0.3 { 1 }
                        else { 0 };
        
        let confidence = scan_result.risk_score;
        
        Ok(ProgressiveResult::Complete {
            scan_result,
            risk_level,
            total_processing_time_ms: total_processing_time,
            confidence,
        })
    }
    
    /// Get cache statistics from nano models
    pub fn get_cache_stats(&self) -> f32 {
        self.nano_models.get_cache_hit_rate()
    }
}
