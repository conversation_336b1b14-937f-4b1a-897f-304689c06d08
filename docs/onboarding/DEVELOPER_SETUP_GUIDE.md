# 🛠️ **Developer Setup Guide**

**Version**: 2.0.0-beta  
**Last Updated**: July 27, 2025  
**Target Audience**: New developers joining the PrivacyAI project  
**Estimated Setup Time**: 30-45 minutes

## 📋 **Prerequisites**

### **Required Software**

| Software | Version | Purpose | Download Link |
|----------|---------|---------|---------------|
| **Node.js** | v18.0+ | Frontend development and package management | [nodejs.org](https://nodejs.org/) |
| **Rust** | Latest stable | Backend development and Tauri | [rustup.rs](https://rustup.rs/) |
| **Git** | Latest | Version control | [git-scm.com](https://git-scm.com/) |
| **VS Code** | Latest | Recommended IDE | [code.visualstudio.com](https://code.visualstudio.com/) |

### **Platform-Specific Requirements**

#### **Windows**
- **Visual Studio Build Tools** or **Visual Studio Community** with C++ workload
- **Windows SDK** (latest version)
- **MSVC toolchain** (installed with Visual Studio)

#### **macOS**
- **Xcode Command Line Tools**: `xcode-select --install`
- **Homebrew** (recommended): `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`

#### **Linux (Ubuntu/Debian)**
```bash
sudo apt update
sudo apt install build-essential curl wget file libssl-dev libgtk-3-dev libayatana-appindicator3-dev librsvg2-dev
```

## 🚀 **Quick Setup (5 minutes)**

### **1. Clone the Repository**

```bash
# Clone the repository
git clone https://github.com/LogicPTK/PrivacyAI.git
cd PrivacyAI

# Verify you're on the correct branch
git branch -a
git checkout feature/phase3-scalability-optimization  # or main/master
```

### **2. Install Dependencies**

```bash
# Install Node.js dependencies
npm install

# Install Rust (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Add required Rust targets
rustup target add wasm32-unknown-unknown
```

### **3. Verify Installation**

```bash
# Check versions
node --version    # Should be v18.0+
npm --version     # Should be 8.0+
rustc --version   # Should be 1.70+
cargo --version   # Should be 1.70+

# Test build
npm run tauri dev
```

If the development server starts successfully, you're ready to go! 🎉

## 🔧 **Detailed Setup**

### **Development Environment Configuration**

#### **VS Code Extensions (Recommended)**

Install these extensions for optimal development experience:

```json
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "tauri-apps.tauri-vscode",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

#### **VS Code Settings**

Create `.vscode/settings.json`:

```json
{
  "rust-analyzer.checkOnSave.command": "clippy",
  "rust-analyzer.cargo.features": "all",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ]
}
```

### **Project Structure Understanding**

```
PrivacyAI/
├── 📁 src/                          # React frontend
│   ├── 📁 components/               # React components
│   │   ├── ScanConfiguration.tsx   # Granular scan configuration UI
│   │   └── ui/                     # shadcn/ui components
│   ├── 📁 hooks/                   # Custom React hooks
│   ├── 📁 lib/                     # Utility functions
│   └── 📁 types/                   # TypeScript type definitions
├── 📁 src-tauri/                   # Rust backend
│   ├── 📁 src/                     # Rust source code
│   │   ├── 📁 unified/             # Unified scanning architecture
│   │   │   ├── scan_configuration.rs  # Granular configuration system
│   │   │   ├── scan_profiles.rs       # Predefined scan profiles
│   │   │   └── commands.rs             # Tauri commands
│   │   ├── 📁 privacy/             # Privacy detection modules
│   │   ├── 📁 security/            # Security and cryptocurrency detection
│   │   ├── 📁 core/                # Core file operations
│   │   └── main.rs                 # Application entry point
│   ├── Cargo.toml                  # Rust dependencies
│   └── tauri.conf.json            # Tauri configuration
├── 📁 docs/                        # Documentation
│   ├── 📁 api/                     # API documentation
│   ├── 📁 user-guide/             # User guides
│   ├── 📁 technical/              # Technical documentation
│   └── 📁 onboarding/             # Developer onboarding
├── 📁 public/                      # Static assets
├── package.json                    # Node.js dependencies and scripts
├── tailwind.config.js             # Tailwind CSS configuration
├── tsconfig.json                  # TypeScript configuration
└── vite.config.ts                 # Vite build configuration
```

### **Key Technologies and Concepts**

#### **Frontend Stack**
- **React 18**: Component-based UI framework
- **TypeScript**: Type-safe JavaScript
- **Vite**: Fast build tool and dev server
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: High-quality component library
- **Lucide React**: Icon library

#### **Backend Stack**
- **Rust**: Systems programming language
- **Tauri**: Cross-platform desktop app framework
- **Serde**: Serialization/deserialization
- **Tokio**: Async runtime for Rust
- **Blake3**: Fast cryptographic hashing

#### **Architecture Patterns**
- **Unified Scanning**: Single-pass detection across all modules
- **Granular Configuration**: User-configurable detection types and processing methods
- **Shared Caching**: Optimized file reading and processing
- **Parallel Processing**: Concurrent execution of detection modules

## 🧪 **Development Workflow**

### **Daily Development Commands**

```bash
# Start development server
npm run tauri dev

# Run frontend only (for UI development)
npm run dev

# Build for production
npm run tauri build

# Run tests
npm test
cargo test

# Format code
npm run format
cargo fmt

# Lint code
npm run lint
cargo clippy
```

### **Git Workflow**

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add granular scan configuration"

# Push and create PR
git push origin feature/your-feature-name
# Create PR via GitHub UI
```

### **Testing Strategy**

#### **Frontend Testing**
```bash
# Run React component tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

#### **Backend Testing**
```bash
# Run all Rust tests
cargo test

# Run specific test module
cargo test unified::scan_configuration

# Run tests with output
cargo test -- --nocapture
```

## 📚 **Essential Documentation**

### **Must-Read Documents (First Week)**

1. **[../README.md](../../README.md)** - Project overview and features
2. **[../technical/UNIFIED_SCANNING_ARCHITECTURE.md](../technical/UNIFIED_SCANNING_ARCHITECTURE.md)** - Core architecture
3. **[../api/UNIFIED_SCANNING_API.md](../api/UNIFIED_SCANNING_API.md)** - API reference
4. **[CONTRIBUTING_GUIDELINES.md](CONTRIBUTING_GUIDELINES.md)** - Code standards and workflow

### **Architecture Deep Dive (Second Week)**

5. **[../analysis/PERFORMANCE_IMPACT_ANALYSIS.md](../analysis/PERFORMANCE_IMPACT_ANALYSIS.md)** - Performance analysis
6. **[../user-guide/GRANULAR_SCAN_CONFIGURATION_GUIDE.md](../user-guide/GRANULAR_SCAN_CONFIGURATION_GUIDE.md)** - Configuration system
7. **[../specifications/UNIFIED_SCANNING_SCOPE.md](../specifications/UNIFIED_SCANNING_SCOPE.md)** - System scope

## 🔍 **Common Development Tasks**

### **Adding a New Detection Type**

1. **Define the pattern** in `src-tauri/src/privacy/privacy_patterns.rs`
2. **Add configuration option** in `src-tauri/src/unified/scan_configuration.rs`
3. **Update UI components** in `src/components/ScanConfiguration.tsx`
4. **Add tests** for the new detection type
5. **Update documentation** with the new capability

### **Creating a New Scan Profile**

1. **Add profile enum** in `src-tauri/src/unified/scan_profiles.rs`
2. **Implement configuration** in the `ScanProfileManager`
3. **Add performance benchmarks** for the new profile
4. **Update UI** to include the new profile option
5. **Document the use case** and performance characteristics

### **Optimizing Performance**

1. **Profile the code** using `cargo flamegraph` or similar tools
2. **Identify bottlenecks** in the scanning pipeline
3. **Implement optimizations** while maintaining accuracy
4. **Benchmark improvements** and update documentation
5. **Add performance tests** to prevent regressions

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Build Failures**

**Issue**: `error: Microsoft Visual C++ 14.0 is required` (Windows)
```bash
# Solution: Install Visual Studio Build Tools
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

**Issue**: `error: linking with 'cc' failed` (Linux)
```bash
# Solution: Install build essentials
sudo apt install build-essential
```

**Issue**: `error: failed to run custom build command for 'openssl-sys'`
```bash
# Solution: Install OpenSSL development libraries
# Ubuntu/Debian:
sudo apt install libssl-dev
# macOS:
brew install openssl
```

#### **Runtime Issues**

**Issue**: Tauri app fails to start
```bash
# Check for missing dependencies
npm run tauri info

# Clear cache and reinstall
rm -rf node_modules target
npm install
```

**Issue**: Hot reload not working
```bash
# Restart development server
npm run tauri dev
```

### **Getting Help**

- **Documentation**: Check the [Documentation Index](../DOCUMENTATION_INDEX.md)
- **GitHub Issues**: [Create an issue](https://github.com/LogicPTK/PrivacyAI/issues) with detailed description
- **GitHub Discussions**: [Ask questions](https://github.com/LogicPTK/PrivacyAI/discussions) in the community
- **Code Review**: Request review from team members for complex changes

## ✅ **Setup Verification Checklist**

- [ ] Repository cloned and correct branch checked out
- [ ] Node.js v18+ installed and verified
- [ ] Rust latest stable installed and verified
- [ ] Platform-specific build tools installed
- [ ] Dependencies installed successfully (`npm install`)
- [ ] Development server starts (`npm run tauri dev`)
- [ ] VS Code extensions installed and configured
- [ ] Essential documentation read
- [ ] Git workflow understood
- [ ] First test commit made

**Congratulations! You're ready to contribute to PrivacyAI! 🎉**

For questions or issues during setup, please create a GitHub issue with the `setup` label.
