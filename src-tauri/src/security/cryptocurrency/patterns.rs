/// Cryptocurrency pattern definitions for detection
/// 
/// This module contains comprehensive regex patterns for detecting various
/// cryptocurrency-related sensitive data including private keys, addresses,
/// and exchange credentials.

use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// Types of cryptocurrency patterns
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CryptoPatternType {
    // Private Keys
    BitcoinWIF,
    BitcoinWIFCompressed,
    EthereumPrivateKey,
    RawPrivateKey,
    Base58PrivateKey,
    
    // Cryptocurrency Addresses
    BitcoinLegacy,
    BitcoinSegWit,
    BitcoinBech32,
    EthereumAddress,
    MoneroAddress,
    LitecoinAdd<PERSON>,

    // Cardano ecosystem
    CardanoAddress,
    WorldMobileToken,
    AdaHandle,

    // Blockchain domains
    UnstoppableDomains,

    // Exchange API Credentials
    BinanceApiKey,
    BinanceSecret,
    CoinbaseApiKey,
    CoinbaseSecret,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>raken<PERSON><PERSON><PERSON>,
    <PERSON>finex<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ric<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>ric<PERSON><PERSON><PERSON><PERSON><PERSON>,
}

/// Cryptocurrency pattern definition
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct CryptoPattern {
    pub pattern_type: CryptoPatternType,
    pub regex: &'static str,
    pub description: &'static str,
    pub severity: CryptoSeverity,
    pub requires_validation: bool,
}

/// Severity levels for cryptocurrency findings
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CryptoSeverity {
    Critical,  // Private keys, seeds, exchange secrets
    High,      // Addresses, API keys
    Medium,    // Potential crypto-related data
}

/// Comprehensive cryptocurrency patterns
pub const CRYPTO_PATTERNS: &[CryptoPattern] = &[
    // === PRIVATE KEY PATTERNS ===
    
    // Bitcoin WIF (Wallet Import Format) - Uncompressed
    CryptoPattern {
        pattern_type: CryptoPatternType::BitcoinWIF,
        regex: r"\b[5][1-9A-HJ-NP-Za-km-z]{50}\b",
        description: "Bitcoin WIF private key (uncompressed)",
        severity: CryptoSeverity::Critical,
        requires_validation: true,
    },
    
    // Bitcoin WIF (Wallet Import Format) - Compressed
    CryptoPattern {
        pattern_type: CryptoPatternType::BitcoinWIFCompressed,
        regex: r"\b[KL][1-9A-HJ-NP-Za-km-z]{51}\b",
        description: "Bitcoin WIF private key (compressed)",
        severity: CryptoSeverity::Critical,
        requires_validation: true,
    },
    
    // Ethereum private key (hex format)
    CryptoPattern {
        pattern_type: CryptoPatternType::EthereumPrivateKey,
        regex: r"\b0x[a-fA-F0-9]{64}\b",
        description: "Ethereum private key (hex format)",
        severity: CryptoSeverity::Critical,
        requires_validation: true,
    },
    
    // Raw 256-bit private key (hex)
    CryptoPattern {
        pattern_type: CryptoPatternType::RawPrivateKey,
        regex: r"\b[a-fA-F0-9]{64}\b",
        description: "Raw 256-bit private key (hex)",
        severity: CryptoSeverity::Critical,
        requires_validation: true,
    },
    
    // Base58 encoded private keys (various lengths)
    CryptoPattern {
        pattern_type: CryptoPatternType::Base58PrivateKey,
        regex: r"\b[1-9A-HJ-NP-Za-km-z]{44,88}\b",
        description: "Base58 encoded private key",
        severity: CryptoSeverity::Critical,
        requires_validation: true,
    },
    
    // === CRYPTOCURRENCY ADDRESS PATTERNS ===
    
    // Bitcoin Legacy addresses (P2PKH)
    CryptoPattern {
        pattern_type: CryptoPatternType::BitcoinLegacy,
        regex: r"\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b",
        description: "Bitcoin Legacy address (P2PKH)",
        severity: CryptoSeverity::High,
        requires_validation: true,
    },
    
    // Bitcoin SegWit addresses (P2SH)
    CryptoPattern {
        pattern_type: CryptoPatternType::BitcoinSegWit,
        regex: r"\b3[a-km-zA-HJ-NP-Z1-9]{25,34}\b",
        description: "Bitcoin SegWit address (P2SH)",
        severity: CryptoSeverity::High,
        requires_validation: true,
    },
    
    // Bitcoin Bech32 addresses (P2WPKH/P2WSH)
    CryptoPattern {
        pattern_type: CryptoPatternType::BitcoinBech32,
        regex: r"\bbc1[a-z0-9]{39,59}\b",
        description: "Bitcoin Bech32 address (P2WPKH/P2WSH)",
        severity: CryptoSeverity::High,
        requires_validation: true,
    },
    
    // Ethereum addresses
    CryptoPattern {
        pattern_type: CryptoPatternType::EthereumAddress,
        regex: r"\b0x[a-fA-F0-9]{40}\b",
        description: "Ethereum address",
        severity: CryptoSeverity::High,
        requires_validation: true,
    },
    
    // Monero addresses
    CryptoPattern {
        pattern_type: CryptoPatternType::MoneroAddress,
        regex: r"\b4[0-9AB][1-9A-HJ-NP-Za-km-z]{93}\b",
        description: "Monero address",
        severity: CryptoSeverity::High,
        requires_validation: false, // Complex validation, skip for now
    },
    
    // Litecoin addresses
    CryptoPattern {
        pattern_type: CryptoPatternType::LitecoinAddress,
        regex: r"\b[LM3][a-km-zA-HJ-NP-Z1-9]{26,33}\b",
        description: "Litecoin address",
        severity: CryptoSeverity::High,
        requires_validation: true,
    },
    
    // === EXCHANGE API CREDENTIAL PATTERNS ===
    
    // Binance API Key
    CryptoPattern {
        pattern_type: CryptoPatternType::BinanceApiKey,
        regex: r"(?i)(?:binance.*api.*key|api.*key.*binance)[\s:=]+[a-zA-Z0-9]{64}",
        description: "Binance API key",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Binance Secret
    CryptoPattern {
        pattern_type: CryptoPatternType::BinanceSecret,
        regex: r"(?i)(?:binance.*secret|secret.*binance)[\s:=]+[a-zA-Z0-9]{64}",
        description: "Binance API secret",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Coinbase API Key
    CryptoPattern {
        pattern_type: CryptoPatternType::CoinbaseApiKey,
        regex: r"(?i)(?:coinbase.*api.*key|api.*key.*coinbase)[\s:=]+[a-zA-Z0-9-]{36}",
        description: "Coinbase API key",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Coinbase Secret
    CryptoPattern {
        pattern_type: CryptoPatternType::CoinbaseSecret,
        regex: r"(?i)(?:coinbase.*secret|secret.*coinbase)[\s:=]+[a-zA-Z0-9+/]{88}",
        description: "Coinbase API secret",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Kraken API Key
    CryptoPattern {
        pattern_type: CryptoPatternType::KrakenApiKey,
        regex: r"(?i)(?:kraken.*api.*key|api.*key.*kraken)[\s:=]+[a-zA-Z0-9+/]{56}",
        description: "Kraken API key",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Kraken Secret
    CryptoPattern {
        pattern_type: CryptoPatternType::KrakenSecret,
        regex: r"(?i)(?:kraken.*secret|secret.*kraken)[\s:=]+[a-zA-Z0-9+/]{88}",
        description: "Kraken API secret",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Bitfinex API Key
    CryptoPattern {
        pattern_type: CryptoPatternType::BitfinexApiKey,
        regex: r"(?i)(?:bitfinex.*api.*key|api.*key.*bitfinex)[\s:=]+[a-zA-Z0-9]{43}",
        description: "Bitfinex API key",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },
    
    // Generic API Key patterns
    CryptoPattern {
        pattern_type: CryptoPatternType::GenericApiKey,
        regex: r"(?i)(?:api[_\s]?key|access[_\s]?key)[\s:=]+[a-zA-Z0-9+/]{32,}",
        description: "Generic API key",
        severity: CryptoSeverity::High,
        requires_validation: false,
    },
    
    // Generic API Secret patterns
    CryptoPattern {
        pattern_type: CryptoPatternType::GenericApiSecret,
        regex: r"(?i)(?:api[_\s]?secret|secret[_\s]?key)[\s:=]+[a-zA-Z0-9+/]{32,}",
        description: "Generic API secret",
        severity: CryptoSeverity::Critical,
        requires_validation: false,
    },

    // === CARDANO ECOSYSTEM PATTERNS ===

    // Cardano addresses (Bech32-based)
    CryptoPattern {
        pattern_type: CryptoPatternType::CardanoAddress,
        regex: r"\baddr1[a-z0-9]{98,}\b",
        description: "Cardano address (Bech32 format)",
        severity: CryptoSeverity::High,
        requires_validation: true,
    },

    // World Mobile Token (Cardano native token)
    CryptoPattern {
        pattern_type: CryptoPatternType::WorldMobileToken,
        regex: r"(?i)(?:world.*mobile.*token|wmt.*token|wmt.*address)",
        description: "World Mobile Token (WMT) reference",
        severity: CryptoSeverity::High,
        requires_validation: false,
    },

    // AdaHandle (Cardano handles)
    CryptoPattern {
        pattern_type: CryptoPatternType::AdaHandle,
        regex: r"\$[a-zA-Z0-9_-]{1,15}\b",
        description: "Cardano AdaHandle ($handle format)",
        severity: CryptoSeverity::Medium,
        requires_validation: false,
    },

    // === BLOCKCHAIN DOMAINS ===

    // Unstoppable Domains
    CryptoPattern {
        pattern_type: CryptoPatternType::UnstoppableDomains,
        regex: r"\b[a-zA-Z0-9-]+\.(?:crypto|nft|blockchain|bitcoin|wallet|x|888|dao|zil)\b",
        description: "Unstoppable Domains blockchain domain",
        severity: CryptoSeverity::Medium,
        requires_validation: false,
    },
];

/// Get patterns by type for efficient lookup
pub fn get_patterns_by_type() -> HashMap<CryptoPatternType, &'static CryptoPattern> {
    CRYPTO_PATTERNS.iter()
        .map(|pattern| (pattern.pattern_type.clone(), pattern))
        .collect()
}

/// Get patterns by severity level
pub fn get_patterns_by_severity(severity: CryptoSeverity) -> Vec<&'static CryptoPattern> {
    CRYPTO_PATTERNS.iter()
        .filter(|pattern| pattern.severity == severity)
        .collect()
}

/// Get all private key patterns
pub fn get_private_key_patterns() -> Vec<&'static CryptoPattern> {
    CRYPTO_PATTERNS.iter()
        .filter(|pattern| matches!(
            pattern.pattern_type,
            CryptoPatternType::BitcoinWIF |
            CryptoPatternType::BitcoinWIFCompressed |
            CryptoPatternType::EthereumPrivateKey |
            CryptoPatternType::RawPrivateKey |
            CryptoPatternType::Base58PrivateKey
        ))
        .collect()
}

/// Get all address patterns
pub fn get_address_patterns() -> Vec<&'static CryptoPattern> {
    CRYPTO_PATTERNS.iter()
        .filter(|pattern| matches!(
            pattern.pattern_type,
            CryptoPatternType::BitcoinLegacy |
            CryptoPatternType::BitcoinSegWit |
            CryptoPatternType::BitcoinBech32 |
            CryptoPatternType::EthereumAddress |
            CryptoPatternType::MoneroAddress |
            CryptoPatternType::LitecoinAddress
        ))
        .collect()
}

/// Get all exchange credential patterns
pub fn get_exchange_patterns() -> Vec<&'static CryptoPattern> {
    CRYPTO_PATTERNS.iter()
        .filter(|pattern| matches!(
            pattern.pattern_type,
            CryptoPatternType::BinanceApiKey |
            CryptoPatternType::BinanceSecret |
            CryptoPatternType::CoinbaseApiKey |
            CryptoPatternType::CoinbaseSecret |
            CryptoPatternType::KrakenApiKey |
            CryptoPatternType::KrakenSecret |
            CryptoPatternType::BitfinexApiKey |
            CryptoPatternType::GenericApiKey |
            CryptoPatternType::GenericApiSecret
        ))
        .collect()
}
