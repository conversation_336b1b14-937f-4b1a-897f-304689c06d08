/// Document Template Matching System
/// 
/// Uses OpenCV-based computer vision techniques to identify document types
/// through template matching, contour analysis, and layout pattern recognition.

use std::collections::HashMap;
use std::path::Path;
use serde::{Serialize, Deserialize};
use image::{D<PERSON>I<PERSON>, ImageBuffer, <PERSON><PERSON>};
use crate::privacy::balanced_error_reporting::{BalancedErrorReporter, LogLevel, ErrorReportingConfig};
// Using balanced error reporter instead of macros

/// Document template matching result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateMatchResult {
    /// Detected document type
    pub document_type: DocumentType,
    /// Confidence score (0.0-1.0)
    pub confidence: f64,
    /// Matching score for each template
    pub template_scores: HashMap<String, f64>,
    /// Detected layout features
    pub layout_features: LayoutFeatures,
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// Template matching details
    pub match_details: Vec<TemplateMatch>,
}

/// Individual template match
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TemplateMatch {
    /// Template name
    pub template_name: String,
    /// Match confidence
    pub confidence: f64,
    /// Bounding box (x, y, width, height)
    pub bounding_box: (u32, u32, u32, u32),
    /// Match location
    pub match_location: (u32, u32),
}

/// Document types that can be detected
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum DocumentType {
    /// Government-issued ID (driver's license, passport, etc.)
    GovernmentId,
    /// Financial documents (bank statements, tax forms, etc.)
    Financial,
    /// Medical records and forms
    Medical,
    /// Legal documents (contracts, agreements, etc.)
    Legal,
    /// Employment documents (resumes, job applications, etc.)
    Employment,
    /// Educational documents (transcripts, certificates, etc.)
    Educational,
    /// Insurance documents
    Insurance,
    /// Business forms and invoices
    Business,
    /// Personal correspondence
    Personal,
    /// Unknown or unclassified document
    Unknown,
}

/// Layout features detected in the document
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LayoutFeatures {
    /// Number of text blocks detected
    pub text_blocks: usize,
    /// Number of tables detected
    pub tables: usize,
    /// Number of forms/fields detected
    pub form_fields: usize,
    /// Header presence and characteristics
    pub header_info: HeaderInfo,
    /// Footer presence and characteristics
    pub footer_info: FooterInfo,
    /// Logo/seal detection
    pub logos_detected: Vec<LogoMatch>,
    /// Text density (characters per area)
    pub text_density: f64,
    /// Layout complexity score
    pub complexity_score: f64,
}

/// Header information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeaderInfo {
    /// Header detected
    pub present: bool,
    /// Header height ratio
    pub height_ratio: f64,
    /// Header text content (if available)
    pub text_content: Option<String>,
}

/// Footer information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FooterInfo {
    /// Footer detected
    pub present: bool,
    /// Footer height ratio
    pub height_ratio: f64,
    /// Footer text content (if available)
    pub text_content: Option<String>,
}

/// Logo/seal match
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogoMatch {
    /// Logo type/organization
    pub logo_type: String,
    /// Match confidence
    pub confidence: f64,
    /// Location (x, y, width, height)
    pub location: (u32, u32, u32, u32),
}

/// Template matching configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateMatchConfig {
    /// Minimum confidence threshold for matches
    pub min_confidence: f64,
    /// Maximum number of templates to match
    pub max_templates: usize,
    /// Enable multi-scale template matching
    pub multi_scale: bool,
    /// Scale factors for multi-scale matching
    pub scale_factors: Vec<f64>,
    /// Template matching method
    pub match_method: TemplateMatchMethod,
    /// Enable layout analysis
    pub enable_layout_analysis: bool,
    /// Enable logo detection
    pub enable_logo_detection: bool,
}

/// Template matching methods
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TemplateMatchMethod {
    /// Normalized cross-correlation
    NormalizedCrossCorrelation,
    /// Squared difference
    SquaredDifference,
    /// Correlation coefficient
    CorrelationCoefficient,
}

impl Default for TemplateMatchConfig {
    fn default() -> Self {
        Self {
            min_confidence: 0.7,
            max_templates: 10,
            multi_scale: true,
            scale_factors: vec![0.8, 0.9, 1.0, 1.1, 1.2],
            match_method: TemplateMatchMethod::NormalizedCrossCorrelation,
            enable_layout_analysis: true,
            enable_logo_detection: true,
        }
    }
}

/// Document Template Matcher
pub struct DocumentTemplateMatcher {
    /// Configuration
    config: TemplateMatchConfig,
    /// Template database
    templates: HashMap<String, TemplateData>,
    /// Balanced error reporter
    reporter: BalancedErrorReporter,
    /// Performance cache
    performance_cache: HashMap<String, u64>,
}

/// Template data
#[derive(Debug, Clone)]
pub struct TemplateData {
    /// Template image data
    pub image_data: Vec<u8>,
    /// Template dimensions
    pub dimensions: (u32, u32),
    /// Associated document type
    pub document_type: DocumentType,
    /// Template features
    pub features: Vec<TemplateFeature>,
    /// Template metadata
    pub metadata: TemplateMetadata,
}

/// Template feature
#[derive(Debug, Clone)]
pub struct TemplateFeature {
    /// Feature type
    pub feature_type: String,
    /// Feature location
    pub location: (u32, u32, u32, u32),
    /// Feature importance weight
    pub weight: f64,
}

/// Template metadata
#[derive(Debug, Clone)]
pub struct TemplateMetadata {
    /// Template name
    pub name: String,
    /// Template version
    pub version: String,
    /// Creation date
    pub created: String,
    /// Template description
    pub description: String,
    /// Expected accuracy
    pub expected_accuracy: f64,
}

impl DocumentTemplateMatcher {
    /// Create new template matcher
    pub fn new() -> Self {
        Self::with_config(TemplateMatchConfig::default())
    }
    
    /// Create new template matcher with configuration
    pub fn with_config(config: TemplateMatchConfig) -> Self {
        let reporter = BalancedErrorReporter::new(ErrorReportingConfig::default());
        
        Self {
            config,
            templates: HashMap::new(),
            reporter,
            performance_cache: HashMap::new(),
        }
    }
    
    /// Load template database
    pub fn load_templates(&mut self, template_dir: &Path) -> Result<usize, Box<dyn std::error::Error + Send + Sync>> {
        self.reporter.start_operation("template_loading", 1);
        self.reporter.log(LogLevel::Info, &format!("Loading templates from {:?}", template_dir), "template_matcher");
        
        let start_time = std::time::Instant::now();
        
        // Initialize with built-in templates for common document types
        self.initialize_builtin_templates()?;
        
        // Load custom templates from directory if it exists
        if template_dir.exists() {
            self.load_custom_templates(template_dir)?;
        }
        
        let loading_time = start_time.elapsed().as_millis() as u64;
        self.reporter.log_performance("template_loading", loading_time, "template_matcher");
        
        let template_count = self.templates.len();
        self.reporter.log(LogLevel::Info, &format!("Loaded {} templates", template_count), "template_matcher");
        
        let _report = self.reporter.end_operation();
        
        Ok(template_count)
    }
    
    /// Initialize built-in templates
    fn initialize_builtin_templates(&mut self) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Create synthetic templates for common document types
        let template_configs = vec![
            ("government_id_template", DocumentType::GovernmentId, "Government ID template with photo and text fields"),
            ("bank_statement_template", DocumentType::Financial, "Bank statement with header, transactions, and footer"),
            ("medical_form_template", DocumentType::Medical, "Medical form with patient info and checkboxes"),
            ("legal_contract_template", DocumentType::Legal, "Legal contract with signature blocks"),
            ("resume_template", DocumentType::Employment, "Resume with contact info and sections"),
            ("transcript_template", DocumentType::Educational, "Academic transcript with grades"),
            ("insurance_policy_template", DocumentType::Insurance, "Insurance policy with coverage details"),
            ("invoice_template", DocumentType::Business, "Business invoice with itemized charges"),
        ];
        
        for (name, doc_type, description) in template_configs {
            let template_data = self.create_synthetic_template(name, doc_type, description)?;
            self.templates.insert(name.to_string(), template_data);
        }
        
        // Debug logging handled by balanced error reporter
        
        Ok(())
    }
    
    /// Create synthetic template for testing
    fn create_synthetic_template(
        &self, 
        name: &str, 
        doc_type: DocumentType, 
        description: &str
    ) -> Result<TemplateData, Box<dyn std::error::Error + Send + Sync>> {
        // Create a simple synthetic template (in production, these would be real template images)
        let width = 800u32;
        let height = 1000u32;
        
        // Create synthetic image data (grayscale)
        let image_data = vec![128u8; (width * height) as usize]; // Gray background
        
        // Define template features based on document type
        let features = match doc_type {
            DocumentType::GovernmentId => vec![
                TemplateFeature {
                    feature_type: "photo_area".to_string(),
                    location: (50, 50, 150, 200),
                    weight: 0.8,
                },
                TemplateFeature {
                    feature_type: "text_fields".to_string(),
                    location: (220, 50, 500, 300),
                    weight: 0.7,
                },
            ],
            DocumentType::Financial => vec![
                TemplateFeature {
                    feature_type: "header_logo".to_string(),
                    location: (50, 20, 200, 80),
                    weight: 0.9,
                },
                TemplateFeature {
                    feature_type: "transaction_table".to_string(),
                    location: (50, 200, 700, 600),
                    weight: 0.8,
                },
            ],
            _ => vec![
                TemplateFeature {
                    feature_type: "header".to_string(),
                    location: (50, 20, 700, 100),
                    weight: 0.6,
                },
                TemplateFeature {
                    feature_type: "content".to_string(),
                    location: (50, 120, 700, 800),
                    weight: 0.7,
                },
            ],
        };
        
        let metadata = TemplateMetadata {
            name: name.to_string(),
            version: "1.0".to_string(),
            created: chrono::Utc::now().to_rfc3339(),
            description: description.to_string(),
            expected_accuracy: 0.85,
        };
        
        Ok(TemplateData {
            image_data,
            dimensions: (width, height),
            document_type: doc_type,
            features,
            metadata,
        })
    }
    
    /// Load custom templates from directory
    fn load_custom_templates(&mut self, _template_dir: &Path) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Placeholder for loading custom templates from files
        // In production, this would load actual template images and metadata
        // Debug logging handled by balanced error reporter
        Ok(())
    }

    /// Match document against templates
    pub async fn match_document(&mut self, image: &DynamicImage) -> Result<TemplateMatchResult, Box<dyn std::error::Error + Send + Sync>> {
        self.reporter.start_operation("template_matching", 1);
        let start_time = std::time::Instant::now();

        self.reporter.log(LogLevel::Info,
            &format!("Starting template matching for {}x{} image", image.width(), image.height()),
            "template_matcher");

        // Convert image to grayscale for processing
        let gray_image = image.to_luma8();

        // Perform template matching
        let template_scores = self.match_all_templates(&gray_image).await?;

        // Analyze layout features
        let layout_features = if self.config.enable_layout_analysis {
            self.analyze_layout(&gray_image).await?
        } else {
            LayoutFeatures::default()
        };

        // Determine best match
        let (document_type, confidence, match_details) = self.determine_best_match(&template_scores, &layout_features)?;

        let processing_time = start_time.elapsed().as_millis() as u64;
        self.reporter.log_performance("template_matching", processing_time, "template_matcher");

        let result = TemplateMatchResult {
            document_type: document_type.clone(),
            confidence,
            template_scores,
            layout_features,
            processing_time_ms: processing_time,
            match_details,
        };

        self.reporter.log(LogLevel::Info,
            &format!("Template matching completed: {:?} (confidence: {:.2})", document_type, confidence),
            "template_matcher");

        let _report = self.reporter.end_operation();

        Ok(result)
    }

    /// Match against all templates
    async fn match_all_templates(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<HashMap<String, f64>, Box<dyn std::error::Error + Send + Sync>> {
        let mut scores = HashMap::new();

        for (template_name, template_data) in &self.templates {
            let score = self.match_single_template(image, template_data).await?;
            scores.insert(template_name.clone(), score);

            // Debug logging handled by balanced error reporter
        }

        Ok(scores)
    }

    /// Match against single template
    async fn match_single_template(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>, template_data: &TemplateData) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        // Simplified template matching using image comparison
        // In production, this would use OpenCV template matching

        let image_area = (image.width() * image.height()) as f64;
        let template_area = (template_data.dimensions.0 * template_data.dimensions.1) as f64;

        // Simple size-based similarity (placeholder for real template matching)
        let size_similarity = 1.0 - ((image_area - template_area).abs() / image_area.max(template_area));

        // Feature-based scoring
        let feature_score = self.calculate_feature_score(image, template_data)?;

        // Combined score
        let combined_score = (size_similarity * 0.3 + feature_score * 0.7).clamp(0.0, 1.0);

        Ok(combined_score)
    }

    /// Calculate feature-based score
    fn calculate_feature_score(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>, template_data: &TemplateData) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let mut total_score = 0.0;
        let mut total_weight = 0.0;

        for feature in &template_data.features {
            let feature_score = self.evaluate_feature(image, feature)?;
            total_score += feature_score * feature.weight;
            total_weight += feature.weight;
        }

        if total_weight > 0.0 {
            Ok(total_score / total_weight)
        } else {
            Ok(0.0)
        }
    }

    /// Evaluate individual feature
    fn evaluate_feature(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>, feature: &TemplateFeature) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let (x, y, width, height) = feature.location;

        // Check if feature location is within image bounds
        if x + width > image.width() || y + height > image.height() {
            return Ok(0.0);
        }

        // Extract region of interest
        let roi_pixels: Vec<u8> = (y..y+height)
            .flat_map(|row| {
                (x..x+width).map(move |col| {
                    *image.get_pixel(col, row).0.first().unwrap_or(&0)
                })
            })
            .collect();

        // Calculate feature characteristics
        let avg_intensity = roi_pixels.iter().map(|&p| p as f64).sum::<f64>() / roi_pixels.len() as f64;
        let variance = roi_pixels.iter()
            .map(|&p| (p as f64 - avg_intensity).powi(2))
            .sum::<f64>() / roi_pixels.len() as f64;

        // Feature scoring based on type
        let score = match feature.feature_type.as_str() {
            "photo_area" => {
                // Photo areas typically have high variance
                (variance / 255.0).clamp(0.0, 1.0)
            },
            "text_fields" => {
                // Text areas have moderate variance
                let normalized_variance = variance / (255.0 * 255.0);
                if normalized_variance > 0.1 && normalized_variance < 0.6 {
                    0.8
                } else {
                    0.3
                }
            },
            "header_logo" => {
                // Headers often have distinct patterns
                if avg_intensity < 200.0 && variance > 1000.0 {
                    0.9
                } else {
                    0.4
                }
            },
            _ => {
                // Generic content scoring
                0.5
            }
        };

        Ok(score)
    }

    /// Analyze document layout
    async fn analyze_layout(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<LayoutFeatures, Box<dyn std::error::Error + Send + Sync>> {
        // Simplified layout analysis
        // In production, this would use OpenCV contour detection and analysis

        let width = image.width();
        let height = image.height();

        // Analyze header region (top 15% of image)
        let header_height = (height as f64 * 0.15) as u32;
        let header_info = self.analyze_header_region(image, header_height)?;

        // Analyze footer region (bottom 10% of image)
        let footer_height = (height as f64 * 0.10) as u32;
        let footer_info = self.analyze_footer_region(image, height - footer_height, footer_height)?;

        // Estimate text density
        let text_density = self.calculate_text_density(image)?;

        // Calculate complexity score
        let complexity_score = self.calculate_complexity_score(image)?;

        Ok(LayoutFeatures {
            text_blocks: self.estimate_text_blocks(image)?,
            tables: self.estimate_tables(image)?,
            form_fields: self.estimate_form_fields(image)?,
            header_info,
            footer_info,
            logos_detected: Vec::new(), // Placeholder
            text_density,
            complexity_score,
        })
    }

    /// Analyze header region
    fn analyze_header_region(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>, header_height: u32) -> Result<HeaderInfo, Box<dyn std::error::Error + Send + Sync>> {
        let header_pixels: Vec<u8> = (0..header_height)
            .flat_map(|y| {
                (0..image.width()).map(move |x| {
                    *image.get_pixel(x, y).0.first().unwrap_or(&255)
                })
            })
            .collect();

        let avg_intensity = header_pixels.iter().map(|&p| p as f64).sum::<f64>() / header_pixels.len() as f64;
        let variance = header_pixels.iter()
            .map(|&p| (p as f64 - avg_intensity).powi(2))
            .sum::<f64>() / header_pixels.len() as f64;

        // Header detected if there's significant content (low average intensity or high variance)
        let present = avg_intensity < 240.0 || variance > 500.0;
        let height_ratio = header_height as f64 / image.height() as f64;

        Ok(HeaderInfo {
            present,
            height_ratio,
            text_content: None, // Would be populated by OCR in production
        })
    }

    /// Analyze footer region
    fn analyze_footer_region(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>, start_y: u32, footer_height: u32) -> Result<FooterInfo, Box<dyn std::error::Error + Send + Sync>> {
        let footer_pixels: Vec<u8> = (start_y..start_y + footer_height)
            .flat_map(|y| {
                (0..image.width()).map(move |x| {
                    *image.get_pixel(x, y).0.first().unwrap_or(&255)
                })
            })
            .collect();

        let avg_intensity = footer_pixels.iter().map(|&p| p as f64).sum::<f64>() / footer_pixels.len() as f64;
        let variance = footer_pixels.iter()
            .map(|&p| (p as f64 - avg_intensity).powi(2))
            .sum::<f64>() / footer_pixels.len() as f64;

        let present = avg_intensity < 240.0 || variance > 300.0;
        let height_ratio = footer_height as f64 / image.height() as f64;

        Ok(FooterInfo {
            present,
            height_ratio,
            text_content: None,
        })
    }

    /// Calculate text density
    fn calculate_text_density(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        let total_pixels = (image.width() * image.height()) as f64;
        let dark_pixels = image.pixels()
            .filter(|pixel| pixel.0[0] < 128) // Consider pixels darker than middle gray as text
            .count() as f64;

        Ok(dark_pixels / total_pixels)
    }

    /// Calculate complexity score
    fn calculate_complexity_score(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
        // Calculate edge density as a measure of complexity
        let mut edge_count = 0;
        let width = image.width();
        let height = image.height();

        for y in 1..height-1 {
            for x in 1..width-1 {
                let center = image.get_pixel(x, y).0[0] as i32;
                let neighbors = [
                    image.get_pixel(x-1, y).0[0] as i32,
                    image.get_pixel(x+1, y).0[0] as i32,
                    image.get_pixel(x, y-1).0[0] as i32,
                    image.get_pixel(x, y+1).0[0] as i32,
                ];

                let max_diff = neighbors.iter()
                    .map(|&n| (center - n).abs())
                    .max()
                    .unwrap_or(0);

                if max_diff > 30 { // Edge threshold
                    edge_count += 1;
                }
            }
        }

        let total_pixels = ((width.saturating_sub(2)) * (height.saturating_sub(2))) as f64;
        Ok(edge_count as f64 / total_pixels)
    }

    /// Estimate number of text blocks
    fn estimate_text_blocks(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<usize, Box<dyn std::error::Error + Send + Sync>> {
        // Simplified text block estimation
        // In production, this would use connected component analysis
        let text_density = self.calculate_text_density(image)?;

        // Estimate based on text density and image size
        let area = (image.width() * image.height()) as f64;
        let estimated_blocks = ((text_density * area) / 50000.0).ceil() as usize;

        Ok(estimated_blocks.max(1).min(20)) // Reasonable bounds
    }

    /// Estimate number of tables
    fn estimate_tables(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<usize, Box<dyn std::error::Error + Send + Sync>> {
        // Simplified table detection
        // Look for horizontal and vertical line patterns
        let width = image.width();
        let height = image.height();

        let mut horizontal_lines = 0;
        let mut vertical_lines = 0;

        // Check for horizontal lines
        for y in (0..height).step_by(10) {
            let mut line_pixels = 0;
            for x in 0..width {
                if image.get_pixel(x, y).0[0] < 100 {
                    line_pixels += 1;
                }
            }
            if line_pixels > width / 2 {
                horizontal_lines += 1;
            }
        }

        // Check for vertical lines
        for x in (0..width).step_by(10) {
            let mut line_pixels = 0;
            for y in 0..height {
                if image.get_pixel(x, y).0[0] < 100 {
                    line_pixels += 1;
                }
            }
            if line_pixels > height / 2 {
                vertical_lines += 1;
            }
        }

        // Estimate tables based on line intersections
        let estimated_tables = if horizontal_lines > 2 && vertical_lines > 2 {
            ((horizontal_lines * vertical_lines) / 20).max(1)
        } else {
            0
        };

        Ok(estimated_tables.min(5)) // Reasonable upper bound
    }

    /// Estimate number of form fields
    fn estimate_form_fields(&self, image: &ImageBuffer<Luma<u8>, Vec<u8>>) -> Result<usize, Box<dyn std::error::Error + Send + Sync>> {
        // Simplified form field detection
        // Look for rectangular regions with borders
        let complexity = self.calculate_complexity_score(image)?;
        let text_density = self.calculate_text_density(image)?;

        // Estimate based on complexity and text density
        let estimated_fields = if complexity > 0.1 && text_density > 0.05 && text_density < 0.3 {
            ((complexity * 100.0) as usize).max(1).min(15)
        } else {
            0
        };

        Ok(estimated_fields)
    }

    /// Determine best match from template scores
    fn determine_best_match(
        &self,
        template_scores: &HashMap<String, f64>,
        layout_features: &LayoutFeatures
    ) -> Result<(DocumentType, f64, Vec<TemplateMatch>), Box<dyn std::error::Error + Send + Sync>> {

        let mut best_score = 0.0;
        let mut best_type = DocumentType::Unknown;
        let mut match_details = Vec::new();

        for (template_name, &score) in template_scores {
            if score > best_score && score >= self.config.min_confidence {
                best_score = score;

                // Get document type from template
                if let Some(template_data) = self.templates.get(template_name) {
                    best_type = template_data.document_type.clone();

                    // Create match detail
                    let match_detail = TemplateMatch {
                        template_name: template_name.clone(),
                        confidence: score,
                        bounding_box: (0, 0, template_data.dimensions.0, template_data.dimensions.1),
                        match_location: (0, 0),
                    };
                    match_details.push(match_detail);
                }
            }
        }

        // Apply layout-based adjustments
        let adjusted_score = self.apply_layout_adjustments(best_score, &best_type, layout_features);

        Ok((best_type, adjusted_score, match_details))
    }

    /// Apply layout-based score adjustments
    fn apply_layout_adjustments(&self, base_score: f64, doc_type: &DocumentType, layout: &LayoutFeatures) -> f64 {
        let mut adjusted_score = base_score;

        match doc_type {
            DocumentType::GovernmentId => {
                // Government IDs typically have headers and specific layout
                if layout.header_info.present {
                    adjusted_score += 0.1;
                }
                if layout.form_fields > 0 {
                    adjusted_score += 0.05;
                }
            },
            DocumentType::Financial => {
                // Financial documents often have tables and headers
                if layout.tables > 0 {
                    adjusted_score += 0.15;
                }
                if layout.header_info.present && layout.footer_info.present {
                    adjusted_score += 0.1;
                }
            },
            DocumentType::Medical => {
                // Medical forms typically have many form fields
                if layout.form_fields > 5 {
                    adjusted_score += 0.2;
                }
            },
            DocumentType::Legal => {
                // Legal documents are typically text-heavy
                if layout.text_density > 0.2 {
                    adjusted_score += 0.1;
                }
                if layout.text_blocks > 3 {
                    adjusted_score += 0.05;
                }
            },
            _ => {
                // Generic adjustments
                if layout.complexity_score > 0.1 {
                    adjusted_score += 0.05;
                }
            }
        }

        adjusted_score.clamp(0.0, 1.0)
    }

    /// Get performance statistics
    pub fn get_performance_stats(&self) -> HashMap<String, u64> {
        self.performance_cache.clone()
    }

    /// Update configuration
    pub fn update_config(&mut self, config: TemplateMatchConfig) {
        self.config = config;
    }

    /// Get current configuration
    pub fn get_config(&self) -> &TemplateMatchConfig {
        &self.config
    }
}

impl Default for LayoutFeatures {
    fn default() -> Self {
        Self {
            text_blocks: 0,
            tables: 0,
            form_fields: 0,
            header_info: HeaderInfo {
                present: false,
                height_ratio: 0.0,
                text_content: None,
            },
            footer_info: FooterInfo {
                present: false,
                height_ratio: 0.0,
                text_content: None,
            },
            logos_detected: Vec::new(),
            text_density: 0.0,
            complexity_score: 0.0,
        }
    }
}
