/* Enhanced Analytics Dashboard with Recharts Styling */

.analytics-dashboard {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8fafc;
    min-height: 100vh;
}

.analytics-dashboard.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dashboard-header h2 {
    margin: 0;
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
}

.dashboard-header p {
    margin: 4px 0 0 0;
    color: #64748b;
    font-size: 1rem;
}

/* Time Range Selector */
.time-range-selector {
    display: flex;
    gap: 4px;
    background: #f1f5f9;
    padding: 4px;
    border-radius: 8px;
}

.range-button {
    padding: 8px 16px;
    background: transparent;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.range-button:hover {
    background: #e2e8f0;
    color: #475569;
}

.range-button.active {
    background: white;
    color: #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.metric-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.metric-icon {
    padding: 8px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
}

.metric-trend.positive {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
}

.metric-trend.negative {
    color: #dc2626;
    background: rgba(220, 38, 38, 0.1);
}

.metric-content {
    text-align: left;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.metric-title {
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;
}

.chart-container {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.chart-container.full-width {
    grid-column: 1 / -1;
}

.chart-container h3 {
    margin: 0 0 20px 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

/* Recharts Custom Styling */
.recharts-wrapper {
    font-family: inherit;
}

.recharts-cartesian-axis-tick-value {
    font-size: 12px;
    fill: #64748b;
}

.recharts-legend-wrapper {
    font-size: 14px;
}

.recharts-tooltip-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.recharts-default-tooltip {
    background: white !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

.recharts-tooltip-label {
    color: #1e293b !important;
    font-weight: 600 !important;
    margin-bottom: 4px !important;
}

.recharts-tooltip-item {
    color: #374151 !important;
}

/* Pie Chart Labels */
.recharts-pie-label-text {
    font-size: 12px;
    font-weight: 500;
    fill: #1e293b;
}

/* Bar Chart Styling */
.recharts-bar {
    transition: all 0.2s ease;
}

.recharts-bar:hover {
    opacity: 0.8;
}

/* Line Chart Styling */
.recharts-line-dot {
    transition: all 0.2s ease;
}

.recharts-active-dot {
    stroke-width: 2;
    stroke: white;
}

/* Area Chart Styling */
.recharts-area {
    transition: all 0.2s ease;
}

/* Grid Styling */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
    stroke: #f1f5f9;
    stroke-dasharray: 3 3;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-container.full-width {
        grid-column: 1;
    }
}

@media (max-width: 768px) {
    .analytics-dashboard {
        padding: 16px;
    }
    
    .dashboard-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
        padding: 16px;
    }
    
    .dashboard-header h2 {
        font-size: 1.5rem;
    }
    
    .time-range-selector {
        width: 100%;
        justify-content: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .chart-container {
        padding: 16px;
    }
    
    .metric-card {
        padding: 16px;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .range-button {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
    
    .metric-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .chart-container h3 {
        font-size: 1rem;
    }
}

/* Animation for chart loading */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chart-container {
    animation: fadeInUp 0.5s ease-out;
}

.metric-card {
    animation: fadeInUp 0.5s ease-out;
}

/* Custom scrollbar for overflow areas */
.chart-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.chart-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.chart-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
