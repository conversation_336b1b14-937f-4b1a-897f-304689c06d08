//! Security modules for privacy and sensitive data detection
//! 
//! This module contains components for detecting and analyzing privacy-sensitive
//! data using pattern matching and validation algorithms.

pub mod pattern_matcher;
pub mod sensitive_data_detector;
pub mod cryptocurrency;
pub mod corruption_detector;
pub mod user_settings;
pub mod custom_patterns;
pub mod pattern_templates;
pub mod integrated_detector;
pub mod secure_operations;
pub mod secure_operations_commands;

// Re-export commonly used types
pub use pattern_matcher::{PatternMatcher, SensitiveDataType, SeverityLevel, DetectionResult};
pub use sensitive_data_detector::SensitiveDataDetector;
pub use cryptocurrency::{
    CryptocurrencyDetector, CryptocurrencyFinding, CryptocurrencyType,
    CryptoValidationResult, CryptoValidationError
};
pub use corruption_detector::{
    CorruptionDetector, CorruptionDetectionResult, CorruptionType,
    CorruptionDetectionError
};
pub use user_settings::{
    UserSettingsManager, UserSettings, PerformanceMode, DetectionMethodSettings,
    SensitivitySettings, FileFilterSettings, SettingsError
};
pub use custom_patterns::{
    CustomPatternManager, CustomPattern, CustomPatternType, CustomSeverity,
    PatternTestCase, PatternTestResult, CustomPatternError, PatternValidationConfig,
    PatternPerformanceStats
};
pub use pattern_templates::{
    PatternTemplateManager, PatternTemplate, TemplateDifficulty
};
pub use integrated_detector::{
    IntegratedDetector, IntegratedDetectionResult, UnifiedFinding,
    UnifiedFindingType, UnifiedSeverity, IntegratedDetectionError
};
pub use secure_operations::{
    SecureFileOperations, SecureOperationsConfig, EncryptionType, CompressionLevel,
    SecureDeletionReport, SecureArchiveResult, PrivacyWorkflow, SecureOperationsError
};
pub use secure_operations_commands::{
    SecureOperationsState, PrivacyWorkflowState, CreateArchiveRequest, SecureDeletionRequest,
    PrivacyScanRequest, SecureOperationsResponse
};
