import { useState, useMemo } from 'react';
import { ArrowLef<PERSON>, Filter, Download, CheckSquare, Square, Scan, AlertTriangle, Shield, FileX } from 'lucide-react';
import * as Checkbox from '@radix-ui/react-checkbox';
import { CheckIcon } from 'lucide-react';
import type { <PERSON>an<PERSON><PERSON><PERSON> } from './types';
import FileActionsPanel from './FileActionsPanel';

interface ResultsDisplayScreenProps {
  results: ScanResult[];
  selectedFiles: string[];
  onFileSelectionChange: (selectedFiles: string[]) => void;
  isScanning: boolean;
  onBack: () => void;
  onNewScan: () => void;
}

type FilterCategory = 'ALL' | 'corrupt_files' | 'duplicate_files' | 'security_documents' | 'cryptocurrency' | 'privacy_data' | 'government_ids' | 'file_integrity';

const categoryLabels: Record<FilterCategory, string> = {
  ALL: 'All Results',
  corrupt_files: 'Corrupt Files',
  duplicate_files: 'Duplicates',
  security_documents: 'Security Docs',
  cryptocurrency: 'Cryptocurrency',
  privacy_data: 'Privacy Data',
  government_ids: 'Government IDs',
  file_integrity: 'File Integrity',
};

const getRiskColor = (riskScore: number): string => {
  if (riskScore >= 0.8) return 'text-red-600';
  if (riskScore >= 0.6) return 'text-orange-500';
  if (riskScore >= 0.3) return 'text-yellow-600';
  return 'text-green-600';
};

const getRiskLabel = (riskScore: number): string => {
  if (riskScore >= 0.8) return 'Critical';
  if (riskScore >= 0.6) return 'High';
  if (riskScore >= 0.3) return 'Medium';
  return 'Low';
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString();
  } catch {
    return 'Unknown';
  }
};

export default function ResultsDisplayScreen({
  results,
  selectedFiles,
  onFileSelectionChange,
  isScanning,
  onBack,
  onNewScan,
}: ResultsDisplayScreenProps) {
  const [activeFilter, setActiveFilter] = useState<FilterCategory>('ALL');

  // Get available categories from results
  const availableCategories = useMemo(() => {
    const categories = new Set<string>();
    results.forEach(result => {
      if (result.detection_category) {
        categories.add(result.detection_category);
      }
    });
    return Array.from(categories);
  }, [results]);

  // Filter results based on active filter
  const filteredResults = useMemo(() => {
    if (activeFilter === 'ALL') return results;
    return results.filter(result => result.detection_category === activeFilter);
  }, [results, activeFilter]);

  const handleFileToggle = (filePath: string) => {
    const newSelection = selectedFiles.includes(filePath)
      ? selectedFiles.filter(f => f !== filePath)
      : [...selectedFiles, filePath];
    onFileSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    const visibleFiles = filteredResults.map(r => r.file_path);
    const allSelected = visibleFiles.every(file => selectedFiles.includes(file));
    
    if (allSelected) {
      // Deselect all visible files
      const newSelection = selectedFiles.filter(file => !visibleFiles.includes(file));
      onFileSelectionChange(newSelection);
    } else {
      // Select all visible files (maintain existing selections from other filters)
      const newSelection = [...new Set([...selectedFiles, ...visibleFiles])];
      onFileSelectionChange(newSelection);
    }
  };

  const handleSaveLog = () => {
    // TODO: Implement save log functionality
    console.log('Save log clicked');
  };

  const handleFileAction = (action: string, files: string[]) => {
    console.log(`File action: ${action} on ${files.length} files`);
    // TODO: Implement file actions
    switch (action) {
      case 'copy':
        console.log('Copying files:', files);
        break;
      case 'move':
        console.log('Moving files:', files);
        break;
      case 'delete':
        console.log('Deleting files:', files);
        // Remove deleted files from results
        const remainingFiles = selectedFiles.filter(f => !files.includes(f));
        onFileSelectionChange(remainingFiles);
        break;
      case 'open':
        console.log('Opening file:', files[0]);
        break;
      case 'details':
        console.log('Showing details for files:', files);
        break;
      case 'compare':
        console.log('Comparing files:', files);
        break;
      case 'open-with':
        console.log('Open with for file:', files[0]);
        break;
      case 'save-log':
        handleSaveLog();
        break;
      default:
        console.log('Unknown action:', action);
    }
  };

  const visibleSelectedCount = filteredResults.filter(r => selectedFiles.includes(r.file_path)).length;
  const allVisibleSelected = filteredResults.length > 0 && visibleSelectedCount === filteredResults.length;

  if (isScanning) {
    return (
      <div className="results-screen">
        <div className="screen-header">
          <button className="back-button" onClick={onBack}>
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>
          <h1 className="screen-title">Scanning in Progress</h1>
        </div>

        <div className="scanning-state">
          <div className="scanning-animation">
            <div className="scanning-spinner" />
            <Shield className="w-12 h-12 text-blue-600" />
          </div>
          <h2>Scanning Your Files</h2>
          <p>Please wait while we analyze your selected folders for privacy risks...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="results-screen">
      <div className="screen-header">
        <button className="back-button" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
          Back
        </button>
        <h1 className="screen-title">Scan Results</h1>
        <button className="new-scan-button" onClick={onNewScan}>
          <Scan className="w-4 h-4" />
          New Scan
        </button>
      </div>

      <div className="results-content">
        {/* Results Summary */}
        <div className="results-summary">
          <div className="summary-stats">
            <div className="stat-item">
              <span className="stat-number">{results.length}</span>
              <span className="stat-label">Files Found</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{selectedFiles.length}</span>
              <span className="stat-label">Selected</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{results.filter(r => r.risk_score >= 0.6).length}</span>
              <span className="stat-label">High Risk</span>
            </div>
          </div>

          <div className="summary-actions">
            <button className="save-log-button" onClick={handleSaveLog}>
              <Download className="w-4 h-4" />
              Save Log
            </button>
          </div>
        </div>

        {/* Category Filters */}
        <div className="category-filters">
          <button
            className={`filter-button ${activeFilter === 'ALL' ? 'active' : ''}`}
            onClick={() => setActiveFilter('ALL')}
          >
            <Filter className="w-4 h-4" />
            All Results ({results.length})
          </button>
          
          {availableCategories.map(category => {
            const count = results.filter(r => r.detection_category === category).length;
            return (
              <button
                key={category}
                className={`filter-button ${activeFilter === category ? 'active' : ''}`}
                onClick={() => setActiveFilter(category as FilterCategory)}
              >
                {categoryLabels[category as FilterCategory] || category} ({count})
              </button>
            );
          })}
        </div>

        {/* Results Table */}
        <div className="results-table-container">
          <div className="table-header">
            <div className="table-controls">
              <button
                className="select-all-button"
                onClick={handleSelectAll}
              >
                {allVisibleSelected ? (
                  <CheckSquare className="w-4 h-4" />
                ) : (
                  <Square className="w-4 h-4" />
                )}
                {allVisibleSelected ? 'Deselect All' : 'Select All'} Visible
              </button>
              
              <span className="selection-counter">
                {visibleSelectedCount} of {filteredResults.length} selected
              </span>
            </div>
          </div>

          {filteredResults.length === 0 ? (
            <div className="empty-results">
              <FileX className="w-12 h-12 text-gray-400" />
              <h3>No Results Found</h3>
              <p>No files match the current filter criteria.</p>
            </div>
          ) : (
            <div className="results-table">
              <div className="table-header-row">
                <div className="table-cell checkbox-cell">
                  <Checkbox.Root
                    className="checkbox-root"
                    checked={allVisibleSelected}
                    onCheckedChange={handleSelectAll}
                  >
                    <Checkbox.Indicator className="checkbox-indicator">
                      <CheckIcon className="w-4 h-4" />
                    </Checkbox.Indicator>
                  </Checkbox.Root>
                </div>
                <div className="table-cell name-cell">File Name</div>
                <div className="table-cell type-cell">Type</div>
                <div className="table-cell size-cell">Size</div>
                <div className="table-cell date-cell">Modified</div>
                <div className="table-cell category-cell">Category</div>
                <div className="table-cell risk-cell">Risk</div>
              </div>

              {filteredResults.map((result, index) => {
                const isSelected = selectedFiles.includes(result.file_path);
                
                return (
                  <div
                    key={`${result.file_path}-${index}`}
                    className={`table-row ${isSelected ? 'selected' : ''}`}
                    onClick={() => handleFileToggle(result.file_path)}
                  >
                    <div className="table-cell checkbox-cell">
                      <Checkbox.Root
                        className="checkbox-root"
                        checked={isSelected}
                        onCheckedChange={() => handleFileToggle(result.file_path)}
                      >
                        <Checkbox.Indicator className="checkbox-indicator">
                          <CheckIcon className="w-4 h-4" />
                        </Checkbox.Indicator>
                      </Checkbox.Root>
                    </div>
                    <div className="table-cell name-cell" title={result.file_path}>
                      {result.file_name}
                    </div>
                    <div className="table-cell type-cell">
                      {result.file_type.toUpperCase()}
                    </div>
                    <div className="table-cell size-cell">
                      {formatFileSize(result.file_size)}
                    </div>
                    <div className="table-cell date-cell">
                      {formatDate(result.date_modified)}
                    </div>
                    <div className="table-cell category-cell">
                      {result.detection_category}
                    </div>
                    <div className="table-cell risk-cell">
                      <span className={`risk-badge ${getRiskColor(result.risk_score)}`}>
                        <AlertTriangle className="w-3 h-3" />
                        {getRiskLabel(result.risk_score)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* File Actions Panel */}
        <FileActionsPanel
          selectedFiles={selectedFiles}
          onAction={handleFileAction}
          disabled={isScanning}
        />
      </div>
    </div>
  );
}
