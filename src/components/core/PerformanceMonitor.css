/* Performance Monitor Component Styling */

.performance-monitor {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin: 24px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.performance-monitor.mobile {
    padding: 16px;
    margin: 16px 0;
}

/* Header */
.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f1f5f9;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-icon {
    color: #3b82f6;
    width: 24px;
    height: 24px;
}

.monitor-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

.monitor-header p {
    margin: 2px 0 0 0;
    color: #64748b;
    font-size: 0.875rem;
}

.monitor-controls {
    display: flex;
    gap: 12px;
}

.monitor-toggle,
.details-toggle {
    padding: 8px 16px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #475569;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.monitor-toggle:hover,
.details-toggle:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.monitor-toggle.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

/* Backend Metrics Section */
.backend-metrics-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 2px solid #f1f5f9;
}

.backend-metrics-section h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.backend-metrics-section h4::before {
    content: "🔧";
    font-size: 1.2rem;
}

.metric-card {
    background: #f8fafc;
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #e2e8f0;
    transition: all 0.2s ease;
}

.metric-card.good {
    border-left-color: #10b981;
    background: #f0fdf4;
}

.metric-card.warning {
    border-left-color: #f59e0b;
    background: #fffbeb;
}

.metric-card.critical {
    border-left-color: #ef4444;
    background: #fef2f2;
}

.metric-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.metric-icon {
    color: #64748b;
}

.metric-card.good .metric-icon {
    color: #10b981;
}

.metric-card.warning .metric-icon {
    color: #f59e0b;
}

.metric-card.critical .metric-icon {
    color: #ef4444;
}

.metric-title {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
}

.metric-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-status.good {
    color: #10b981;
}

.metric-status.warning {
    color: #f59e0b;
}

.metric-status.critical {
    color: #ef4444;
}

/* Suggestions Section */
.suggestions-section {
    margin-bottom: 24px;
}

.suggestions-section h4 {
    margin: 0 0 16px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.suggestion {
    background: #f8fafc;
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid #e2e8f0;
}

.suggestion.critical {
    border-left-color: #ef4444;
    background: #fef2f2;
}

.suggestion.warning {
    border-left-color: #f59e0b;
    background: #fffbeb;
}

.suggestion.info {
    border-left-color: #3b82f6;
    background: #eff6ff;
}

.suggestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.suggestion-title {
    font-weight: 600;
    color: #1e293b;
}

.suggestion-impact {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.suggestion-impact.high {
    background: #fee2e2;
    color: #dc2626;
}

.suggestion-impact.medium {
    background: #fef3c7;
    color: #d97706;
}

.suggestion-impact.low {
    background: #dbeafe;
    color: #2563eb;
}

.suggestion-description {
    color: #64748b;
    margin-bottom: 8px;
    line-height: 1.5;
}

.suggestion-action {
    color: #374151;
    font-size: 0.875rem;
}

.suggestion-action strong {
    color: #1e293b;
}

/* Details Section */
.details-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 16px;
}

.details-section h4 {
    margin: 0 0 16px 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

.detail-item span:first-child {
    font-weight: 500;
    color: #374151;
}

.detail-item span:last-child {
    color: #1e293b;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .performance-monitor {
        padding: 16px;
    }
    
    .monitor-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .monitor-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .suggestion-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

@media (max-width: 480px) {
    .performance-monitor {
        padding: 12px;
        margin: 12px 0;
    }
    
    .monitor-header h3 {
        font-size: 1.125rem;
    }
    
    .metric-card {
        padding: 12px;
    }
    
    .metric-value {
        font-size: 1.25rem;
    }
    
    .suggestion {
        padding: 12px;
    }
    
    .details-section {
        padding: 12px;
    }
}

/* Animation for metric updates */
@keyframes metricUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.metric-value {
    animation: metricUpdate 0.3s ease-in-out;
}

/* Loading state */
.metric-card.loading {
    opacity: 0.6;
}

.metric-card.loading .metric-value {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    height: 1.5rem;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .performance-monitor {
        border: 2px solid ButtonText;
    }
    
    .metric-card {
        border: 1px solid ButtonText;
    }
    
    .monitor-toggle,
    .details-toggle {
        border: 2px solid ButtonText;
    }
    
    .monitor-toggle.active {
        background: Highlight;
        color: HighlightText;
        border-color: HighlightText;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .metric-card,
    .monitor-toggle,
    .details-toggle,
    .suggestion {
        transition: none;
    }
    
    .metric-value {
        animation: none;
    }
    
    .metric-card.loading .metric-value {
        animation: none;
        background: #e0e0e0;
    }
}
