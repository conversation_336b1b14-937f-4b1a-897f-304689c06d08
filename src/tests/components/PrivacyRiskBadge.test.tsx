import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import React from 'react';

// Mock the PrivacyRiskBadge component since it's defined inline in App.tsx
// In a real implementation, this would be extracted to its own file
const PrivacyRiskBadge: React.FC<{ filePath: string }> = ({ filePath }) => {
  const [riskIndicator, setRiskIndicator] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  React.useEffect(() => {
    if (filePath) {
      assessPrivacyRisk(filePath);
    }
  }, [filePath]);

  const assessPrivacyRisk = async (path: string) => {
    setIsLoading(true);
    try {
      // Mock invoke call
      const result = await (window as any).mockInvoke('quick_privacy_assessment', { filePath: path });
      setRiskIndicator(result);
    } catch (error) {
      console.error('Failed to assess privacy risk:', error);
      setRiskIndicator(null);
    } finally {
      setIsLoading(false);
    }
  };

  const getRiskColor = (level: number) => {
    switch (level) {
      case 0: return 'text-green-600';
      case 1: return 'text-yellow-600';
      case 2: return 'text-orange-600';
      case 3: return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getRiskText = (level: number) => {
    switch (level) {
      case 0: return 'No Risk';
      case 1: return 'Low Risk';
      case 2: return 'Medium Risk';
      case 3: return 'High Risk';
      default: return 'Unknown';
    }
  };

  if (isLoading) {
    return <span className="text-gray-500">Assessing...</span>;
  }

  if (!riskIndicator) {
    return <span className="text-gray-400">No assessment</span>;
  }

  return (
    <span className={`font-medium ${getRiskColor(riskIndicator.risk_level)}`}>
      {getRiskText(riskIndicator.risk_level)}
      {riskIndicator.confidence && (
        <span className="text-xs text-gray-500 ml-1">
          ({Math.round(riskIndicator.confidence * 100)}%)
        </span>
      )}
    </span>
  );
};

describe('PrivacyRiskBadge Component', () => {
  beforeEach(() => {
    // Setup mock invoke function
    (window as any).mockInvoke = vi.fn();
  });

  it('renders loading state initially', async () => {
    (window as any).mockInvoke.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<PrivacyRiskBadge filePath="test.txt" />);
    
    expect(screen.getByText(/Assessing.../i)).toBeInTheDocument();
  });

  it('displays no risk assessment for empty path', () => {
    render(<PrivacyRiskBadge filePath="" />);
    
    expect(screen.getByText(/No assessment/i)).toBeInTheDocument();
  });

  it('displays no risk level correctly', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 0,
      risk_description: 'No privacy risks detected',
      confidence: 0.95,
      processing_time_ms: 25
    });

    render(<PrivacyRiskBadge filePath="clean_file.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/No Risk/i)).toBeInTheDocument();
      expect(screen.getByText(/95%/i)).toBeInTheDocument();
    });
  });

  it('displays low risk level correctly', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 1,
      risk_description: 'Low privacy risk detected',
      confidence: 0.8,
      processing_time_ms: 30
    });

    render(<PrivacyRiskBadge filePath="low_risk_file.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/Low Risk/i)).toBeInTheDocument();
      expect(screen.getByText(/80%/i)).toBeInTheDocument();
    });
  });

  it('displays medium risk level correctly', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 2,
      risk_description: 'Medium privacy risk detected',
      confidence: 0.85,
      processing_time_ms: 40
    });

    render(<PrivacyRiskBadge filePath="medium_risk_file.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/Medium Risk/i)).toBeInTheDocument();
      expect(screen.getByText(/85%/i)).toBeInTheDocument();
    });
  });

  it('displays high risk level correctly', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 3,
      risk_description: 'High privacy risk detected',
      confidence: 0.92,
      processing_time_ms: 45
    });

    render(<PrivacyRiskBadge filePath="high_risk_file.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/High Risk/i)).toBeInTheDocument();
      expect(screen.getByText(/92%/i)).toBeInTheDocument();
    });
  });

  it('handles assessment errors gracefully', async () => {
    (window as any).mockInvoke.mockRejectedValue(new Error('Assessment failed'));

    render(<PrivacyRiskBadge filePath="error_file.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/No assessment/i)).toBeInTheDocument();
    });
  });

  it('updates when file path changes', async () => {
    (window as any).mockInvoke
      .mockResolvedValueOnce({
        risk_level: 1,
        risk_description: 'Low risk',
        confidence: 0.7,
        processing_time_ms: 30
      })
      .mockResolvedValueOnce({
        risk_level: 3,
        risk_description: 'High risk',
        confidence: 0.9,
        processing_time_ms: 35
      });

    const { rerender } = render(<PrivacyRiskBadge filePath="file1.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/Low Risk/i)).toBeInTheDocument();
    });

    rerender(<PrivacyRiskBadge filePath="file2.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/High Risk/i)).toBeInTheDocument();
    });
  });

  it('displays confidence percentage correctly', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 2,
      risk_description: 'Medium risk',
      confidence: 0.756, // Should round to 76%
      processing_time_ms: 40
    });

    render(<PrivacyRiskBadge filePath="test.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/76%/i)).toBeInTheDocument();
    });
  });

  it('handles missing confidence gracefully', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 1,
      risk_description: 'Low risk',
      // confidence is missing
      processing_time_ms: 30
    });

    render(<PrivacyRiskBadge filePath="test.txt" />);
    
    await waitFor(() => {
      expect(screen.getByText(/Low Risk/i)).toBeInTheDocument();
      expect(screen.queryByText(/%/)).not.toBeInTheDocument();
    });
  });

  it('applies correct CSS classes for risk levels', async () => {
    const riskLevels = [
      { level: 0, expectedClass: 'text-green-600' },
      { level: 1, expectedClass: 'text-yellow-600' },
      { level: 2, expectedClass: 'text-orange-600' },
      { level: 3, expectedClass: 'text-red-600' }
    ];

    for (const { level, expectedClass } of riskLevels) {
      (window as any).mockInvoke.mockResolvedValue({
        risk_level: level,
        risk_description: `Risk level ${level}`,
        confidence: 0.8,
        processing_time_ms: 30
      });

      const { unmount } = render(<PrivacyRiskBadge filePath={`test_${level}.txt`} />);
      
      await waitFor(() => {
        const riskElement = screen.getByText(new RegExp(`${level === 0 ? 'No' : level === 1 ? 'Low' : level === 2 ? 'Medium' : 'High'} Risk`, 'i'));
        expect(riskElement).toHaveClass(expectedClass);
      });

      unmount();
    }
  });

  it('calls assessment API with correct parameters', async () => {
    (window as any).mockInvoke.mockResolvedValue({
      risk_level: 1,
      risk_description: 'Low risk',
      confidence: 0.8,
      processing_time_ms: 30
    });

    render(<PrivacyRiskBadge filePath="specific_file.txt" />);
    
    await waitFor(() => {
      expect((window as any).mockInvoke).toHaveBeenCalledWith(
        'quick_privacy_assessment',
        { filePath: 'specific_file.txt' }
      );
    });
  });

  it('does not call assessment API for empty file path', () => {
    render(<PrivacyRiskBadge filePath="" />);
    
    expect((window as any).mockInvoke).not.toHaveBeenCalled();
  });

  it('shows loading state during assessment', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise(resolve => {
      resolvePromise = resolve;
    });

    (window as any).mockInvoke.mockReturnValue(promise);

    render(<PrivacyRiskBadge filePath="test.txt" />);
    
    expect(screen.getByText(/Assessing.../i)).toBeInTheDocument();
    
    // Resolve the promise
    resolvePromise!({
      risk_level: 1,
      risk_description: 'Low risk',
      confidence: 0.8,
      processing_time_ms: 30
    });

    await waitFor(() => {
      expect(screen.queryByText(/Assessing.../i)).not.toBeInTheDocument();
      expect(screen.getByText(/Low Risk/i)).toBeInTheDocument();
    });
  });
});
