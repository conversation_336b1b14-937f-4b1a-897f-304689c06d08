# Windows Environment Verification Script
Write-Host "=== PrivacyAI Windows Environment Verification ===" -ForegroundColor Green

# Check Rust installation
Write-Host "`n1. Checking Rust installation..." -ForegroundColor Yellow
if (Get-Command rustc -ErrorAction SilentlyContinue) {
    rustc --version
    rustup --version
} else {
    Write-Host "ERROR: Rust not found. Install from https://rustup.rs/" -ForegroundColor Red
    exit 1
}

# Check current toolchain
Write-Host "`n2. Current Rust toolchain:" -ForegroundColor Yellow
rustup show

# Check Visual Studio Build Tools
Write-Host "`n3. Checking Visual Studio Build Tools..." -ForegroundColor Yellow
$vsWhere = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\Installer\vswhere.exe"
if (Test-Path $vsWhere) {
    & $vsWhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64
} else {
    Write-Host "WARNING: Visual Studio Build Tools not detected" -ForegroundColor Yellow
}

# Check Node.js
Write-Host "`n4. Checking Node.js..." -ForegroundColor Yellow
if (Get-Command node -ErrorAction SilentlyContinue) {
    node --version
    npm --version
} else {
    Write-Host "ERROR: Node.js not found" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== Environment Check Complete ===" -ForegroundColor Green