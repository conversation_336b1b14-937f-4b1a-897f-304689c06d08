use std::path::{<PERSON>, PathBuf};
use serde::{Serialize, Deserialize};
use thiserror::Error;

use crate::core::error::{FileManagerError, Result as CoreResult};
use crate::security::{pattern_matcher::Pat<PERSON><PERSON>atcher, sensitive_data_detector::SensitiveDataDetector};
use super::ocr_engine::{OCREngine, OCRResult, OCRError};

/// Main privacy detection orchestrator
/// 
/// This is the central component that coordinates OCR, pattern matching,
/// and AI-based privacy detection across different file types.
#[derive(Debug)]
pub struct PrivacyDetector {
    /// Configuration options for privacy detection
    pub options: PrivacyDetectionOptions,
    /// OCR engine for text extraction
    pub ocr_engine: Option<OCREngine>,
    /// Pattern matcher for structured data detection
    pub pattern_matcher: PatternMatcher,
    /// Sensitive data detector for general privacy patterns
    pub sensitive_data_detector: SensitiveDataDetector,
}

/// Configuration options for privacy detection
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct PrivacyDetectionOptions {
    /// Enable OCR text extraction
    pub enable_ocr: bool,
    /// Enable pattern matching for structured data
    pub enable_pattern_matching: bool,
    /// Enable AI-based visual detection
    pub enable_ai_detection: bool,
    /// Minimum confidence threshold for detections
    pub confidence_threshold: f32,
    /// Maximum file size to process (in bytes)
    pub max_file_size: usize,
    /// Supported file extensions
    pub supported_extensions: Vec<String>,
}

/// Result of privacy scanning for a single file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacyScanResult {
    /// Path of the scanned file
    pub file_path: PathBuf,
    /// Overall privacy risk score (0.0-1.0)
    pub risk_score: f32,
    /// List of privacy findings
    pub findings: Vec<PrivacyFinding>,
    /// OCR results if text extraction was performed
    pub ocr_result: Option<OCRResult>,
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// File size in bytes
    pub file_size: u64,
    /// Any errors encountered during processing
    pub errors: Vec<String>,
}

/// Individual privacy finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacyFinding {
    /// Type of privacy data detected
    pub data_type: PrivacyDataType,
    /// Confidence score (0.0-1.0)
    pub confidence: f32,
    /// Location in the file/text where found
    pub location: Option<FindingLocation>,
    /// Context or sample of the detected data (sanitized)
    pub context: Option<String>,
    /// Severity level
    pub severity: PrivacySeverity,
    /// Detection method used
    pub detection_method: DetectionMethod,
}

/// Types of privacy data that can be detected
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum PrivacyDataType {
    /// Social Security Number
    SocialSecurityNumber,
    /// Credit card number
    CreditCardNumber,
    /// Phone number
    PhoneNumber,
    /// Email address
    EmailAddress,
    /// Driver's license number
    DriversLicense,
    /// Passport number
    PassportNumber,
    /// Bank account number
    BankAccountNumber,
    /// Personal identification document
    IdentificationDocument,
    /// Medical record number
    MedicalRecordNumber,
    /// Custom pattern match
    Custom(String),
}

/// Severity levels for privacy findings
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum PrivacySeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Detection methods used to find privacy data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DetectionMethod {
    /// Pattern matching (regex-based)
    PatternMatching,
    /// OCR text extraction
    OCRExtraction,
    /// AI visual detection
    AIVisualDetection,
    /// Metadata analysis
    MetadataAnalysis,
}

/// Location information for a finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FindingLocation {
    /// Line number (for text-based detection)
    pub line: Option<usize>,
    /// Column start position
    pub column_start: Option<usize>,
    /// Column end position
    pub column_end: Option<usize>,
    /// Page number (for PDF documents)
    pub page: Option<usize>,
}

/// Privacy detection specific errors
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum PrivacyDetectionError {
    #[error("File not supported: {path}")]
    UnsupportedFile { path: String },
    
    #[error("OCR processing failed: {error}")]
    OCRFailed { error: String },
    
    #[error("Pattern matching failed: {error}")]
    PatternMatchingFailed { error: String },
    
    #[error("AI detection failed: {error}")]
    AIDetectionFailed { error: String },
    
    #[error("File access error: {error}")]
    FileAccessError { error: String },
    
    #[error("Configuration error: {error}")]
    ConfigurationError { error: String },
}

impl Default for PrivacyDetectionOptions {
    fn default() -> Self {
        Self {
            enable_ocr: true,
            enable_pattern_matching: true,
            enable_ai_detection: true, // Enabled with lightweight models
            confidence_threshold: 0.7,
            max_file_size: 100 * 1024 * 1024, // 100MB
            supported_extensions: vec![
                "pdf".to_string(),
                "jpg".to_string(),
                "jpeg".to_string(),
                "png".to_string(),
                "bmp".to_string(),
                "tiff".to_string(),
                "txt".to_string(),
                "doc".to_string(),
                "docx".to_string(),
            ],
        }
    }
}

impl PrivacyDetector {
    /// Create a new privacy detector with default options
    pub fn new() -> CoreResult<Self> {
        Self::with_options(PrivacyDetectionOptions::default())
    }
    
    /// Create a new privacy detector with custom options
    pub fn with_options(options: PrivacyDetectionOptions) -> CoreResult<Self> {
        let ocr_engine = if options.enable_ocr {
            Some(OCREngine::new())
        } else {
            None
        };

        // Only initialize pattern matcher if pattern matching is enabled
        let pattern_matcher = if options.enable_pattern_matching {
            PatternMatcher::new()
                .map_err(|e| FileManagerError::Config {
                    message: format!("Failed to initialize pattern matcher: {}", e)
                })?
        } else {
            // Create a minimal pattern matcher that doesn't compile regex patterns
            PatternMatcher::minimal()
                .map_err(|e| FileManagerError::Config {
                    message: format!("Failed to initialize minimal pattern matcher: {}", e)
                })?
        };

        // Only initialize sensitive data detector if pattern matching is enabled
        let sensitive_data_detector = if options.enable_pattern_matching {
            SensitiveDataDetector::new()
                .map_err(|e| FileManagerError::Config {
                    message: format!("Failed to initialize sensitive data detector: {}", e)
                })?
        } else {
            // Create a minimal detector that doesn't use pattern matching
            SensitiveDataDetector::minimal()
                .map_err(|e| FileManagerError::Config {
                    message: format!("Failed to initialize minimal sensitive data detector: {}", e)
                })?
        };

        Ok(Self {
            options,
            ocr_engine,
            pattern_matcher,
            sensitive_data_detector,
        })
    }
    
    /// Scan a single file for privacy content
    pub async fn scan_file<P: AsRef<Path>>(&self, file_path: P) -> Result<PrivacyScanResult, PrivacyDetectionError> {
        let path = file_path.as_ref();
        let start_time = std::time::Instant::now();
        
        // Initialize result
        let mut result = PrivacyScanResult {
            file_path: path.to_path_buf(),
            risk_score: 0.0,
            findings: Vec::new(),
            ocr_result: None,
            processing_time_ms: 0,
            file_size: 0,
            errors: Vec::new(),
        };
        
        // Get file metadata
        match std::fs::metadata(path) {
            Ok(metadata) => {
                result.file_size = metadata.len();
                
                // Check file size limit
                if metadata.len() as usize > self.options.max_file_size {
                    result.errors.push(format!("File too large: {} bytes", metadata.len()));
                    result.processing_time_ms = start_time.elapsed().as_millis() as u64;
                    return Ok(result);
                }
            }
            Err(e) => {
                return Err(PrivacyDetectionError::FileAccessError {
                    error: e.to_string(),
                });
            }
        }
        
        // Check if file type is supported
        if !self.is_supported_file(path) {
            return Err(PrivacyDetectionError::UnsupportedFile {
                path: path.to_string_lossy().to_string(),
            });
        }
        
        // Perform OCR if enabled and applicable
        if self.options.enable_ocr && self.should_use_ocr(path) {
            if let Some(ref ocr_engine) = self.ocr_engine {
                match self.extract_text_with_ocr(ocr_engine, path).await {
                    Ok(ocr_result) => {
                        result.ocr_result = Some(ocr_result.clone());
                        
                        // Perform pattern matching on OCR text
                        if self.options.enable_pattern_matching {
                            let ocr_findings = self.analyze_text_for_privacy(&ocr_result.text);
                            result.findings.extend(ocr_findings);
                        }
                    }
                    Err(e) => {
                        result.errors.push(format!("OCR failed: {}", e));
                    }
                }
            }
        }
        
        // Perform direct text analysis for text files
        if self.is_text_file(path) && self.options.enable_pattern_matching {
            match std::fs::read_to_string(path) {
                Ok(content) => {
                    let text_findings = self.analyze_text_for_privacy(&content);
                    result.findings.extend(text_findings);
                }
                Err(e) => {
                    result.errors.push(format!("Failed to read text file: {}", e));
                }
            }
        }
        
        // Calculate overall risk score
        result.risk_score = self.calculate_risk_score(&result.findings);
        
        // Record processing time
        result.processing_time_ms = start_time.elapsed().as_millis() as u64;
        
        Ok(result)
    }
    
    /// Scan multiple files in a directory recursively
    pub async fn scan_directory<P: AsRef<Path>>(&self, dir_path: P) -> Vec<PrivacyScanResult> {
        let mut results = Vec::new();

        // Collect all files first (non-async)
        let mut file_paths = Vec::new();
        self.collect_files_recursive(dir_path.as_ref(), &mut file_paths);

        // Then scan each file (async)
        for file_path in file_paths {
            match self.scan_file(&file_path).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    // Create error result
                    results.push(PrivacyScanResult {
                        file_path,
                        risk_score: 0.0,
                        findings: Vec::new(),
                        ocr_result: None,
                        processing_time_ms: 0,
                        file_size: 0,
                        errors: vec![e.to_string()],
                    });
                }
            }
        }

        results
    }

    /// Recursively collect all files in directory and subdirectories
    fn collect_files_recursive(&self, dir_path: &Path, file_paths: &mut Vec<PathBuf>) {
        if let Ok(entries) = std::fs::read_dir(dir_path) {
            for entry in entries.flatten() {
                let path = entry.path();

                if path.is_file() {
                    file_paths.push(path);
                } else if path.is_dir() {
                    // Recursively collect from subdirectory
                    self.collect_files_recursive(&path, file_paths);
                }
            }
        }
    }

    /// Check if a file is supported for privacy scanning
    fn is_supported_file(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            self.options.supported_extensions.contains(&extension.to_lowercase())
        } else {
            false
        }
    }

    /// Check if OCR should be used for this file type
    fn should_use_ocr(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            matches!(extension.to_lowercase().as_str(),
                "pdf" | "jpg" | "jpeg" | "png" | "bmp" | "tiff" | "tif" | "webp")
        } else {
            false
        }
    }

    /// Check if file is a text file that can be read directly
    fn is_text_file(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            matches!(extension.to_lowercase().as_str(), "txt" | "md" | "csv" | "log")
        } else {
            false
        }
    }

    /// Extract text using OCR engine
    async fn extract_text_with_ocr(&self, ocr_engine: &OCREngine, path: &Path) -> Result<OCRResult, OCRError> {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            match extension.to_lowercase().as_str() {
                "pdf" => ocr_engine.extract_text_from_pdf(path).await,
                "jpg" | "jpeg" | "png" | "bmp" | "tiff" | "tif" | "webp" => {
                    ocr_engine.extract_text_from_image(path).await
                }
                _ => Err(OCRError::UnsupportedFormat {
                    format: extension.to_string()
                }),
            }
        } else {
            Err(OCRError::UnsupportedFormat {
                format: "unknown".to_string()
            })
        }
    }

    /// Analyze text content for privacy patterns
    fn analyze_text_for_privacy(&self, text: &str) -> Vec<PrivacyFinding> {
        let mut findings = Vec::new();

        // Use pattern matcher to find structured data
        let pattern_results = self.pattern_matcher.scan_content(text, "text_analysis");

        for detection in pattern_results {
            let privacy_type = match detection.data_type {
                crate::security::SensitiveDataType::SocialSecurityNumber => PrivacyDataType::SocialSecurityNumber,
                crate::security::SensitiveDataType::CreditCard => PrivacyDataType::CreditCardNumber,
                crate::security::SensitiveDataType::PhoneNumber => PrivacyDataType::PhoneNumber,
                crate::security::SensitiveDataType::Email => PrivacyDataType::EmailAddress,
                crate::security::SensitiveDataType::DriverLicense => PrivacyDataType::DriversLicense,
                crate::security::SensitiveDataType::PassportNumber => PrivacyDataType::PassportNumber,
                crate::security::SensitiveDataType::BankAccount => PrivacyDataType::BankAccountNumber,
                _ => PrivacyDataType::Custom(format!("{:?}", detection.data_type)),
            };

            let severity = match detection.confidence {
                c if c >= 0.9 => PrivacySeverity::Critical,
                c if c >= 0.8 => PrivacySeverity::High,
                c if c >= 0.6 => PrivacySeverity::Medium,
                _ => PrivacySeverity::Low,
            };

            findings.push(PrivacyFinding {
                data_type: privacy_type,
                confidence: detection.confidence,
                location: Some(FindingLocation {
                    line: detection.line_number,
                    column_start: detection.column_start,
                    column_end: detection.column_end,
                    page: None,
                }),
                context: Some(detection.context_hint),
                severity,
                detection_method: DetectionMethod::PatternMatching,
            });
        }

        findings
    }

    /// Calculate overall risk score based on findings
    fn calculate_risk_score(&self, findings: &[PrivacyFinding]) -> f32 {
        if findings.is_empty() {
            return 0.0;
        }

        let mut total_score = 0.0;
        let mut weight_sum = 0.0;

        for finding in findings {
            let severity_weight = match finding.severity {
                PrivacySeverity::Critical => 1.0,
                PrivacySeverity::High => 0.8,
                PrivacySeverity::Medium => 0.6,
                PrivacySeverity::Low => 0.4,
            };

            total_score += finding.confidence * severity_weight;
            weight_sum += severity_weight;
        }

        if weight_sum > 0.0 {
            (total_score / weight_sum).min(1.0)
        } else {
            0.0
        }
    }
}
