# PrivacyAI Test Files

This directory contains test files for validating the PrivacyAI privacy detection engine. These files contain various types of sensitive and non-sensitive data to ensure comprehensive testing of the detection algorithms.

## ⚠️ **IMPORTANT SECURITY NOTICE**

**ALL DATA IN THESE FILES IS FAKE AND FOR TESTING PURPOSES ONLY**

- SSN numbers are invalid test patterns
- Credit card numbers are test numbers that will not work
- Cryptocurrency addresses are either test network addresses or invalid
- API keys and passwords are fake examples
- Personal information is fictional

**DO NOT USE ANY OF THIS DATA FOR REAL TRANSACTIONS OR AUTHENTICATION**

## 📁 **Test File Descriptions**

### 1. `sensitive-data-samples.txt`
**Purpose**: Comprehensive collection of various sensitive data types
**Expected Risk**: HIGH (0.8 - 1.0)
**Contains**:
- Social Security Numbers
- Credit card numbers
- Bank account information
- Cryptocurrency addresses (Bitcoin, Ethereum, Cardano)
- World Mobile Token (WMT) addresses
- AdaHandles ($handle format)
- Unstoppable Domains (.crypto, .nft, etc.)
- API keys and tokens
- Personal identification numbers
- Medical and government IDs
- Private keys and seed phrases

### 2. `financial-document.txt`
**Purpose**: Simulates a comprehensive financial statement
**Expected Risk**: HIGH (0.9 - 1.0)
**Contains**:
- Complete personal financial profile
- Multiple bank accounts
- Credit card details
- Investment accounts
- Cryptocurrency holdings
- Loan information
- Insurance policies
- Emergency contact SSNs

### 3. `crypto-trading-log.txt`
**Purpose**: Tests cryptocurrency-specific detection
**Expected Risk**: HIGH (0.8 - 1.0)
**Contains**:
- Multiple cryptocurrency wallet addresses
- AdaHandles and Unstoppable Domains
- Seed phrases and private keys
- Exchange account information
- DeFi protocol interactions
- Staking information
- Trading transaction details

### 4. `clean-document.txt`
**Purpose**: Control test with no sensitive information
**Expected Risk**: LOW (0.0 - 0.1)
**Contains**:
- Software documentation
- Technical specifications
- General company information
- Product features
- Development roadmap
- Contact information (public)

### 5. `mixed-content.txt`
**Purpose**: Tests selective detection in mixed content
**Expected Risk**: MEDIUM-HIGH (0.6 - 0.8)
**Contains**:
- Mix of safe and sensitive information
- Company newsletter (safe)
- Employee records (sensitive)
- Meeting notes (mixed)
- Development credentials (sensitive)
- Customer data (mixed sensitivity levels)

## 🧪 **Testing Scenarios**

### Positive Tests (Should Detect)
- **High Risk Files**: `sensitive-data-samples.txt`, `financial-document.txt`, `crypto-trading-log.txt`
- **Expected Detections**: SSN, credit cards, crypto addresses, API keys, personal data

### Negative Tests (Should NOT Detect)
- **Low Risk File**: `clean-document.txt`
- **Expected Result**: No sensitive data detected, low risk score

### Mixed Content Tests
- **Mixed Risk File**: `mixed-content.txt`
- **Expected Behavior**: Detect only the sensitive portions, ignore safe content

### Cryptocurrency-Specific Tests
- **Bitcoin Addresses**: Various formats (legacy, SegWit, Bech32)
- **Ethereum Addresses**: Standard 0x format
- **Cardano Addresses**: Shelley era addresses
- **AdaHandles**: $handle format detection
- **Unstoppable Domains**: Multiple TLD support (.crypto, .nft, .blockchain, etc.)
- **World Mobile Token**: WMT-specific patterns

## 📊 **Expected Results Summary**

| File | Risk Level | Score Range | Primary Categories |
|------|------------|-------------|-------------------|
| `sensitive-data-samples.txt` | HIGH | 0.8 - 1.0 | financial, personal, crypto |
| `financial-document.txt` | HIGH | 0.9 - 1.0 | financial, personal |
| `crypto-trading-log.txt` | HIGH | 0.8 - 1.0 | crypto, financial |
| `clean-document.txt` | LOW | 0.0 - 0.1 | none |
| `mixed-content.txt` | MEDIUM-HIGH | 0.6 - 0.8 | financial, personal, credentials |

## 🔍 **How to Use These Test Files**

1. **Copy test-files directory** to a location accessible by PrivacyAI
2. **Run PrivacyAI scan** on the test-files directory
3. **Compare results** with expected outcomes above
4. **Verify detection accuracy** for each category
5. **Check for false positives** in clean-document.txt
6. **Validate cryptocurrency detection** especially for Cardano/WMT

## 🎯 **Validation Checklist**

### ✅ **Pattern Matching Tests**
- [ ] SSN detection (various formats)
- [ ] Credit card detection (Visa, MasterCard, Amex, Discover)
- [ ] Phone number detection
- [ ] Email address detection
- [ ] Bank account number detection

### ✅ **Cryptocurrency Tests**
- [ ] Bitcoin address detection (all formats)
- [ ] Ethereum address detection
- [ ] Cardano address detection
- [ ] AdaHandle detection ($handle)
- [ ] Unstoppable Domain detection (.crypto, .nft, etc.)
- [ ] World Mobile Token detection
- [ ] Seed phrase detection
- [ ] Private key detection

### ✅ **Risk Assessment Tests**
- [ ] High-risk files score 0.7+
- [ ] Low-risk files score <0.3
- [ ] Mixed content shows appropriate risk levels
- [ ] Risk categories are correctly identified

### ✅ **Performance Tests**
- [ ] Scan completes without errors
- [ ] Processing time is reasonable
- [ ] Memory usage is acceptable
- [ ] No false positives in clean document

## 🚨 **Security Reminder**

These test files contain patterns that look like real sensitive data but are completely fake. They should:

- **NEVER be used** for real transactions
- **NEVER be submitted** to real services
- **ONLY be used** for testing PrivacyAI
- **Be kept secure** to prevent confusion with real data

## 📝 **Adding New Test Cases**

When adding new test files:

1. **Use fake data only** - never real sensitive information
2. **Document expected results** in this README
3. **Include diverse patterns** for comprehensive testing
4. **Test edge cases** and boundary conditions
5. **Validate with multiple detection engines**

---

**Last Updated**: December 2023  
**Test File Version**: 1.0  
**Compatible with**: PrivacyAI v1.0+
