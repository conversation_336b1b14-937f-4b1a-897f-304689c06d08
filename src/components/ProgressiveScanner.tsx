import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import {
  Shield,
  CheckCircle,
  XCircle,
  Loader2,
  Eye,
  Search
} from 'lucide-react';

// Type definitions for progressive results
interface NanoResult {
  model_type: string;
  confidence: number;
  classification: string;
  processing_time_ms: number;
  privacy_detected: boolean;
  risk_level: number;
}

interface ProgressiveResult {
  stage: 'Preview' | 'Patterns' | 'Complete';
  nano_results?: NanoResult[];
  patterns_detected?: number;
  pattern_types?: string[];
  scan_result?: any;
  risk_level: number;
  processing_time_ms?: number;
  total_processing_time_ms?: number;
  confidence: number;
}

interface ProgressiveScannerProps {
  filePath: string;
  onComplete?: (results: ProgressiveResult[]) => void;
  onError?: (error: string) => void;
}

const ProgressiveScanner: React.FC<ProgressiveScannerProps> = ({ 
  filePath, 
  onComplete, 
  onError 
}) => {
  const [results, setResults] = useState<ProgressiveResult[]>([]);
  const [currentStage, setCurrentStage] = useState<string>('');
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string>('');
  const [totalTime, setTotalTime] = useState<number>(0);

  useEffect(() => {
    if (filePath) {
      startProgressiveScan();
    }
  }, [filePath]);

  const startProgressiveScan = async () => {
    setIsScanning(true);
    setError('');
    setResults([]);
    setCurrentStage('Initializing...');

    try {
      const progressiveResults = await invoke<ProgressiveResult[]>(
        'progressive_privacy_assessment',
        { filePath }
      );

      setResults(progressiveResults);
      setCurrentStage('Complete');
      
      // Calculate total time from the last result
      const lastResult = progressiveResults[progressiveResults.length - 1];
      if (lastResult && lastResult.total_processing_time_ms) {
        setTotalTime(lastResult.total_processing_time_ms);
      }

      if (onComplete) {
        onComplete(progressiveResults);
      }
    } catch (err) {
      const errorMessage = `Progressive scan failed: ${err}`;
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setIsScanning(false);
    }
  };

  const getRiskColor = (riskLevel: number) => {
    switch (riskLevel) {
      case 3: return 'text-red-600 bg-red-50 border-red-200';
      case 2: return 'text-orange-600 bg-orange-50 border-orange-200';
      case 1: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getRiskLabel = (riskLevel: number) => {
    switch (riskLevel) {
      case 3: return 'High Risk';
      case 2: return 'Medium Risk';
      case 1: return 'Low Risk';
      default: return 'No Risk';
    }
  };



  const renderPreviewStage = (result: ProgressiveResult) => (
    <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
      <div className="flex items-center space-x-2 mb-3">
        <Eye className="w-5 h-5 text-blue-600" />
        <h3 className="font-semibold text-blue-900">Stage 1: Instant Preview</h3>
        <span className="text-sm text-blue-600">
          {result.processing_time_ms}ms
        </span>
      </div>
      
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRiskColor(result.risk_level)}`}>
        {getRiskLabel(result.risk_level)} ({(result.confidence * 100).toFixed(0)}% confidence)
      </div>

      {result.nano_results && (
        <div className="mt-3 space-y-2">
          {result.nano_results.map((nano, index) => (
            <div key={index} className="text-sm bg-white rounded p-2 border">
              <div className="flex justify-between items-center">
                <span className="font-medium">{nano.model_type}</span>
                <span className="text-gray-500">{nano.processing_time_ms}ms</span>
              </div>
              <div className="text-gray-600">{nano.classification}</div>
              <div className="text-xs text-gray-500">
                Confidence: {(nano.confidence * 100).toFixed(0)}%
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderPatternsStage = (result: ProgressiveResult) => (
    <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
      <div className="flex items-center space-x-2 mb-3">
        <Search className="w-5 h-5 text-yellow-600" />
        <h3 className="font-semibold text-yellow-900">Stage 2: Pattern Matching</h3>
        <span className="text-sm text-yellow-600">
          {result.processing_time_ms}ms
        </span>
      </div>
      
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRiskColor(result.risk_level)}`}>
        {result.patterns_detected} patterns detected
      </div>

      {result.pattern_types && result.pattern_types.length > 0 && (
        <div className="mt-3">
          <div className="text-sm font-medium text-gray-700 mb-2">Pattern Types:</div>
          <div className="flex flex-wrap gap-2">
            {result.pattern_types.map((type, index) => (
              <span key={index} className="px-2 py-1 bg-white rounded text-xs border">
                {type}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderCompleteStage = (result: ProgressiveResult) => (
    <div className="border rounded-lg p-4 bg-green-50 border-green-200">
      <div className="flex items-center space-x-2 mb-3">
        <Shield className="w-5 h-5 text-green-600" />
        <h3 className="font-semibold text-green-900">Stage 3: Complete Analysis</h3>
        <span className="text-sm text-green-600">
          Total: {result.total_processing_time_ms}ms
        </span>
      </div>
      
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getRiskColor(result.risk_level)}`}>
        Final: {getRiskLabel(result.risk_level)} ({(result.confidence * 100).toFixed(0)}% confidence)
      </div>

      {result.scan_result && (
        <div className="mt-3 text-sm">
          <div className="bg-white rounded p-3 border">
            <div className="font-medium text-gray-700">Full Scan Results</div>
            <div className="text-gray-600 mt-1">
              Risk Score: {(result.scan_result.risk_score * 100).toFixed(1)}%
            </div>
            <div className="text-gray-600">
              Findings: {result.scan_result.findings?.length || 0}
            </div>
            <div className="text-gray-600">
              Processing Time: {result.scan_result.processing_time_ms}ms
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          Progressive Privacy Scan
        </h2>
        {isScanning && (
          <div className="flex items-center space-x-2 text-blue-600">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">{currentStage}</span>
          </div>
        )}
      </div>

      {/* File Path */}
      <div className="text-sm text-gray-600 bg-gray-50 rounded p-2">
        {filePath.split(/[/\\]/).pop()}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <XCircle className="w-5 h-5 text-red-600" />
              <span className="text-red-800 font-medium">Scan Error</span>
            </div>
            <button
              onClick={() => setError('')}
              className="text-red-600 hover:text-red-800 transition-colors"
              title="Dismiss error"
            >
              <XCircle className="w-4 h-4" />
            </button>
          </div>
          <p className="text-red-700 mt-2">{error}</p>
        </div>
      )}

      {/* Progressive Results */}
      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={index}>
            {result.stage === 'Preview' && renderPreviewStage(result)}
            {result.stage === 'Patterns' && renderPatternsStage(result)}
            {result.stage === 'Complete' && renderCompleteStage(result)}
          </div>
        ))}
      </div>

      {/* Summary */}
      {results.length > 0 && !isScanning && (
        <div className="bg-gray-50 rounded-lg p-4 border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="font-medium text-gray-900">Scan Complete</span>
            </div>
            <div className="text-sm text-gray-600">
              Total Time: {totalTime}ms
            </div>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Processed {results.length} stages successfully
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgressiveScanner;
