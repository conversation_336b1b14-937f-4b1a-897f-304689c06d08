# Test Document - Email Addresses and Contact Information

This document contains various email addresses and contact information for privacy detection testing.

## Standard Email Addresses

### Personal Emails
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Business Emails
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Various Domains
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

## Email Formats and Variations

### With Plus Signs
- <EMAIL>
- <EMAIL>
- <EMAIL>

### With Numbers
- <EMAIL>
- <EMAIL>
- <EMAIL>

### With Hyphens and Underscores
- <EMAIL>
- <EMAIL>
- <EMAIL>

### International Domains
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

## Contact Information Lists

### Employee Directory
1. <PERSON> - <EMAIL> - (*************
2. Jane Smith - <EMAIL> - (*************
3. Bob Johnson - <EMAIL> - (*************
4. Alice Brown - <EMAIL> - (*************

### Customer Database
Customer ID: 001
Name: Michael Wilson
Email: <EMAIL>
Phone: (*************
Address: 123 Main St, City, ST 12345

Customer ID: 002
Name: Sarah Davis
Email: <EMAIL>
Phone: (*************
Address: 456 Oak Ave, Town, ST 67890

## Email in Context

### Communication Records
From: <EMAIL>
To: <EMAIL>
Subject: Meeting Schedule
Date: 2024-01-15

Please contact <NAME_EMAIL> for further details.

### Mailing Lists
Newsletter subscribers:
- <EMAIL>
- <EMAIL>
- <EMAIL>
- <EMAIL>

### Support Tickets
Ticket #12345
Reporter: <EMAIL>
Assigned to: <EMAIL>
Status: Open

## Phone Numbers and Additional Contact Info

### Phone Number Formats
- (*************
- ************
- ************
- +1 (*************
- **************

### Mixed Contact Information
Name: David Miller
Email: <EMAIL>
Phone: (*************
Mobile: ************
Fax: (*************

### International Numbers
- +44 20 7946 0958 (UK)
- +33 1 42 86 83 26 (France)
- +49 30 12345678 (Germany)
- ****** 555-0199 (Canada)

## Web and Social Media

### Website URLs
- https://www.example.com
- http://testsite.org
- www.company.com
- business.net

### Social Media
- Twitter: @username
- LinkedIn: linkedin.com/in/profile
- Facebook: facebook.com/user.name

## Expected Detection Results

This document should trigger:
- MEDIUM RISK classification
- Multiple email address matches
- Phone number detection
- Privacy data type: EMAIL_ADDRESS, PHONE_NUMBER
- Confidence score: >85%

## Test Validation

The privacy detection system should:
1. Identify all email addresses (30+ instances)
2. Detect phone numbers in various formats
3. Classify document as MEDIUM RISK
4. Handle international formats
5. Recognize contact information patterns
6. Suggest appropriate data handling measures
