[package]
name = "tauri-appprivacy-ai"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_appprivacy_ai_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-dialog = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
thiserror = "2.0"
tokio = { version = "1.0", features = ["full"] }
regex = "1.10"
uuid = { version = "1.0", features = ["v4"] }
image = "0.25"
img_hash = "3.2"
blake3 = "1.5"
lopdf = "0.34"
pdf-extract = "0.7"  # For PDF text extraction
pdfium-render = "0.8"  # For PDF page rendering to images
rayon = "1.10"
walkdir = "2.5"
base64 = "0.22"
lru = "0.12"
zip = "2.1"
chrono = { version = "0.4", features = ["serde"] }
csv = "1.3"
ort = { version = "2.0.0-rc.10", features = ["copy-dylibs"] }
ndarray = "0.16"
async-trait = "0.1"
num_cpus = "1.16"

# Enhanced Pattern Recognition Dependencies (Phase 1 Implementation)
validator = { version = "0.16", features = ["derive"] }
once_cell = "1.19"
dashmap = "5.5"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter"] }

# Context Analysis Dependencies
unicode-segmentation = "1.10"
unicode-normalization = "0.1"

# User Data Privacy Controls Dependencies
md5 = "0.7"

# OCR Integration Dependencies
imageproc = "0.25"
# Real Tesseract OCR (optional, requires system installation)
leptess = { version = "0.14", optional = true }

# Document Type Detection Dependencies
nalgebra = "0.33"
rand = "0.8"

# Secure File Operations Dependencies
ring = "0.17"
tempfile = "3.8"
argon2 = "0.5"
camino = { version = "1.1", features = ["serde1"] }

[features]
default = []
real-ocr = ["leptess"]

