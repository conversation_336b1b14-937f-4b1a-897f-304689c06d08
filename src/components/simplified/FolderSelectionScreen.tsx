import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { ArrowLeft, Folder, FolderOpen, ChevronRight, ChevronDown, Play, Home, HardDrive } from 'lucide-react';
import * as Checkbox from '@radix-ui/react-checkbox';
import { CheckIcon } from 'lucide-react';
import type { FolderNode } from './types';

interface FolderSelectionScreenProps {
  onComplete: (selectedFolders: string[]) => void;
  onBack: () => void;
}

export default function FolderSelectionScreen({
  onComplete,
  onBack,
}: FolderSelectionScreenProps) {
  const [folderTree, setFolderTree] = useState<FolderNode[]>([]);
  const [selectedFolders, setSelectedFolders] = useState<Set<string>>(new Set());
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadInitialFolders();
  }, []);

  const loadInitialFolders = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get common user directories
      const commonPaths = [
        { name: 'Documents', path: await getDocumentsPath() },
        { name: 'Desktop', path: await getDesktopPath() },
        { name: 'Downloads', path: await getDownloadsPath() },
        { name: 'Pictures', path: await getPicturesPath() },
      ];

      const rootNodes: FolderNode[] = [];

      for (const { name, path } of commonPaths) {
        if (path) {
          const node: FolderNode = {
            id: path,
            name,
            path,
            isDirectory: true,
            selected: false,
            children: [],
            expanded: false,
          };
          rootNodes.push(node);
        }
      }

      setFolderTree(rootNodes);
    } catch (err) {
      setError(`Failed to load folders: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions to get system paths
  const getDocumentsPath = async (): Promise<string | null> => {
    try {
      // For Windows, use common paths
      if (typeof window !== 'undefined' && window.navigator.platform.includes('Win')) {
        return 'C:\\Users\\<USER>\\Documents';
      }
      return '/home/<USER>/Documents';
    } catch {
      return null;
    }
  };

  const getDesktopPath = async (): Promise<string | null> => {
    try {
      if (typeof window !== 'undefined' && window.navigator.platform.includes('Win')) {
        return 'C:\\Users\\<USER>\\Desktop';
      }
      return '/home/<USER>/Desktop';
    } catch {
      return null;
    }
  };

  const getDownloadsPath = async (): Promise<string | null> => {
    try {
      if (typeof window !== 'undefined' && window.navigator.platform.includes('Win')) {
        return 'C:\\Users\\<USER>\\Downloads';
      }
      return '/home/<USER>/Downloads';
    } catch {
      return null;
    }
  };

  const getPicturesPath = async (): Promise<string | null> => {
    try {
      if (typeof window !== 'undefined' && window.navigator.platform.includes('Win')) {
        return 'C:\\Users\\<USER>\\Pictures';
      }
      return '/home/<USER>/Pictures';
    } catch {
      return null;
    }
  };

  const handleFolderToggle = (folderId: string) => {
    setSelectedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const handleExpandToggle = async (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
        // Load children if not already loaded
        loadFolderChildren(folderId);
      }
      return newSet;
    });
  };

  const loadFolderChildren = async (folderPath: string) => {
    try {
      // For now, we'll use a simple approach since we don't have a specific Tauri command
      // In a real implementation, you'd call a Tauri command to list directory contents
      console.log(`Loading children for: ${folderPath}`);
      
      // Placeholder - in real implementation, you'd populate children
      setFolderTree(prev => 
        prev.map(node => 
          node.id === folderPath 
            ? { ...node, children: [] } // Would populate with actual children
            : node
        )
      );
    } catch (err) {
      console.error(`Failed to load children for ${folderPath}:`, err);
    }
  };

  const handleSelectFolder = async () => {
    try {
      const selectedPath = await invoke<string | null>('select_directory');
      if (selectedPath) {
        setSelectedFolders(prev => new Set([...prev, selectedPath]));
      }
    } catch (err) {
      setError(`Failed to select folder: ${err}`);
    }
  };

  const handleContinue = () => {
    onComplete(Array.from(selectedFolders));
  };

  const handleTestScan = () => {
    // Add a test folder for quick testing
    const testFolder = 'C:\\Users\\<USER>\\Documents';
    setSelectedFolders(new Set([testFolder]));
    onComplete([testFolder]);
  };

  const renderFolderNode = (node: FolderNode, level: number = 0) => {
    const isSelected = selectedFolders.has(node.id);
    const isExpanded = expandedFolders.has(node.id);
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div key={node.id} className="folder-node">
        <div 
          className={`folder-item ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft: `${level * 1.5 + 1}rem` }}
        >
          <div className="folder-item-content">
            <button
              className="expand-button"
              onClick={() => handleExpandToggle(node.id)}
              disabled={!node.isDirectory}
            >
              {node.isDirectory ? (
                isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )
              ) : (
                <div className="w-4 h-4" />
              )}
            </button>

            <div className="folder-icon">
              {node.isDirectory ? (
                isExpanded ? (
                  <FolderOpen className="w-5 h-5 text-blue-600" />
                ) : (
                  <Folder className="w-5 h-5 text-blue-600" />
                )
              ) : (
                <div className="w-5 h-5" />
              )}
            </div>

            <span className="folder-name">{node.name}</span>

            <Checkbox.Root
              className="checkbox-root"
              checked={isSelected}
              onCheckedChange={() => handleFolderToggle(node.id)}
            >
              <Checkbox.Indicator className="checkbox-indicator">
                <CheckIcon className="w-4 h-4" />
              </Checkbox.Indicator>
            </Checkbox.Root>
          </div>
        </div>

        {isExpanded && hasChildren && (
          <div className="folder-children">
            {node.children!.map(child => renderFolderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="folder-selection-screen">
        <div className="screen-header">
          <button className="back-button" onClick={onBack}>
            <ArrowLeft className="w-4 h-4" />
            Back
          </button>
          <h1 className="screen-title">Select Folders</h1>
        </div>
        <div className="loading-state">
          <div className="loading-spinner" />
          <p>Loading folders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="folder-selection-screen">
      <div className="screen-header">
        <button className="back-button" onClick={onBack}>
          <ArrowLeft className="w-4 h-4" />
          Back
        </button>
        <h1 className="screen-title">Select Folders to Scan</h1>
      </div>

      <div className="folder-selection-content">
        <div className="selection-info">
          <p className="selection-description">
            Choose the folders you want to scan. You can select multiple folders.
          </p>
          
          <div className="selection-actions">
            <button className="browse-button" onClick={handleSelectFolder}>
              <Folder className="w-4 h-4" />
              Browse for Folder
            </button>
            <button className="browse-button" onClick={handleTestScan} style={{ background: '#10b981' }}>
              <Play className="w-4 h-4" />
              Quick Test Scan
            </button>
            <span className="selection-count">
              {selectedFolders.size} folder{selectedFolders.size !== 1 ? 's' : ''} selected
            </span>
          </div>
        </div>

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        <div className="folder-tree">
          <div className="tree-header">
            <h3>Common Folders</h3>
          </div>
          <div className="tree-content">
            {folderTree.map(node => renderFolderNode(node))}
          </div>
        </div>

        <div className="selection-footer">
          <button
            className={`continue-button ${selectedFolders.size > 0 ? 'enabled' : 'disabled'}`}
            onClick={handleContinue}
            disabled={selectedFolders.size === 0}
          >
            <Play className="w-5 h-5" />
            Start Scanning ({selectedFolders.size} folder{selectedFolders.size !== 1 ? 's' : ''})
          </button>
          
          {selectedFolders.size === 0 && (
            <p className="continue-hint">
              Please select at least one folder to scan
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
